<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Ollama Local Chat</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto max-w-2xl p-4">
        <div id="connectionStatus" class="mb-4 p-3 bg-yellow-100 text-yellow-800 rounded" style="display:none;">
            Checking connection to Ollama...
        </div>
        <div class="bg-white rounded-lg shadow-md">
            <div id="chatArea" class="h-96 overflow-y-auto p-4">
                <!-- Chat messages will appear here -->
            </div>
            <div class="border-t p-4 flex">
                <input 
                    type="text" 
                    id="userInput" 
                    class="flex-grow p-2 border rounded-l-lg" 
                    placeholder="Type your message..."
                >
                <button 
                    id="sendButton" 
                    class="bg-blue-500 text-white p-2 rounded-r-lg"
                >
                    Send
                </button>
            </div>
        </div>
    </div>

    <script>
        const chatArea = document.getElementById('chatArea');
        const userInput = document.getElementById('userInput');
        const sendButton = document.getElementById('sendButton');
        const connectionStatus = document.getElementById('connectionStatus');

        // Local storage to maintain conversation context
        let conversationHistory = [];

        // Connection check
        async function checkOllamaConnection() {
            connectionStatus.style.display = 'block';
            connectionStatus.textContent = 'Checking connection to Ollama...';
            connectionStatus.className = 'mb-4 p-3 bg-yellow-100 text-yellow-800 rounded';

            try {
                const response = await fetch('http://localhost:11434/api/version', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const version = await response.json();
                    connectionStatus.textContent = `Connected to Ollama (Version: ${version.version})`;
                    connectionStatus.className = 'mb-4 p-3 bg-green-100 text-green-800 rounded';
                    return true;
                } else {
                    throw new Error('Connection failed');
                }
            } catch (error) {
                connectionStatus.textContent = 'Unable to connect to Ollama. Please check your setup.';
                connectionStatus.className = 'mb-4 p-3 bg-red-100 text-red-800 rounded';
                return false;
            }
        }

        async function sendMessage() {
            const message = userInput.value.trim();
            if (!message) return;

            // Check connection before sending
            const isConnected = await checkOllamaConnection();
            if (!isConnected) return;

            // Add user message to chat and history
            appendMessage('You', message, 'text-blue-600');
            conversationHistory.push({ role: 'user', content: message });

            // Clear input
            userInput.value = '';

            try {
                const response = await fetch('http://localhost:11434/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: 'gemma3:1b',  
                        messages: conversationHistory,
                        stream: true
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // Stream the response
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let fullModelResponse = '';
                let currentModelResponse = '';

                const modelMessageElement = appendMessage('Model', '', 'text-green-600');

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n').filter(line => line.trim() !== '');

                    lines.forEach(line => {
                        try {
                            const parsedChunk = JSON.parse(line);
                            if (parsedChunk.message) {
                                currentModelResponse += parsedChunk.message.content || '';
                                modelMessageElement.textContent = `Model: ${currentModelResponse}`;
                                chatArea.scrollTop = chatArea.scrollHeight;
                            }
                        } catch (parseError) {
                            console.error('Parsing error:', parseError);
                        }
                    });
                }

                // Add final response to conversation history
                conversationHistory.push({ role: 'assistant', content: currentModelResponse });

            } catch (error) {
                console.error('Error:', error);
                appendMessage('System', `Error communicating with the model: ${error.message}`, 'text-red-600');
            }
        }

        function appendMessage(sender, message, colorClass) {
            const messageElement = document.createElement('div');
            messageElement.innerHTML = `
                <strong class="${colorClass}">${sender}:</strong> 
                ${message}
            `;
            messageElement.className = 'mb-2 p-2 bg-gray-50 rounded';
            chatArea.appendChild(messageElement);
            chatArea.scrollTop = chatArea.scrollHeight;
            return messageElement;
        }

        // Initial connection check
        checkOllamaConnection();

        // Event Listeners
        sendButton.addEventListener('click', sendMessage);
        userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') sendMessage();
        });
    </script>
</body>
</html>