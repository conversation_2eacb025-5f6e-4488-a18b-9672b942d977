{"version": 3, "file": "luxon.js", "sources": ["../../src/errors.js", "../../src/impl/formats.js", "../../src/zone.js", "../../src/zones/systemZone.js", "../../src/zones/IANAZone.js", "../../src/impl/locale.js", "../../src/zones/fixedOffsetZone.js", "../../src/zones/invalidZone.js", "../../src/impl/zoneUtil.js", "../../src/impl/digits.js", "../../src/settings.js", "../../src/impl/invalid.js", "../../src/impl/conversions.js", "../../src/impl/util.js", "../../src/impl/english.js", "../../src/impl/formatter.js", "../../src/impl/regexParser.js", "../../src/duration.js", "../../src/interval.js", "../../src/info.js", "../../src/impl/diff.js", "../../src/impl/tokenParser.js", "../../src/datetime.js", "../../src/luxon.js"], "sourcesContent": ["// these aren't really private, but nor are they really useful to document\n\n/**\n * @private\n */\nclass LuxonError extends Error {}\n\n/**\n * @private\n */\nexport class InvalidDateTimeError extends LuxonError {\n  constructor(reason) {\n    super(`Invalid DateTime: ${reason.toMessage()}`);\n  }\n}\n\n/**\n * @private\n */\nexport class InvalidIntervalError extends LuxonError {\n  constructor(reason) {\n    super(`Invalid Interval: ${reason.toMessage()}`);\n  }\n}\n\n/**\n * @private\n */\nexport class InvalidDurationError extends LuxonError {\n  constructor(reason) {\n    super(`Invalid Duration: ${reason.toMessage()}`);\n  }\n}\n\n/**\n * @private\n */\nexport class ConflictingSpecificationError extends LuxonError {}\n\n/**\n * @private\n */\nexport class InvalidUnitError extends LuxonError {\n  constructor(unit) {\n    super(`Invalid unit ${unit}`);\n  }\n}\n\n/**\n * @private\n */\nexport class InvalidArgumentError extends LuxonError {}\n\n/**\n * @private\n */\nexport class ZoneIsAbstractError extends LuxonError {\n  constructor() {\n    super(\"Zone is an abstract class\");\n  }\n}\n", "/**\n * @private\n */\n\nconst n = \"numeric\",\n  s = \"short\",\n  l = \"long\";\n\nexport const DATE_SHORT = {\n  year: n,\n  month: n,\n  day: n,\n};\n\nexport const DATE_MED = {\n  year: n,\n  month: s,\n  day: n,\n};\n\nexport const DATE_MED_WITH_WEEKDAY = {\n  year: n,\n  month: s,\n  day: n,\n  weekday: s,\n};\n\nexport const DATE_FULL = {\n  year: n,\n  month: l,\n  day: n,\n};\n\nexport const DATE_HUGE = {\n  year: n,\n  month: l,\n  day: n,\n  weekday: l,\n};\n\nexport const TIME_SIMPLE = {\n  hour: n,\n  minute: n,\n};\n\nexport const TIME_WITH_SECONDS = {\n  hour: n,\n  minute: n,\n  second: n,\n};\n\nexport const TIME_WITH_SHORT_OFFSET = {\n  hour: n,\n  minute: n,\n  second: n,\n  timeZoneName: s,\n};\n\nexport const TIME_WITH_LONG_OFFSET = {\n  hour: n,\n  minute: n,\n  second: n,\n  timeZoneName: l,\n};\n\nexport const TIME_24_SIMPLE = {\n  hour: n,\n  minute: n,\n  hourCycle: \"h23\",\n};\n\nexport const TIME_24_WITH_SECONDS = {\n  hour: n,\n  minute: n,\n  second: n,\n  hourCycle: \"h23\",\n};\n\nexport const TIME_24_WITH_SHORT_OFFSET = {\n  hour: n,\n  minute: n,\n  second: n,\n  hourCycle: \"h23\",\n  timeZoneName: s,\n};\n\nexport const TIME_24_WITH_LONG_OFFSET = {\n  hour: n,\n  minute: n,\n  second: n,\n  hourCycle: \"h23\",\n  timeZoneName: l,\n};\n\nexport const DATETIME_SHORT = {\n  year: n,\n  month: n,\n  day: n,\n  hour: n,\n  minute: n,\n};\n\nexport const DATETIME_SHORT_WITH_SECONDS = {\n  year: n,\n  month: n,\n  day: n,\n  hour: n,\n  minute: n,\n  second: n,\n};\n\nexport const DATETIME_MED = {\n  year: n,\n  month: s,\n  day: n,\n  hour: n,\n  minute: n,\n};\n\nexport const DATETIME_MED_WITH_SECONDS = {\n  year: n,\n  month: s,\n  day: n,\n  hour: n,\n  minute: n,\n  second: n,\n};\n\nexport const DATETIME_MED_WITH_WEEKDAY = {\n  year: n,\n  month: s,\n  day: n,\n  weekday: s,\n  hour: n,\n  minute: n,\n};\n\nexport const DATETIME_FULL = {\n  year: n,\n  month: l,\n  day: n,\n  hour: n,\n  minute: n,\n  timeZoneName: s,\n};\n\nexport const DATETIME_FULL_WITH_SECONDS = {\n  year: n,\n  month: l,\n  day: n,\n  hour: n,\n  minute: n,\n  second: n,\n  timeZoneName: s,\n};\n\nexport const DATETIME_HUGE = {\n  year: n,\n  month: l,\n  day: n,\n  weekday: l,\n  hour: n,\n  minute: n,\n  timeZoneName: l,\n};\n\nexport const DATETIME_HUGE_WITH_SECONDS = {\n  year: n,\n  month: l,\n  day: n,\n  weekday: l,\n  hour: n,\n  minute: n,\n  second: n,\n  timeZoneName: l,\n};\n", "import { ZoneIsAbstractError } from \"./errors.js\";\n\n/**\n * @interface\n */\nexport default class Zone {\n  /**\n   * The type of zone\n   * @abstract\n   * @type {string}\n   */\n  get type() {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * The name of this zone.\n   * @abstract\n   * @type {string}\n   */\n  get name() {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * The IANA name of this zone.\n   * Defaults to `name` if not overwritten by a subclass.\n   * @abstract\n   * @type {string}\n   */\n  get ianaName() {\n    return this.name;\n  }\n\n  /**\n   * Returns whether the offset is known to be fixed for the whole year.\n   * @abstract\n   * @type {boolean}\n   */\n  get isUniversal() {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Returns the offset's common name (such as EST) at the specified timestamp\n   * @abstract\n   * @param {number} ts - Epoch milliseconds for which to get the name\n   * @param {Object} opts - Options to affect the format\n   * @param {string} opts.format - What style of offset to return. Accepts 'long' or 'short'.\n   * @param {string} opts.locale - What locale to return the offset name in.\n   * @return {string}\n   */\n  offsetName(ts, opts) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Returns the offset's value as a string\n   * @abstract\n   * @param {number} ts - Epoch milliseconds for which to get the offset\n   * @param {string} format - What style of offset to return.\n   *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n   * @return {string}\n   */\n  formatOffset(ts, format) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Return the offset in minutes for this zone at the specified timestamp.\n   * @abstract\n   * @param {number} ts - Epoch milliseconds for which to compute the offset\n   * @return {number}\n   */\n  offset(ts) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Return whether this Zone is equal to another zone\n   * @abstract\n   * @param {Zone} otherZone - the zone to compare\n   * @return {boolean}\n   */\n  equals(otherZone) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Return whether this Zone is valid.\n   * @abstract\n   * @type {boolean}\n   */\n  get isValid() {\n    throw new ZoneIsAbstractError();\n  }\n}\n", "import { formatOffset, parseZoneInfo } from \"../impl/util.js\";\nimport Zone from \"../zone.js\";\n\nlet singleton = null;\n\n/**\n * Represents the local zone for this JavaScript environment.\n * @implements {Zone}\n */\nexport default class SystemZone extends Zone {\n  /**\n   * Get a singleton instance of the local zone\n   * @return {SystemZone}\n   */\n  static get instance() {\n    if (singleton === null) {\n      singleton = new SystemZone();\n    }\n    return singleton;\n  }\n\n  /** @override **/\n  get type() {\n    return \"system\";\n  }\n\n  /** @override **/\n  get name() {\n    return new Intl.DateTimeFormat().resolvedOptions().timeZone;\n  }\n\n  /** @override **/\n  get isUniversal() {\n    return false;\n  }\n\n  /** @override **/\n  offsetName(ts, { format, locale }) {\n    return parseZoneInfo(ts, format, locale);\n  }\n\n  /** @override **/\n  formatOffset(ts, format) {\n    return formatOffset(this.offset(ts), format);\n  }\n\n  /** @override **/\n  offset(ts) {\n    return -new Date(ts).getTimezoneOffset();\n  }\n\n  /** @override **/\n  equals(otherZone) {\n    return otherZone.type === \"system\";\n  }\n\n  /** @override **/\n  get isValid() {\n    return true;\n  }\n}\n", "import { formatOffset, parseZoneInfo, isUndefined, objToLocalTS } from \"../impl/util.js\";\nimport Zone from \"../zone.js\";\n\nconst dtfCache = new Map();\nfunction makeDTF(zoneName) {\n  let dtf = dtfCache.get(zoneName);\n  if (dtf === undefined) {\n    dtf = new Intl.DateTimeFormat(\"en-US\", {\n      hour12: false,\n      timeZone: zoneName,\n      year: \"numeric\",\n      month: \"2-digit\",\n      day: \"2-digit\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n      second: \"2-digit\",\n      era: \"short\",\n    });\n    dtfCache.set(zoneName, dtf);\n  }\n  return dtf;\n}\n\nconst typeToPos = {\n  year: 0,\n  month: 1,\n  day: 2,\n  era: 3,\n  hour: 4,\n  minute: 5,\n  second: 6,\n};\n\nfunction hackyOffset(dtf, date) {\n  const formatted = dtf.format(date).replace(/\\u200E/g, \"\"),\n    parsed = /(\\d+)\\/(\\d+)\\/(\\d+) (AD|BC),? (\\d+):(\\d+):(\\d+)/.exec(formatted),\n    [, fMonth, fDay, fYear, fadOrBc, fHour, fMinute, fSecond] = parsed;\n  return [fYear, fMonth, fDay, fadOrBc, fHour, fMinute, fSecond];\n}\n\nfunction partsOffset(dtf, date) {\n  const formatted = dtf.formatToParts(date);\n  const filled = [];\n  for (let i = 0; i < formatted.length; i++) {\n    const { type, value } = formatted[i];\n    const pos = typeToPos[type];\n\n    if (type === \"era\") {\n      filled[pos] = value;\n    } else if (!isUndefined(pos)) {\n      filled[pos] = parseInt(value, 10);\n    }\n  }\n  return filled;\n}\n\nconst ianaZoneCache = new Map();\n/**\n * A zone identified by an IANA identifier, like America/New_York\n * @implements {Zone}\n */\nexport default class IANAZone extends Zone {\n  /**\n   * @param {string} name - Zone name\n   * @return {IANAZone}\n   */\n  static create(name) {\n    let zone = ianaZoneCache.get(name);\n    if (zone === undefined) {\n      ianaZoneCache.set(name, (zone = new IANAZone(name)));\n    }\n    return zone;\n  }\n\n  /**\n   * Reset local caches. Should only be necessary in testing scenarios.\n   * @return {void}\n   */\n  static resetCache() {\n    ianaZoneCache.clear();\n    dtfCache.clear();\n  }\n\n  /**\n   * Returns whether the provided string is a valid specifier. This only checks the string's format, not that the specifier identifies a known zone; see isValidZone for that.\n   * @param {string} s - The string to check validity on\n   * @example IANAZone.isValidSpecifier(\"America/New_York\") //=> true\n   * @example IANAZone.isValidSpecifier(\"Sport~~blorp\") //=> false\n   * @deprecated For backward compatibility, this forwards to isValidZone, better use `isValidZone()` directly instead.\n   * @return {boolean}\n   */\n  static isValidSpecifier(s) {\n    return this.isValidZone(s);\n  }\n\n  /**\n   * Returns whether the provided string identifies a real zone\n   * @param {string} zone - The string to check\n   * @example IANAZone.isValidZone(\"America/New_York\") //=> true\n   * @example IANAZone.isValidZone(\"Fantasia/Castle\") //=> false\n   * @example IANAZone.isValidZone(\"Sport~~blorp\") //=> false\n   * @return {boolean}\n   */\n  static isValidZone(zone) {\n    if (!zone) {\n      return false;\n    }\n    try {\n      new Intl.DateTimeFormat(\"en-US\", { timeZone: zone }).format();\n      return true;\n    } catch (e) {\n      return false;\n    }\n  }\n\n  constructor(name) {\n    super();\n    /** @private **/\n    this.zoneName = name;\n    /** @private **/\n    this.valid = IANAZone.isValidZone(name);\n  }\n\n  /**\n   * The type of zone. `iana` for all instances of `IANAZone`.\n   * @override\n   * @type {string}\n   */\n  get type() {\n    return \"iana\";\n  }\n\n  /**\n   * The name of this zone (i.e. the IANA zone name).\n   * @override\n   * @type {string}\n   */\n  get name() {\n    return this.zoneName;\n  }\n\n  /**\n   * Returns whether the offset is known to be fixed for the whole year:\n   * Always returns false for all IANA zones.\n   * @override\n   * @type {boolean}\n   */\n  get isUniversal() {\n    return false;\n  }\n\n  /**\n   * Returns the offset's common name (such as EST) at the specified timestamp\n   * @override\n   * @param {number} ts - Epoch milliseconds for which to get the name\n   * @param {Object} opts - Options to affect the format\n   * @param {string} opts.format - What style of offset to return. Accepts 'long' or 'short'.\n   * @param {string} opts.locale - What locale to return the offset name in.\n   * @return {string}\n   */\n  offsetName(ts, { format, locale }) {\n    return parseZoneInfo(ts, format, locale, this.name);\n  }\n\n  /**\n   * Returns the offset's value as a string\n   * @override\n   * @param {number} ts - Epoch milliseconds for which to get the offset\n   * @param {string} format - What style of offset to return.\n   *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n   * @return {string}\n   */\n  formatOffset(ts, format) {\n    return formatOffset(this.offset(ts), format);\n  }\n\n  /**\n   * Return the offset in minutes for this zone at the specified timestamp.\n   * @override\n   * @param {number} ts - Epoch milliseconds for which to compute the offset\n   * @return {number}\n   */\n  offset(ts) {\n    if (!this.valid) return NaN;\n    const date = new Date(ts);\n\n    if (isNaN(date)) return NaN;\n\n    const dtf = makeDTF(this.name);\n    let [year, month, day, adOrBc, hour, minute, second] = dtf.formatToParts\n      ? partsOffset(dtf, date)\n      : hackyOffset(dtf, date);\n\n    if (adOrBc === \"BC\") {\n      year = -Math.abs(year) + 1;\n    }\n\n    // because we're using hour12 and https://bugs.chromium.org/p/chromium/issues/detail?id=1025564&can=2&q=%2224%3A00%22%20datetimeformat\n    const adjustedHour = hour === 24 ? 0 : hour;\n\n    const asUTC = objToLocalTS({\n      year,\n      month,\n      day,\n      hour: adjustedHour,\n      minute,\n      second,\n      millisecond: 0,\n    });\n\n    let asTS = +date;\n    const over = asTS % 1000;\n    asTS -= over >= 0 ? over : 1000 + over;\n    return (asUTC - asTS) / (60 * 1000);\n  }\n\n  /**\n   * Return whether this Zone is equal to another zone\n   * @override\n   * @param {Zone} otherZone - the zone to compare\n   * @return {boolean}\n   */\n  equals(otherZone) {\n    return otherZone.type === \"iana\" && otherZone.name === this.name;\n  }\n\n  /**\n   * Return whether this Zone is valid.\n   * @override\n   * @type {boolean}\n   */\n  get isValid() {\n    return this.valid;\n  }\n}\n", "import { hasLocaleWeekInfo, hasRelative, padStart, roundTo, validateWeekSettings } from \"./util.js\";\nimport * as English from \"./english.js\";\nimport Settings from \"../settings.js\";\nimport DateTime from \"../datetime.js\";\nimport IANAZone from \"../zones/IANAZone.js\";\n\n// todo - remap caching\n\nlet intlLFCache = {};\nfunction getCachedLF(locString, opts = {}) {\n  const key = JSON.stringify([locString, opts]);\n  let dtf = intlLFCache[key];\n  if (!dtf) {\n    dtf = new Intl.ListFormat(locString, opts);\n    intlLFCache[key] = dtf;\n  }\n  return dtf;\n}\n\nconst intlDTCache = new Map();\nfunction getCachedDTF(locString, opts = {}) {\n  const key = JSON.stringify([locString, opts]);\n  let dtf = intlDTCache.get(key);\n  if (dtf === undefined) {\n    dtf = new Intl.DateTimeFormat(locString, opts);\n    intlDTCache.set(key, dtf);\n  }\n  return dtf;\n}\n\nconst intlNumCache = new Map();\nfunction getCachedINF(locString, opts = {}) {\n  const key = JSON.stringify([locString, opts]);\n  let inf = intlNumCache.get(key);\n  if (inf === undefined) {\n    inf = new Intl.NumberFormat(locString, opts);\n    intlNumCache.set(key, inf);\n  }\n  return inf;\n}\n\nconst intlRelCache = new Map();\nfunction getCachedRTF(locString, opts = {}) {\n  const { base, ...cacheKeyOpts } = opts; // exclude `base` from the options\n  const key = JSON.stringify([locString, cacheKeyOpts]);\n  let inf = intlRelCache.get(key);\n  if (inf === undefined) {\n    inf = new Intl.RelativeTimeFormat(locString, opts);\n    intlRelCache.set(key, inf);\n  }\n  return inf;\n}\n\nlet sysLocaleCache = null;\nfunction systemLocale() {\n  if (sysLocaleCache) {\n    return sysLocaleCache;\n  } else {\n    sysLocaleCache = new Intl.DateTimeFormat().resolvedOptions().locale;\n    return sysLocaleCache;\n  }\n}\n\nconst intlResolvedOptionsCache = new Map();\nfunction getCachedIntResolvedOptions(locString) {\n  let opts = intlResolvedOptionsCache.get(locString);\n  if (opts === undefined) {\n    opts = new Intl.DateTimeFormat(locString).resolvedOptions();\n    intlResolvedOptionsCache.set(locString, opts);\n  }\n  return opts;\n}\n\nconst weekInfoCache = new Map();\nfunction getCachedWeekInfo(locString) {\n  let data = weekInfoCache.get(locString);\n  if (!data) {\n    const locale = new Intl.Locale(locString);\n    // browsers currently implement this as a property, but spec says it should be a getter function\n    data = \"getWeekInfo\" in locale ? locale.getWeekInfo() : locale.weekInfo;\n    // minimalDays was removed from WeekInfo: https://github.com/tc39/proposal-intl-locale-info/issues/86\n    if (!(\"minimalDays\" in data)) {\n      data = { ...fallbackWeekSettings, ...data };\n    }\n    weekInfoCache.set(locString, data);\n  }\n  return data;\n}\n\nfunction parseLocaleString(localeStr) {\n  // I really want to avoid writing a BCP 47 parser\n  // see, e.g. https://github.com/wooorm/bcp-47\n  // Instead, we'll do this:\n\n  // a) if the string has no -u extensions, just leave it alone\n  // b) if it does, use Intl to resolve everything\n  // c) if Intl fails, try again without the -u\n\n  // private subtags and unicode subtags have ordering requirements,\n  // and we're not properly parsing this, so just strip out the\n  // private ones if they exist.\n  const xIndex = localeStr.indexOf(\"-x-\");\n  if (xIndex !== -1) {\n    localeStr = localeStr.substring(0, xIndex);\n  }\n\n  const uIndex = localeStr.indexOf(\"-u-\");\n  if (uIndex === -1) {\n    return [localeStr];\n  } else {\n    let options;\n    let selectedStr;\n    try {\n      options = getCachedDTF(localeStr).resolvedOptions();\n      selectedStr = localeStr;\n    } catch (e) {\n      const smaller = localeStr.substring(0, uIndex);\n      options = getCachedDTF(smaller).resolvedOptions();\n      selectedStr = smaller;\n    }\n\n    const { numberingSystem, calendar } = options;\n    return [selectedStr, numberingSystem, calendar];\n  }\n}\n\nfunction intlConfigString(localeStr, numberingSystem, outputCalendar) {\n  if (outputCalendar || numberingSystem) {\n    if (!localeStr.includes(\"-u-\")) {\n      localeStr += \"-u\";\n    }\n\n    if (outputCalendar) {\n      localeStr += `-ca-${outputCalendar}`;\n    }\n\n    if (numberingSystem) {\n      localeStr += `-nu-${numberingSystem}`;\n    }\n    return localeStr;\n  } else {\n    return localeStr;\n  }\n}\n\nfunction mapMonths(f) {\n  const ms = [];\n  for (let i = 1; i <= 12; i++) {\n    const dt = DateTime.utc(2009, i, 1);\n    ms.push(f(dt));\n  }\n  return ms;\n}\n\nfunction mapWeekdays(f) {\n  const ms = [];\n  for (let i = 1; i <= 7; i++) {\n    const dt = DateTime.utc(2016, 11, 13 + i);\n    ms.push(f(dt));\n  }\n  return ms;\n}\n\nfunction listStuff(loc, length, englishFn, intlFn) {\n  const mode = loc.listingMode();\n\n  if (mode === \"error\") {\n    return null;\n  } else if (mode === \"en\") {\n    return englishFn(length);\n  } else {\n    return intlFn(length);\n  }\n}\n\nfunction supportsFastNumbers(loc) {\n  if (loc.numberingSystem && loc.numberingSystem !== \"latn\") {\n    return false;\n  } else {\n    return (\n      loc.numberingSystem === \"latn\" ||\n      !loc.locale ||\n      loc.locale.startsWith(\"en\") ||\n      getCachedIntResolvedOptions(loc.locale).numberingSystem === \"latn\"\n    );\n  }\n}\n\n/**\n * @private\n */\n\nclass PolyNumberFormatter {\n  constructor(intl, forceSimple, opts) {\n    this.padTo = opts.padTo || 0;\n    this.floor = opts.floor || false;\n\n    const { padTo, floor, ...otherOpts } = opts;\n\n    if (!forceSimple || Object.keys(otherOpts).length > 0) {\n      const intlOpts = { useGrouping: false, ...opts };\n      if (opts.padTo > 0) intlOpts.minimumIntegerDigits = opts.padTo;\n      this.inf = getCachedINF(intl, intlOpts);\n    }\n  }\n\n  format(i) {\n    if (this.inf) {\n      const fixed = this.floor ? Math.floor(i) : i;\n      return this.inf.format(fixed);\n    } else {\n      // to match the browser's numberformatter defaults\n      const fixed = this.floor ? Math.floor(i) : roundTo(i, 3);\n      return padStart(fixed, this.padTo);\n    }\n  }\n}\n\n/**\n * @private\n */\n\nclass PolyDateFormatter {\n  constructor(dt, intl, opts) {\n    this.opts = opts;\n    this.originalZone = undefined;\n\n    let z = undefined;\n    if (this.opts.timeZone) {\n      // Don't apply any workarounds if a timeZone is explicitly provided in opts\n      this.dt = dt;\n    } else if (dt.zone.type === \"fixed\") {\n      // UTC-8 or Etc/UTC-8 are not part of tzdata, only Etc/GMT+8 and the like.\n      // That is why fixed-offset TZ is set to that unless it is:\n      // 1. Representing offset 0 when UTC is used to maintain previous behavior and does not become GMT.\n      // 2. Unsupported by the browser:\n      //    - some do not support Etc/\n      //    - < Etc/GMT-14, > Etc/GMT+12, and 30-minute or 45-minute offsets are not part of tzdata\n      const gmtOffset = -1 * (dt.offset / 60);\n      const offsetZ = gmtOffset >= 0 ? `Etc/GMT+${gmtOffset}` : `Etc/GMT${gmtOffset}`;\n      if (dt.offset !== 0 && IANAZone.create(offsetZ).valid) {\n        z = offsetZ;\n        this.dt = dt;\n      } else {\n        // Not all fixed-offset zones like Etc/+4:30 are present in tzdata so\n        // we manually apply the offset and substitute the zone as needed.\n        z = \"UTC\";\n        this.dt = dt.offset === 0 ? dt : dt.setZone(\"UTC\").plus({ minutes: dt.offset });\n        this.originalZone = dt.zone;\n      }\n    } else if (dt.zone.type === \"system\") {\n      this.dt = dt;\n    } else if (dt.zone.type === \"iana\") {\n      this.dt = dt;\n      z = dt.zone.name;\n    } else {\n      // Custom zones can have any offset / offsetName so we just manually\n      // apply the offset and substitute the zone as needed.\n      z = \"UTC\";\n      this.dt = dt.setZone(\"UTC\").plus({ minutes: dt.offset });\n      this.originalZone = dt.zone;\n    }\n\n    const intlOpts = { ...this.opts };\n    intlOpts.timeZone = intlOpts.timeZone || z;\n    this.dtf = getCachedDTF(intl, intlOpts);\n  }\n\n  format() {\n    if (this.originalZone) {\n      // If we have to substitute in the actual zone name, we have to use\n      // formatToParts so that the timezone can be replaced.\n      return this.formatToParts()\n        .map(({ value }) => value)\n        .join(\"\");\n    }\n    return this.dtf.format(this.dt.toJSDate());\n  }\n\n  formatToParts() {\n    const parts = this.dtf.formatToParts(this.dt.toJSDate());\n    if (this.originalZone) {\n      return parts.map((part) => {\n        if (part.type === \"timeZoneName\") {\n          const offsetName = this.originalZone.offsetName(this.dt.ts, {\n            locale: this.dt.locale,\n            format: this.opts.timeZoneName,\n          });\n          return {\n            ...part,\n            value: offsetName,\n          };\n        } else {\n          return part;\n        }\n      });\n    }\n    return parts;\n  }\n\n  resolvedOptions() {\n    return this.dtf.resolvedOptions();\n  }\n}\n\n/**\n * @private\n */\nclass PolyRelFormatter {\n  constructor(intl, isEnglish, opts) {\n    this.opts = { style: \"long\", ...opts };\n    if (!isEnglish && hasRelative()) {\n      this.rtf = getCachedRTF(intl, opts);\n    }\n  }\n\n  format(count, unit) {\n    if (this.rtf) {\n      return this.rtf.format(count, unit);\n    } else {\n      return English.formatRelativeTime(unit, count, this.opts.numeric, this.opts.style !== \"long\");\n    }\n  }\n\n  formatToParts(count, unit) {\n    if (this.rtf) {\n      return this.rtf.formatToParts(count, unit);\n    } else {\n      return [];\n    }\n  }\n}\n\nconst fallbackWeekSettings = {\n  firstDay: 1,\n  minimalDays: 4,\n  weekend: [6, 7],\n};\n\n/**\n * @private\n */\nexport default class Locale {\n  static fromOpts(opts) {\n    return Locale.create(\n      opts.locale,\n      opts.numberingSystem,\n      opts.outputCalendar,\n      opts.weekSettings,\n      opts.defaultToEN\n    );\n  }\n\n  static create(locale, numberingSystem, outputCalendar, weekSettings, defaultToEN = false) {\n    const specifiedLocale = locale || Settings.defaultLocale;\n    // the system locale is useful for human-readable strings but annoying for parsing/formatting known formats\n    const localeR = specifiedLocale || (defaultToEN ? \"en-US\" : systemLocale());\n    const numberingSystemR = numberingSystem || Settings.defaultNumberingSystem;\n    const outputCalendarR = outputCalendar || Settings.defaultOutputCalendar;\n    const weekSettingsR = validateWeekSettings(weekSettings) || Settings.defaultWeekSettings;\n    return new Locale(localeR, numberingSystemR, outputCalendarR, weekSettingsR, specifiedLocale);\n  }\n\n  static resetCache() {\n    sysLocaleCache = null;\n    intlDTCache.clear();\n    intlNumCache.clear();\n    intlRelCache.clear();\n    intlResolvedOptionsCache.clear();\n    weekInfoCache.clear();\n  }\n\n  static fromObject({ locale, numberingSystem, outputCalendar, weekSettings } = {}) {\n    return Locale.create(locale, numberingSystem, outputCalendar, weekSettings);\n  }\n\n  constructor(locale, numbering, outputCalendar, weekSettings, specifiedLocale) {\n    const [parsedLocale, parsedNumberingSystem, parsedOutputCalendar] = parseLocaleString(locale);\n\n    this.locale = parsedLocale;\n    this.numberingSystem = numbering || parsedNumberingSystem || null;\n    this.outputCalendar = outputCalendar || parsedOutputCalendar || null;\n    this.weekSettings = weekSettings;\n    this.intl = intlConfigString(this.locale, this.numberingSystem, this.outputCalendar);\n\n    this.weekdaysCache = { format: {}, standalone: {} };\n    this.monthsCache = { format: {}, standalone: {} };\n    this.meridiemCache = null;\n    this.eraCache = {};\n\n    this.specifiedLocale = specifiedLocale;\n    this.fastNumbersCached = null;\n  }\n\n  get fastNumbers() {\n    if (this.fastNumbersCached == null) {\n      this.fastNumbersCached = supportsFastNumbers(this);\n    }\n\n    return this.fastNumbersCached;\n  }\n\n  listingMode() {\n    const isActuallyEn = this.isEnglish();\n    const hasNoWeirdness =\n      (this.numberingSystem === null || this.numberingSystem === \"latn\") &&\n      (this.outputCalendar === null || this.outputCalendar === \"gregory\");\n    return isActuallyEn && hasNoWeirdness ? \"en\" : \"intl\";\n  }\n\n  clone(alts) {\n    if (!alts || Object.getOwnPropertyNames(alts).length === 0) {\n      return this;\n    } else {\n      return Locale.create(\n        alts.locale || this.specifiedLocale,\n        alts.numberingSystem || this.numberingSystem,\n        alts.outputCalendar || this.outputCalendar,\n        validateWeekSettings(alts.weekSettings) || this.weekSettings,\n        alts.defaultToEN || false\n      );\n    }\n  }\n\n  redefaultToEN(alts = {}) {\n    return this.clone({ ...alts, defaultToEN: true });\n  }\n\n  redefaultToSystem(alts = {}) {\n    return this.clone({ ...alts, defaultToEN: false });\n  }\n\n  months(length, format = false) {\n    return listStuff(this, length, English.months, () => {\n      // Workaround for \"ja\" locale: formatToParts does not label all parts of the month\n      // as \"month\" and for this locale there is no difference between \"format\" and \"non-format\".\n      // As such, just use format() instead of formatToParts() and take the whole string\n      const monthSpecialCase = this.intl === \"ja\" || this.intl.startsWith(\"ja-\");\n      format &= !monthSpecialCase;\n      const intl = format ? { month: length, day: \"numeric\" } : { month: length },\n        formatStr = format ? \"format\" : \"standalone\";\n      if (!this.monthsCache[formatStr][length]) {\n        const mapper = !monthSpecialCase\n          ? (dt) => this.extract(dt, intl, \"month\")\n          : (dt) => this.dtFormatter(dt, intl).format();\n        this.monthsCache[formatStr][length] = mapMonths(mapper);\n      }\n      return this.monthsCache[formatStr][length];\n    });\n  }\n\n  weekdays(length, format = false) {\n    return listStuff(this, length, English.weekdays, () => {\n      const intl = format\n          ? { weekday: length, year: \"numeric\", month: \"long\", day: \"numeric\" }\n          : { weekday: length },\n        formatStr = format ? \"format\" : \"standalone\";\n      if (!this.weekdaysCache[formatStr][length]) {\n        this.weekdaysCache[formatStr][length] = mapWeekdays((dt) =>\n          this.extract(dt, intl, \"weekday\")\n        );\n      }\n      return this.weekdaysCache[formatStr][length];\n    });\n  }\n\n  meridiems() {\n    return listStuff(\n      this,\n      undefined,\n      () => English.meridiems,\n      () => {\n        // In theory there could be aribitrary day periods. We're gonna assume there are exactly two\n        // for AM and PM. This is probably wrong, but it's makes parsing way easier.\n        if (!this.meridiemCache) {\n          const intl = { hour: \"numeric\", hourCycle: \"h12\" };\n          this.meridiemCache = [DateTime.utc(2016, 11, 13, 9), DateTime.utc(2016, 11, 13, 19)].map(\n            (dt) => this.extract(dt, intl, \"dayperiod\")\n          );\n        }\n\n        return this.meridiemCache;\n      }\n    );\n  }\n\n  eras(length) {\n    return listStuff(this, length, English.eras, () => {\n      const intl = { era: length };\n\n      // This is problematic. Different calendars are going to define eras totally differently. What I need is the minimum set of dates\n      // to definitely enumerate them.\n      if (!this.eraCache[length]) {\n        this.eraCache[length] = [DateTime.utc(-40, 1, 1), DateTime.utc(2017, 1, 1)].map((dt) =>\n          this.extract(dt, intl, \"era\")\n        );\n      }\n\n      return this.eraCache[length];\n    });\n  }\n\n  extract(dt, intlOpts, field) {\n    const df = this.dtFormatter(dt, intlOpts),\n      results = df.formatToParts(),\n      matching = results.find((m) => m.type.toLowerCase() === field);\n    return matching ? matching.value : null;\n  }\n\n  numberFormatter(opts = {}) {\n    // this forcesimple option is never used (the only caller short-circuits on it, but it seems safer to leave)\n    // (in contrast, the rest of the condition is used heavily)\n    return new PolyNumberFormatter(this.intl, opts.forceSimple || this.fastNumbers, opts);\n  }\n\n  dtFormatter(dt, intlOpts = {}) {\n    return new PolyDateFormatter(dt, this.intl, intlOpts);\n  }\n\n  relFormatter(opts = {}) {\n    return new PolyRelFormatter(this.intl, this.isEnglish(), opts);\n  }\n\n  listFormatter(opts = {}) {\n    return getCachedLF(this.intl, opts);\n  }\n\n  isEnglish() {\n    return (\n      this.locale === \"en\" ||\n      this.locale.toLowerCase() === \"en-us\" ||\n      getCachedIntResolvedOptions(this.intl).locale.startsWith(\"en-us\")\n    );\n  }\n\n  getWeekSettings() {\n    if (this.weekSettings) {\n      return this.weekSettings;\n    } else if (!hasLocaleWeekInfo()) {\n      return fallbackWeekSettings;\n    } else {\n      return getCachedWeekInfo(this.locale);\n    }\n  }\n\n  getStartOfWeek() {\n    return this.getWeekSettings().firstDay;\n  }\n\n  getMinDaysInFirstWeek() {\n    return this.getWeekSettings().minimalDays;\n  }\n\n  getWeekendDays() {\n    return this.getWeekSettings().weekend;\n  }\n\n  equals(other) {\n    return (\n      this.locale === other.locale &&\n      this.numberingSystem === other.numberingSystem &&\n      this.outputCalendar === other.outputCalendar\n    );\n  }\n\n  toString() {\n    return `Locale(${this.locale}, ${this.numberingSystem}, ${this.outputCalendar})`;\n  }\n}\n", "import { formatOffset, signedOffset } from \"../impl/util.js\";\nimport Zone from \"../zone.js\";\n\nlet singleton = null;\n\n/**\n * A zone with a fixed offset (meaning no DST)\n * @implements {Zone}\n */\nexport default class FixedOffsetZone extends Zone {\n  /**\n   * Get a singleton instance of UTC\n   * @return {FixedOffsetZone}\n   */\n  static get utcInstance() {\n    if (singleton === null) {\n      singleton = new FixedOffsetZone(0);\n    }\n    return singleton;\n  }\n\n  /**\n   * Get an instance with a specified offset\n   * @param {number} offset - The offset in minutes\n   * @return {FixedOffsetZone}\n   */\n  static instance(offset) {\n    return offset === 0 ? FixedOffsetZone.utcInstance : new FixedOffsetZone(offset);\n  }\n\n  /**\n   * Get an instance of FixedOffsetZone from a UTC offset string, like \"UTC+6\"\n   * @param {string} s - The offset string to parse\n   * @example FixedOffsetZone.parseSpecifier(\"UTC+6\")\n   * @example FixedOffsetZone.parseSpecifier(\"UTC+06\")\n   * @example FixedOffsetZone.parseSpecifier(\"UTC-6:00\")\n   * @return {FixedOffsetZone}\n   */\n  static parseSpecifier(s) {\n    if (s) {\n      const r = s.match(/^utc(?:([+-]\\d{1,2})(?::(\\d{2}))?)?$/i);\n      if (r) {\n        return new FixedOffsetZone(signedOffset(r[1], r[2]));\n      }\n    }\n    return null;\n  }\n\n  constructor(offset) {\n    super();\n    /** @private **/\n    this.fixed = offset;\n  }\n\n  /**\n   * The type of zone. `fixed` for all instances of `FixedOffsetZone`.\n   * @override\n   * @type {string}\n   */\n  get type() {\n    return \"fixed\";\n  }\n\n  /**\n   * The name of this zone.\n   * All fixed zones' names always start with \"UTC\" (plus optional offset)\n   * @override\n   * @type {string}\n   */\n  get name() {\n    return this.fixed === 0 ? \"UTC\" : `UTC${formatOffset(this.fixed, \"narrow\")}`;\n  }\n\n  /**\n   * The IANA name of this zone, i.e. `Etc/UTC` or `Etc/GMT+/-nn`\n   *\n   * @override\n   * @type {string}\n   */\n  get ianaName() {\n    if (this.fixed === 0) {\n      return \"Etc/UTC\";\n    } else {\n      return `Etc/GMT${formatOffset(-this.fixed, \"narrow\")}`;\n    }\n  }\n\n  /**\n   * Returns the offset's common name at the specified timestamp.\n   *\n   * For fixed offset zones this equals to the zone name.\n   * @override\n   */\n  offsetName() {\n    return this.name;\n  }\n\n  /**\n   * Returns the offset's value as a string\n   * @override\n   * @param {number} ts - Epoch milliseconds for which to get the offset\n   * @param {string} format - What style of offset to return.\n   *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n   * @return {string}\n   */\n  formatOffset(ts, format) {\n    return formatOffset(this.fixed, format);\n  }\n\n  /**\n   * Returns whether the offset is known to be fixed for the whole year:\n   * Always returns true for all fixed offset zones.\n   * @override\n   * @type {boolean}\n   */\n  get isUniversal() {\n    return true;\n  }\n\n  /**\n   * Return the offset in minutes for this zone at the specified timestamp.\n   *\n   * For fixed offset zones, this is constant and does not depend on a timestamp.\n   * @override\n   * @return {number}\n   */\n  offset() {\n    return this.fixed;\n  }\n\n  /**\n   * Return whether this Zone is equal to another zone (i.e. also fixed and same offset)\n   * @override\n   * @param {Zone} otherZone - the zone to compare\n   * @return {boolean}\n   */\n  equals(otherZone) {\n    return otherZone.type === \"fixed\" && otherZone.fixed === this.fixed;\n  }\n\n  /**\n   * Return whether this Zone is valid:\n   * All fixed offset zones are valid.\n   * @override\n   * @type {boolean}\n   */\n  get isValid() {\n    return true;\n  }\n}\n", "import Zone from \"../zone.js\";\n\n/**\n * A zone that failed to parse. You should never need to instantiate this.\n * @implements {Zone}\n */\nexport default class InvalidZone extends Zone {\n  constructor(zoneName) {\n    super();\n    /**  @private */\n    this.zoneName = zoneName;\n  }\n\n  /** @override **/\n  get type() {\n    return \"invalid\";\n  }\n\n  /** @override **/\n  get name() {\n    return this.zoneName;\n  }\n\n  /** @override **/\n  get isUniversal() {\n    return false;\n  }\n\n  /** @override **/\n  offsetName() {\n    return null;\n  }\n\n  /** @override **/\n  formatOffset() {\n    return \"\";\n  }\n\n  /** @override **/\n  offset() {\n    return NaN;\n  }\n\n  /** @override **/\n  equals() {\n    return false;\n  }\n\n  /** @override **/\n  get isValid() {\n    return false;\n  }\n}\n", "/**\n * @private\n */\n\nimport Zone from \"../zone.js\";\nimport IANAZone from \"../zones/IANAZone.js\";\nimport FixedOffsetZone from \"../zones/fixedOffsetZone.js\";\nimport InvalidZone from \"../zones/invalidZone.js\";\n\nimport { isUndefined, isString, isNumber } from \"./util.js\";\nimport SystemZone from \"../zones/systemZone.js\";\n\nexport function normalizeZone(input, defaultZone) {\n  let offset;\n  if (isUndefined(input) || input === null) {\n    return defaultZone;\n  } else if (input instanceof Zone) {\n    return input;\n  } else if (isString(input)) {\n    const lowered = input.toLowerCase();\n    if (lowered === \"default\") return defaultZone;\n    else if (lowered === \"local\" || lowered === \"system\") return SystemZone.instance;\n    else if (lowered === \"utc\" || lowered === \"gmt\") return FixedOffsetZone.utcInstance;\n    else return FixedOffsetZone.parseSpecifier(lowered) || IANAZone.create(input);\n  } else if (isNumber(input)) {\n    return FixedOffsetZone.instance(input);\n  } else if (typeof input === \"object\" && \"offset\" in input && typeof input.offset === \"function\") {\n    // This is dumb, but the instanceof check above doesn't seem to really work\n    // so we're duck checking it\n    return input;\n  } else {\n    return new InvalidZone(input);\n  }\n}\n", "const numberingSystems = {\n  arab: \"[\\u0660-\\u0669]\",\n  arabext: \"[\\u06F0-\\u06F9]\",\n  bali: \"[\\u1B50-\\u1B59]\",\n  beng: \"[\\u09E6-\\u09EF]\",\n  deva: \"[\\u0966-\\u096F]\",\n  fullwide: \"[\\uFF10-\\uFF19]\",\n  gujr: \"[\\u0AE6-\\u0AEF]\",\n  hanidec: \"[〇|一|二|三|四|五|六|七|八|九]\",\n  khmr: \"[\\u17E0-\\u17E9]\",\n  knda: \"[\\u0CE6-\\u0CEF]\",\n  laoo: \"[\\u0ED0-\\u0ED9]\",\n  limb: \"[\\u1946-\\u194F]\",\n  mlym: \"[\\u0D66-\\u0D6F]\",\n  mong: \"[\\u1810-\\u1819]\",\n  mymr: \"[\\u1040-\\u1049]\",\n  orya: \"[\\u0B66-\\u0B6F]\",\n  tamldec: \"[\\u0BE6-\\u0BEF]\",\n  telu: \"[\\u0C66-\\u0C6F]\",\n  thai: \"[\\u0E50-\\u0E59]\",\n  tibt: \"[\\u0F20-\\u0F29]\",\n  latn: \"\\\\d\",\n};\n\nconst numberingSystemsUTF16 = {\n  arab: [1632, 1641],\n  arabext: [1776, 1785],\n  bali: [6992, 7001],\n  beng: [2534, 2543],\n  deva: [2406, 2415],\n  fullwide: [65296, 65303],\n  gujr: [2790, 2799],\n  khmr: [6112, 6121],\n  knda: [3302, 3311],\n  laoo: [3792, 3801],\n  limb: [6470, 6479],\n  mlym: [3430, 3439],\n  mong: [6160, 6169],\n  mymr: [4160, 4169],\n  orya: [2918, 2927],\n  tamldec: [3046, 3055],\n  telu: [3174, 3183],\n  thai: [3664, 3673],\n  tibt: [3872, 3881],\n};\n\nconst hanidecChars = numberingSystems.hanidec.replace(/[\\[|\\]]/g, \"\").split(\"\");\n\nexport function parseDigits(str) {\n  let value = parseInt(str, 10);\n  if (isNaN(value)) {\n    value = \"\";\n    for (let i = 0; i < str.length; i++) {\n      const code = str.charCodeAt(i);\n\n      if (str[i].search(numberingSystems.hanidec) !== -1) {\n        value += hanidecChars.indexOf(str[i]);\n      } else {\n        for (const key in numberingSystemsUTF16) {\n          const [min, max] = numberingSystemsUTF16[key];\n          if (code >= min && code <= max) {\n            value += code - min;\n          }\n        }\n      }\n    }\n    return parseInt(value, 10);\n  } else {\n    return value;\n  }\n}\n\n// cache of {numberingSystem: {append: regex}}\nconst digitRegexCache = new Map();\nexport function resetDigitRegexCache() {\n  digitRegexCache.clear();\n}\n\nexport function digitRegex({ numberingSystem }, append = \"\") {\n  const ns = numberingSystem || \"latn\";\n\n  let appendCache = digitRegexCache.get(ns);\n  if (appendCache === undefined) {\n    appendCache = new Map();\n    digitRegexCache.set(ns, appendCache);\n  }\n  let regex = appendCache.get(append);\n  if (regex === undefined) {\n    regex = new RegExp(`${numberingSystems[ns]}${append}`);\n    appendCache.set(append, regex);\n  }\n\n  return regex;\n}\n", "import SystemZone from \"./zones/systemZone.js\";\nimport IANAZone from \"./zones/IANAZone.js\";\nimport Locale from \"./impl/locale.js\";\nimport DateTime from \"./datetime.js\";\n\nimport { normalizeZone } from \"./impl/zoneUtil.js\";\nimport { validateWeekSettings } from \"./impl/util.js\";\nimport { resetDigitRegexCache } from \"./impl/digits.js\";\n\nlet now = () => Date.now(),\n  defaultZone = \"system\",\n  defaultLocale = null,\n  defaultNumberingSystem = null,\n  defaultOutputCalendar = null,\n  twoDigitCutoffYear = 60,\n  throwOnInvalid,\n  defaultWeekSettings = null;\n\n/**\n * Settings contains static getters and setters that control <PERSON><PERSON>'s overall behavior. Luxon is a simple library with few options, but the ones it does have live here.\n */\nexport default class Settings {\n  /**\n   * Get the callback for returning the current timestamp.\n   * @type {function}\n   */\n  static get now() {\n    return now;\n  }\n\n  /**\n   * Set the callback for returning the current timestamp.\n   * The function should return a number, which will be interpreted as an Epoch millisecond count\n   * @type {function}\n   * @example Settings.now = () => Date.now() + 3000 // pretend it is 3 seconds in the future\n   * @example Settings.now = () => 0 // always pretend it's Jan 1, 1970 at midnight in UTC time\n   */\n  static set now(n) {\n    now = n;\n  }\n\n  /**\n   * Set the default time zone to create DateTimes in. Does not affect existing instances.\n   * Use the value \"system\" to reset this value to the system's time zone.\n   * @type {string}\n   */\n  static set defaultZone(zone) {\n    defaultZone = zone;\n  }\n\n  /**\n   * Get the default time zone object currently used to create DateTimes. Does not affect existing instances.\n   * The default value is the system's time zone (the one set on the machine that runs this code).\n   * @type {Zone}\n   */\n  static get defaultZone() {\n    return normalizeZone(defaultZone, SystemZone.instance);\n  }\n\n  /**\n   * Get the default locale to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static get defaultLocale() {\n    return defaultLocale;\n  }\n\n  /**\n   * Set the default locale to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static set defaultLocale(locale) {\n    defaultLocale = locale;\n  }\n\n  /**\n   * Get the default numbering system to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static get defaultNumberingSystem() {\n    return defaultNumberingSystem;\n  }\n\n  /**\n   * Set the default numbering system to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static set defaultNumberingSystem(numberingSystem) {\n    defaultNumberingSystem = numberingSystem;\n  }\n\n  /**\n   * Get the default output calendar to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static get defaultOutputCalendar() {\n    return defaultOutputCalendar;\n  }\n\n  /**\n   * Set the default output calendar to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static set defaultOutputCalendar(outputCalendar) {\n    defaultOutputCalendar = outputCalendar;\n  }\n\n  /**\n   * @typedef {Object} WeekSettings\n   * @property {number} firstDay\n   * @property {number} minimalDays\n   * @property {number[]} weekend\n   */\n\n  /**\n   * @return {WeekSettings|null}\n   */\n  static get defaultWeekSettings() {\n    return defaultWeekSettings;\n  }\n\n  /**\n   * Allows overriding the default locale week settings, i.e. the start of the week, the weekend and\n   * how many days are required in the first week of a year.\n   * Does not affect existing instances.\n   *\n   * @param {WeekSettings|null} weekSettings\n   */\n  static set defaultWeekSettings(weekSettings) {\n    defaultWeekSettings = validateWeekSettings(weekSettings);\n  }\n\n  /**\n   * Get the cutoff year for whether a 2-digit year string is interpreted in the current or previous century. Numbers higher than the cutoff will be considered to mean 19xx and numbers lower or equal to the cutoff will be considered 20xx.\n   * @type {number}\n   */\n  static get twoDigitCutoffYear() {\n    return twoDigitCutoffYear;\n  }\n\n  /**\n   * Set the cutoff year for whether a 2-digit year string is interpreted in the current or previous century. Numbers higher than the cutoff will be considered to mean 19xx and numbers lower or equal to the cutoff will be considered 20xx.\n   * @type {number}\n   * @example Settings.twoDigitCutoffYear = 0 // all 'yy' are interpreted as 20th century\n   * @example Settings.twoDigitCutoffYear = 99 // all 'yy' are interpreted as 21st century\n   * @example Settings.twoDigitCutoffYear = 50 // '49' -> 2049; '50' -> 1950\n   * @example Settings.twoDigitCutoffYear = 1950 // interpreted as 50\n   * @example Settings.twoDigitCutoffYear = 2050 // ALSO interpreted as 50\n   */\n  static set twoDigitCutoffYear(cutoffYear) {\n    twoDigitCutoffYear = cutoffYear % 100;\n  }\n\n  /**\n   * Get whether Luxon will throw when it encounters invalid DateTimes, Durations, or Intervals\n   * @type {boolean}\n   */\n  static get throwOnInvalid() {\n    return throwOnInvalid;\n  }\n\n  /**\n   * Set whether Luxon will throw when it encounters invalid DateTimes, Durations, or Intervals\n   * @type {boolean}\n   */\n  static set throwOnInvalid(t) {\n    throwOnInvalid = t;\n  }\n\n  /**\n   * Reset Luxon's global caches. Should only be necessary in testing scenarios.\n   * @return {void}\n   */\n  static resetCaches() {\n    Locale.resetCache();\n    IANAZone.resetCache();\n    DateTime.resetCache();\n    resetDigitRegexCache();\n  }\n}\n", "export default class Invalid {\n  constructor(reason, explanation) {\n    this.reason = reason;\n    this.explanation = explanation;\n  }\n\n  toMessage() {\n    if (this.explanation) {\n      return `${this.reason}: ${this.explanation}`;\n    } else {\n      return this.reason;\n    }\n  }\n}\n", "import {\n  integerBetween,\n  isLeapYear,\n  timeObject,\n  daysInYear,\n  daysInMonth,\n  weeksInWeekYear,\n  isInteger,\n  isUndefined,\n} from \"./util.js\";\nimport Invalid from \"./invalid.js\";\nimport { ConflictingSpecificationError } from \"../errors.js\";\n\nconst nonLeapLadder = [0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334],\n  leapLadder = [0, 31, 60, 91, 121, 152, 182, 213, 244, 274, 305, 335];\n\nfunction unitOutOfRange(unit, value) {\n  return new Invalid(\n    \"unit out of range\",\n    `you specified ${value} (of type ${typeof value}) as a ${unit}, which is invalid`\n  );\n}\n\nexport function dayOfWeek(year, month, day) {\n  const d = new Date(Date.UTC(year, month - 1, day));\n\n  if (year < 100 && year >= 0) {\n    d.setUTCFullYear(d.getUTCFullYear() - 1900);\n  }\n\n  const js = d.getUTCDay();\n\n  return js === 0 ? 7 : js;\n}\n\nfunction computeOrdinal(year, month, day) {\n  return day + (isLeapYear(year) ? leapLadder : nonLeapLadder)[month - 1];\n}\n\nfunction uncomputeOrdinal(year, ordinal) {\n  const table = isLeapYear(year) ? leapLadder : nonLeapLadder,\n    month0 = table.findIndex((i) => i < ordinal),\n    day = ordinal - table[month0];\n  return { month: month0 + 1, day };\n}\n\nexport function isoWeekdayToLocal(isoWeekday, startOfWeek) {\n  return ((isoWeekday - startOfWeek + 7) % 7) + 1;\n}\n\n/**\n * @private\n */\n\nexport function gregorianToWeek(gregObj, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const { year, month, day } = gregObj,\n    ordinal = computeOrdinal(year, month, day),\n    weekday = isoWeekdayToLocal(dayOfWeek(year, month, day), startOfWeek);\n\n  let weekNumber = Math.floor((ordinal - weekday + 14 - minDaysInFirstWeek) / 7),\n    weekYear;\n\n  if (weekNumber < 1) {\n    weekYear = year - 1;\n    weekNumber = weeksInWeekYear(weekYear, minDaysInFirstWeek, startOfWeek);\n  } else if (weekNumber > weeksInWeekYear(year, minDaysInFirstWeek, startOfWeek)) {\n    weekYear = year + 1;\n    weekNumber = 1;\n  } else {\n    weekYear = year;\n  }\n\n  return { weekYear, weekNumber, weekday, ...timeObject(gregObj) };\n}\n\nexport function weekToGregorian(weekData, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const { weekYear, weekNumber, weekday } = weekData,\n    weekdayOfJan4 = isoWeekdayToLocal(dayOfWeek(weekYear, 1, minDaysInFirstWeek), startOfWeek),\n    yearInDays = daysInYear(weekYear);\n\n  let ordinal = weekNumber * 7 + weekday - weekdayOfJan4 - 7 + minDaysInFirstWeek,\n    year;\n\n  if (ordinal < 1) {\n    year = weekYear - 1;\n    ordinal += daysInYear(year);\n  } else if (ordinal > yearInDays) {\n    year = weekYear + 1;\n    ordinal -= daysInYear(weekYear);\n  } else {\n    year = weekYear;\n  }\n\n  const { month, day } = uncomputeOrdinal(year, ordinal);\n  return { year, month, day, ...timeObject(weekData) };\n}\n\nexport function gregorianToOrdinal(gregData) {\n  const { year, month, day } = gregData;\n  const ordinal = computeOrdinal(year, month, day);\n  return { year, ordinal, ...timeObject(gregData) };\n}\n\nexport function ordinalToGregorian(ordinalData) {\n  const { year, ordinal } = ordinalData;\n  const { month, day } = uncomputeOrdinal(year, ordinal);\n  return { year, month, day, ...timeObject(ordinalData) };\n}\n\n/**\n * Check if local week units like localWeekday are used in obj.\n * If so, validates that they are not mixed with ISO week units and then copies them to the normal week unit properties.\n * Modifies obj in-place!\n * @param obj the object values\n */\nexport function usesLocalWeekValues(obj, loc) {\n  const hasLocaleWeekData =\n    !isUndefined(obj.localWeekday) ||\n    !isUndefined(obj.localWeekNumber) ||\n    !isUndefined(obj.localWeekYear);\n  if (hasLocaleWeekData) {\n    const hasIsoWeekData =\n      !isUndefined(obj.weekday) || !isUndefined(obj.weekNumber) || !isUndefined(obj.weekYear);\n\n    if (hasIsoWeekData) {\n      throw new ConflictingSpecificationError(\n        \"Cannot mix locale-based week fields with ISO-based week fields\"\n      );\n    }\n    if (!isUndefined(obj.localWeekday)) obj.weekday = obj.localWeekday;\n    if (!isUndefined(obj.localWeekNumber)) obj.weekNumber = obj.localWeekNumber;\n    if (!isUndefined(obj.localWeekYear)) obj.weekYear = obj.localWeekYear;\n    delete obj.localWeekday;\n    delete obj.localWeekNumber;\n    delete obj.localWeekYear;\n    return {\n      minDaysInFirstWeek: loc.getMinDaysInFirstWeek(),\n      startOfWeek: loc.getStartOfWeek(),\n    };\n  } else {\n    return { minDaysInFirstWeek: 4, startOfWeek: 1 };\n  }\n}\n\nexport function hasInvalidWeekData(obj, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const validYear = isInteger(obj.weekYear),\n    validWeek = integerBetween(\n      obj.weekNumber,\n      1,\n      weeksInWeekYear(obj.weekYear, minDaysInFirstWeek, startOfWeek)\n    ),\n    validWeekday = integerBetween(obj.weekday, 1, 7);\n\n  if (!validYear) {\n    return unitOutOfRange(\"weekYear\", obj.weekYear);\n  } else if (!validWeek) {\n    return unitOutOfRange(\"week\", obj.weekNumber);\n  } else if (!validWeekday) {\n    return unitOutOfRange(\"weekday\", obj.weekday);\n  } else return false;\n}\n\nexport function hasInvalidOrdinalData(obj) {\n  const validYear = isInteger(obj.year),\n    validOrdinal = integerBetween(obj.ordinal, 1, daysInYear(obj.year));\n\n  if (!validYear) {\n    return unitOutOfRange(\"year\", obj.year);\n  } else if (!validOrdinal) {\n    return unitOutOfRange(\"ordinal\", obj.ordinal);\n  } else return false;\n}\n\nexport function hasInvalidGregorianData(obj) {\n  const validYear = isInteger(obj.year),\n    validMonth = integerBetween(obj.month, 1, 12),\n    validDay = integerBetween(obj.day, 1, daysInMonth(obj.year, obj.month));\n\n  if (!validYear) {\n    return unitOutOfRange(\"year\", obj.year);\n  } else if (!validMonth) {\n    return unitOutOfRange(\"month\", obj.month);\n  } else if (!validDay) {\n    return unitOutOfRange(\"day\", obj.day);\n  } else return false;\n}\n\nexport function hasInvalidTimeData(obj) {\n  const { hour, minute, second, millisecond } = obj;\n  const validHour =\n      integerBetween(hour, 0, 23) ||\n      (hour === 24 && minute === 0 && second === 0 && millisecond === 0),\n    validMinute = integerBetween(minute, 0, 59),\n    validSecond = integerBetween(second, 0, 59),\n    validMillisecond = integerBetween(millisecond, 0, 999);\n\n  if (!validHour) {\n    return unitOutOfRange(\"hour\", hour);\n  } else if (!validMinute) {\n    return unitOutOfRange(\"minute\", minute);\n  } else if (!validSecond) {\n    return unitOutOfRange(\"second\", second);\n  } else if (!validMillisecond) {\n    return unitOutOfRange(\"millisecond\", millisecond);\n  } else return false;\n}\n", "/*\n  This is just a junk drawer, containing anything used across multiple classes.\n  Because <PERSON>xon is small(ish), this should stay small and we won't worry about splitting\n  it up into, say, parsingUtil.js and basicUtil.js and so on. But they are divided up by feature area.\n*/\n\nimport { InvalidArgumentError } from \"../errors.js\";\nimport Settings from \"../settings.js\";\nimport { dayOfWeek, isoWeekdayToLocal } from \"./conversions.js\";\n\n/**\n * @private\n */\n\n// TYPES\n\nexport function isUndefined(o) {\n  return typeof o === \"undefined\";\n}\n\nexport function isNumber(o) {\n  return typeof o === \"number\";\n}\n\nexport function isInteger(o) {\n  return typeof o === \"number\" && o % 1 === 0;\n}\n\nexport function isString(o) {\n  return typeof o === \"string\";\n}\n\nexport function isDate(o) {\n  return Object.prototype.toString.call(o) === \"[object Date]\";\n}\n\n// CAPABILITIES\n\nexport function hasRelative() {\n  try {\n    return typeof Intl !== \"undefined\" && !!Intl.RelativeTimeFormat;\n  } catch (e) {\n    return false;\n  }\n}\n\nexport function hasLocaleWeekInfo() {\n  try {\n    return (\n      typeof Intl !== \"undefined\" &&\n      !!Intl.Locale &&\n      (\"weekInfo\" in Intl.Locale.prototype || \"getWeekInfo\" in Intl.Locale.prototype)\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\n// OBJECTS AND ARRAYS\n\nexport function maybeArray(thing) {\n  return Array.isArray(thing) ? thing : [thing];\n}\n\nexport function bestBy(arr, by, compare) {\n  if (arr.length === 0) {\n    return undefined;\n  }\n  return arr.reduce((best, next) => {\n    const pair = [by(next), next];\n    if (!best) {\n      return pair;\n    } else if (compare(best[0], pair[0]) === best[0]) {\n      return best;\n    } else {\n      return pair;\n    }\n  }, null)[1];\n}\n\nexport function pick(obj, keys) {\n  return keys.reduce((a, k) => {\n    a[k] = obj[k];\n    return a;\n  }, {});\n}\n\nexport function hasOwnProperty(obj, prop) {\n  return Object.prototype.hasOwnProperty.call(obj, prop);\n}\n\nexport function validateWeekSettings(settings) {\n  if (settings == null) {\n    return null;\n  } else if (typeof settings !== \"object\") {\n    throw new InvalidArgumentError(\"Week settings must be an object\");\n  } else {\n    if (\n      !integerBetween(settings.firstDay, 1, 7) ||\n      !integerBetween(settings.minimalDays, 1, 7) ||\n      !Array.isArray(settings.weekend) ||\n      settings.weekend.some((v) => !integerBetween(v, 1, 7))\n    ) {\n      throw new InvalidArgumentError(\"Invalid week settings\");\n    }\n    return {\n      firstDay: settings.firstDay,\n      minimalDays: settings.minimalDays,\n      weekend: Array.from(settings.weekend),\n    };\n  }\n}\n\n// NUMBERS AND STRINGS\n\nexport function integerBetween(thing, bottom, top) {\n  return isInteger(thing) && thing >= bottom && thing <= top;\n}\n\n// x % n but takes the sign of n instead of x\nexport function floorMod(x, n) {\n  return x - n * Math.floor(x / n);\n}\n\nexport function padStart(input, n = 2) {\n  const isNeg = input < 0;\n  let padded;\n  if (isNeg) {\n    padded = \"-\" + (\"\" + -input).padStart(n, \"0\");\n  } else {\n    padded = (\"\" + input).padStart(n, \"0\");\n  }\n  return padded;\n}\n\nexport function parseInteger(string) {\n  if (isUndefined(string) || string === null || string === \"\") {\n    return undefined;\n  } else {\n    return parseInt(string, 10);\n  }\n}\n\nexport function parseFloating(string) {\n  if (isUndefined(string) || string === null || string === \"\") {\n    return undefined;\n  } else {\n    return parseFloat(string);\n  }\n}\n\nexport function parseMillis(fraction) {\n  // Return undefined (instead of 0) in these cases, where fraction is not set\n  if (isUndefined(fraction) || fraction === null || fraction === \"\") {\n    return undefined;\n  } else {\n    const f = parseFloat(\"0.\" + fraction) * 1000;\n    return Math.floor(f);\n  }\n}\n\nexport function roundTo(number, digits, rounding = \"round\") {\n  const factor = 10 ** digits;\n  switch (rounding) {\n    case \"expand\":\n      return number > 0\n        ? Math.ceil(number * factor) / factor\n        : Math.floor(number * factor) / factor;\n    case \"trunc\":\n      return Math.trunc(number * factor) / factor;\n    case \"round\":\n      return Math.round(number * factor) / factor;\n    case \"floor\":\n      return Math.floor(number * factor) / factor;\n    case \"ceil\":\n      return Math.ceil(number * factor) / factor;\n    default:\n      throw new RangeError(`Value rounding ${rounding} is out of range`);\n  }\n}\n\n// DATE BASICS\n\nexport function isLeapYear(year) {\n  return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);\n}\n\nexport function daysInYear(year) {\n  return isLeapYear(year) ? 366 : 365;\n}\n\nexport function daysInMonth(year, month) {\n  const modMonth = floorMod(month - 1, 12) + 1,\n    modYear = year + (month - modMonth) / 12;\n\n  if (modMonth === 2) {\n    return isLeapYear(modYear) ? 29 : 28;\n  } else {\n    return [31, null, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][modMonth - 1];\n  }\n}\n\n// convert a calendar object to a local timestamp (epoch, but with the offset baked in)\nexport function objToLocalTS(obj) {\n  let d = Date.UTC(\n    obj.year,\n    obj.month - 1,\n    obj.day,\n    obj.hour,\n    obj.minute,\n    obj.second,\n    obj.millisecond\n  );\n\n  // for legacy reasons, years between 0 and 99 are interpreted as 19XX; revert that\n  if (obj.year < 100 && obj.year >= 0) {\n    d = new Date(d);\n    // set the month and day again, this is necessary because year 2000 is a leap year, but year 100 is not\n    // so if obj.year is in 99, but obj.day makes it roll over into year 100,\n    // the calculations done by Date.UTC are using year 2000 - which is incorrect\n    d.setUTCFullYear(obj.year, obj.month - 1, obj.day);\n  }\n  return +d;\n}\n\n// adapted from moment.js: https://github.com/moment/moment/blob/000ac1800e620f770f4eb31b5ae908f6167b0ab2/src/lib/units/week-calendar-utils.js\nfunction firstWeekOffset(year, minDaysInFirstWeek, startOfWeek) {\n  const fwdlw = isoWeekdayToLocal(dayOfWeek(year, 1, minDaysInFirstWeek), startOfWeek);\n  return -fwdlw + minDaysInFirstWeek - 1;\n}\n\nexport function weeksInWeekYear(weekYear, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const weekOffset = firstWeekOffset(weekYear, minDaysInFirstWeek, startOfWeek);\n  const weekOffsetNext = firstWeekOffset(weekYear + 1, minDaysInFirstWeek, startOfWeek);\n  return (daysInYear(weekYear) - weekOffset + weekOffsetNext) / 7;\n}\n\nexport function untruncateYear(year) {\n  if (year > 99) {\n    return year;\n  } else return year > Settings.twoDigitCutoffYear ? 1900 + year : 2000 + year;\n}\n\n// PARSING\n\nexport function parseZoneInfo(ts, offsetFormat, locale, timeZone = null) {\n  const date = new Date(ts),\n    intlOpts = {\n      hourCycle: \"h23\",\n      year: \"numeric\",\n      month: \"2-digit\",\n      day: \"2-digit\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    };\n\n  if (timeZone) {\n    intlOpts.timeZone = timeZone;\n  }\n\n  const modified = { timeZoneName: offsetFormat, ...intlOpts };\n\n  const parsed = new Intl.DateTimeFormat(locale, modified)\n    .formatToParts(date)\n    .find((m) => m.type.toLowerCase() === \"timezonename\");\n  return parsed ? parsed.value : null;\n}\n\n// signedOffset('-5', '30') -> -330\nexport function signedOffset(offHourStr, offMinuteStr) {\n  let offHour = parseInt(offHourStr, 10);\n\n  // don't || this because we want to preserve -0\n  if (Number.isNaN(offHour)) {\n    offHour = 0;\n  }\n\n  const offMin = parseInt(offMinuteStr, 10) || 0,\n    offMinSigned = offHour < 0 || Object.is(offHour, -0) ? -offMin : offMin;\n  return offHour * 60 + offMinSigned;\n}\n\n// COERCION\n\nexport function asNumber(value) {\n  const numericValue = Number(value);\n  if (typeof value === \"boolean\" || value === \"\" || !Number.isFinite(numericValue))\n    throw new InvalidArgumentError(`Invalid unit value ${value}`);\n  return numericValue;\n}\n\nexport function normalizeObject(obj, normalizer) {\n  const normalized = {};\n  for (const u in obj) {\n    if (hasOwnProperty(obj, u)) {\n      const v = obj[u];\n      if (v === undefined || v === null) continue;\n      normalized[normalizer(u)] = asNumber(v);\n    }\n  }\n  return normalized;\n}\n\n/**\n * Returns the offset's value as a string\n * @param {number} ts - Epoch milliseconds for which to get the offset\n * @param {string} format - What style of offset to return.\n *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n * @return {string}\n */\nexport function formatOffset(offset, format) {\n  const hours = Math.trunc(Math.abs(offset / 60)),\n    minutes = Math.trunc(Math.abs(offset % 60)),\n    sign = offset >= 0 ? \"+\" : \"-\";\n\n  switch (format) {\n    case \"short\":\n      return `${sign}${padStart(hours, 2)}:${padStart(minutes, 2)}`;\n    case \"narrow\":\n      return `${sign}${hours}${minutes > 0 ? `:${minutes}` : \"\"}`;\n    case \"techie\":\n      return `${sign}${padStart(hours, 2)}${padStart(minutes, 2)}`;\n    default:\n      throw new RangeError(`Value format ${format} is out of range for property format`);\n  }\n}\n\nexport function timeObject(obj) {\n  return pick(obj, [\"hour\", \"minute\", \"second\", \"millisecond\"]);\n}\n", "import * as Formats from \"./formats.js\";\nimport { pick } from \"./util.js\";\n\nfunction stringify(obj) {\n  return JSON.stringify(obj, Object.keys(obj).sort());\n}\n\n/**\n * @private\n */\n\nexport const monthsLong = [\n  \"January\",\n  \"February\",\n  \"March\",\n  \"April\",\n  \"May\",\n  \"June\",\n  \"July\",\n  \"August\",\n  \"September\",\n  \"October\",\n  \"November\",\n  \"December\",\n];\n\nexport const monthsShort = [\n  \"Jan\",\n  \"Feb\",\n  \"Mar\",\n  \"Apr\",\n  \"May\",\n  \"Jun\",\n  \"Jul\",\n  \"Aug\",\n  \"Sep\",\n  \"Oct\",\n  \"Nov\",\n  \"Dec\",\n];\n\nexport const monthsNarrow = [\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"];\n\nexport function months(length) {\n  switch (length) {\n    case \"narrow\":\n      return [...monthsNarrow];\n    case \"short\":\n      return [...monthsShort];\n    case \"long\":\n      return [...monthsLong];\n    case \"numeric\":\n      return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"];\n    case \"2-digit\":\n      return [\"01\", \"02\", \"03\", \"04\", \"05\", \"06\", \"07\", \"08\", \"09\", \"10\", \"11\", \"12\"];\n    default:\n      return null;\n  }\n}\n\nexport const weekdaysLong = [\n  \"Monday\",\n  \"Tuesday\",\n  \"Wednesday\",\n  \"Thursday\",\n  \"Friday\",\n  \"Saturday\",\n  \"Sunday\",\n];\n\nexport const weekdaysShort = [\"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\", \"Sun\"];\n\nexport const weekdaysNarrow = [\"M\", \"T\", \"W\", \"T\", \"F\", \"S\", \"S\"];\n\nexport function weekdays(length) {\n  switch (length) {\n    case \"narrow\":\n      return [...weekdaysNarrow];\n    case \"short\":\n      return [...weekdaysShort];\n    case \"long\":\n      return [...weekdaysLong];\n    case \"numeric\":\n      return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n    default:\n      return null;\n  }\n}\n\nexport const meridiems = [\"AM\", \"PM\"];\n\nexport const erasLong = [\"Before Christ\", \"Anno Domini\"];\n\nexport const erasShort = [\"BC\", \"AD\"];\n\nexport const erasNarrow = [\"B\", \"A\"];\n\nexport function eras(length) {\n  switch (length) {\n    case \"narrow\":\n      return [...erasNarrow];\n    case \"short\":\n      return [...erasShort];\n    case \"long\":\n      return [...erasLong];\n    default:\n      return null;\n  }\n}\n\nexport function meridiemForDateTime(dt) {\n  return meridiems[dt.hour < 12 ? 0 : 1];\n}\n\nexport function weekdayForDateTime(dt, length) {\n  return weekdays(length)[dt.weekday - 1];\n}\n\nexport function monthForDateTime(dt, length) {\n  return months(length)[dt.month - 1];\n}\n\nexport function eraForDateTime(dt, length) {\n  return eras(length)[dt.year < 0 ? 0 : 1];\n}\n\nexport function formatRelativeTime(unit, count, numeric = \"always\", narrow = false) {\n  const units = {\n    years: [\"year\", \"yr.\"],\n    quarters: [\"quarter\", \"qtr.\"],\n    months: [\"month\", \"mo.\"],\n    weeks: [\"week\", \"wk.\"],\n    days: [\"day\", \"day\", \"days\"],\n    hours: [\"hour\", \"hr.\"],\n    minutes: [\"minute\", \"min.\"],\n    seconds: [\"second\", \"sec.\"],\n  };\n\n  const lastable = [\"hours\", \"minutes\", \"seconds\"].indexOf(unit) === -1;\n\n  if (numeric === \"auto\" && lastable) {\n    const isDay = unit === \"days\";\n    switch (count) {\n      case 1:\n        return isDay ? \"tomorrow\" : `next ${units[unit][0]}`;\n      case -1:\n        return isDay ? \"yesterday\" : `last ${units[unit][0]}`;\n      case 0:\n        return isDay ? \"today\" : `this ${units[unit][0]}`;\n      default: // fall through\n    }\n  }\n\n  const isInPast = Object.is(count, -0) || count < 0,\n    fmtValue = Math.abs(count),\n    singular = fmtValue === 1,\n    lilUnits = units[unit],\n    fmtUnit = narrow\n      ? singular\n        ? lilUnits[1]\n        : lilUnits[2] || lilUnits[1]\n      : singular\n      ? units[unit][0]\n      : unit;\n  return isInPast ? `${fmtValue} ${fmtUnit} ago` : `in ${fmtValue} ${fmtUnit}`;\n}\n\nexport function formatString(knownFormat) {\n  // these all have the offsets removed because we don't have access to them\n  // without all the intl stuff this is backfilling\n  const filtered = pick(knownFormat, [\n      \"weekday\",\n      \"era\",\n      \"year\",\n      \"month\",\n      \"day\",\n      \"hour\",\n      \"minute\",\n      \"second\",\n      \"timeZoneName\",\n      \"hourCycle\",\n    ]),\n    key = stringify(filtered),\n    dateTimeHuge = \"EEEE, LLLL d, yyyy, h:mm a\";\n  switch (key) {\n    case stringify(Formats.DATE_SHORT):\n      return \"M/d/yyyy\";\n    case stringify(Formats.DATE_MED):\n      return \"LLL d, yyyy\";\n    case stringify(Formats.DATE_MED_WITH_WEEKDAY):\n      return \"EEE, LLL d, yyyy\";\n    case stringify(Formats.DATE_FULL):\n      return \"LLLL d, yyyy\";\n    case stringify(Formats.DATE_HUGE):\n      return \"EEEE, LLLL d, yyyy\";\n    case stringify(Formats.TIME_SIMPLE):\n      return \"h:mm a\";\n    case stringify(Formats.TIME_WITH_SECONDS):\n      return \"h:mm:ss a\";\n    case stringify(Formats.TIME_WITH_SHORT_OFFSET):\n      return \"h:mm a\";\n    case stringify(Formats.TIME_WITH_LONG_OFFSET):\n      return \"h:mm a\";\n    case stringify(Formats.TIME_24_SIMPLE):\n      return \"HH:mm\";\n    case stringify(Formats.TIME_24_WITH_SECONDS):\n      return \"HH:mm:ss\";\n    case stringify(Formats.TIME_24_WITH_SHORT_OFFSET):\n      return \"HH:mm\";\n    case stringify(Formats.TIME_24_WITH_LONG_OFFSET):\n      return \"HH:mm\";\n    case stringify(Formats.DATETIME_SHORT):\n      return \"M/d/yyyy, h:mm a\";\n    case stringify(Formats.DATETIME_MED):\n      return \"LLL d, yyyy, h:mm a\";\n    case stringify(Formats.DATETIME_FULL):\n      return \"LLLL d, yyyy, h:mm a\";\n    case stringify(Formats.DATETIME_HUGE):\n      return dateTimeHuge;\n    case stringify(Formats.DATETIME_SHORT_WITH_SECONDS):\n      return \"M/d/yyyy, h:mm:ss a\";\n    case stringify(Formats.DATETIME_MED_WITH_SECONDS):\n      return \"LLL d, yyyy, h:mm:ss a\";\n    case stringify(Formats.DATETIME_MED_WITH_WEEKDAY):\n      return \"EEE, d LLL yyyy, h:mm a\";\n    case stringify(Formats.DATETIME_FULL_WITH_SECONDS):\n      return \"LLLL d, yyyy, h:mm:ss a\";\n    case stringify(Formats.DATETIME_HUGE_WITH_SECONDS):\n      return \"EEEE, LLLL d, yyyy, h:mm:ss a\";\n    default:\n      return dateTimeHuge;\n  }\n}\n", "import * as English from \"./english.js\";\nimport * as Formats from \"./formats.js\";\nimport { padStart } from \"./util.js\";\n\nfunction stringifyTokens(splits, tokenToString) {\n  let s = \"\";\n  for (const token of splits) {\n    if (token.literal) {\n      s += token.val;\n    } else {\n      s += tokenToString(token.val);\n    }\n  }\n  return s;\n}\n\nconst macroTokenToFormatOpts = {\n  D: Formats.DATE_SHORT,\n  DD: Formats.DATE_MED,\n  DDD: Formats.DATE_FULL,\n  DDDD: Formats.DATE_HUGE,\n  t: Formats.TIME_SIMPLE,\n  tt: Formats.TIME_WITH_SECONDS,\n  ttt: Formats.TIME_WITH_SHORT_OFFSET,\n  tttt: Formats.TIME_WITH_LONG_OFFSET,\n  T: Formats.TIME_24_SIMPLE,\n  TT: Formats.TIME_24_WITH_SECONDS,\n  TTT: Formats.TIME_24_WITH_SHORT_OFFSET,\n  TTTT: Formats.TIME_24_WITH_LONG_OFFSET,\n  f: Formats.DATETIME_SHORT,\n  ff: Formats.DATETIME_MED,\n  fff: Formats.DATETIME_FULL,\n  ffff: Formats.DATETIME_HUGE,\n  F: Formats.DATETIME_SHORT_WITH_SECONDS,\n  FF: Formats.DATETIME_MED_WITH_SECONDS,\n  FFF: Formats.DATETIME_FULL_WITH_SECONDS,\n  FFFF: Formats.DATETIME_HUGE_WITH_SECONDS,\n};\n\n/**\n * @private\n */\n\nexport default class Formatter {\n  static create(locale, opts = {}) {\n    return new Formatter(locale, opts);\n  }\n\n  static parseFormat(fmt) {\n    // white-space is always considered a literal in user-provided formats\n    // the \" \" token has a special meaning (see unitForToken)\n\n    let current = null,\n      currentFull = \"\",\n      bracketed = false;\n    const splits = [];\n    for (let i = 0; i < fmt.length; i++) {\n      const c = fmt.charAt(i);\n      if (c === \"'\") {\n        // turn '' into a literal signal quote instead of just skipping the empty literal\n        if (currentFull.length > 0 || bracketed) {\n          splits.push({\n            literal: bracketed || /^\\s+$/.test(currentFull),\n            val: currentFull === \"\" ? \"'\" : currentFull,\n          });\n        }\n        current = null;\n        currentFull = \"\";\n        bracketed = !bracketed;\n      } else if (bracketed) {\n        currentFull += c;\n      } else if (c === current) {\n        currentFull += c;\n      } else {\n        if (currentFull.length > 0) {\n          splits.push({ literal: /^\\s+$/.test(currentFull), val: currentFull });\n        }\n        currentFull = c;\n        current = c;\n      }\n    }\n\n    if (currentFull.length > 0) {\n      splits.push({ literal: bracketed || /^\\s+$/.test(currentFull), val: currentFull });\n    }\n\n    return splits;\n  }\n\n  static macroTokenToFormatOpts(token) {\n    return macroTokenToFormatOpts[token];\n  }\n\n  constructor(locale, formatOpts) {\n    this.opts = formatOpts;\n    this.loc = locale;\n    this.systemLoc = null;\n  }\n\n  formatWithSystemDefault(dt, opts) {\n    if (this.systemLoc === null) {\n      this.systemLoc = this.loc.redefaultToSystem();\n    }\n    const df = this.systemLoc.dtFormatter(dt, { ...this.opts, ...opts });\n    return df.format();\n  }\n\n  dtFormatter(dt, opts = {}) {\n    return this.loc.dtFormatter(dt, { ...this.opts, ...opts });\n  }\n\n  formatDateTime(dt, opts) {\n    return this.dtFormatter(dt, opts).format();\n  }\n\n  formatDateTimeParts(dt, opts) {\n    return this.dtFormatter(dt, opts).formatToParts();\n  }\n\n  formatInterval(interval, opts) {\n    const df = this.dtFormatter(interval.start, opts);\n    return df.dtf.formatRange(interval.start.toJSDate(), interval.end.toJSDate());\n  }\n\n  resolvedOptions(dt, opts) {\n    return this.dtFormatter(dt, opts).resolvedOptions();\n  }\n\n  num(n, p = 0, signDisplay = undefined) {\n    // we get some perf out of doing this here, annoyingly\n    if (this.opts.forceSimple) {\n      return padStart(n, p);\n    }\n\n    const opts = { ...this.opts };\n\n    if (p > 0) {\n      opts.padTo = p;\n    }\n    if (signDisplay) {\n      opts.signDisplay = signDisplay;\n    }\n\n    return this.loc.numberFormatter(opts).format(n);\n  }\n\n  formatDateTimeFromString(dt, fmt) {\n    const knownEnglish = this.loc.listingMode() === \"en\",\n      useDateTimeFormatter = this.loc.outputCalendar && this.loc.outputCalendar !== \"gregory\",\n      string = (opts, extract) => this.loc.extract(dt, opts, extract),\n      formatOffset = (opts) => {\n        if (dt.isOffsetFixed && dt.offset === 0 && opts.allowZ) {\n          return \"Z\";\n        }\n\n        return dt.isValid ? dt.zone.formatOffset(dt.ts, opts.format) : \"\";\n      },\n      meridiem = () =>\n        knownEnglish\n          ? English.meridiemForDateTime(dt)\n          : string({ hour: \"numeric\", hourCycle: \"h12\" }, \"dayperiod\"),\n      month = (length, standalone) =>\n        knownEnglish\n          ? English.monthForDateTime(dt, length)\n          : string(standalone ? { month: length } : { month: length, day: \"numeric\" }, \"month\"),\n      weekday = (length, standalone) =>\n        knownEnglish\n          ? English.weekdayForDateTime(dt, length)\n          : string(\n              standalone ? { weekday: length } : { weekday: length, month: \"long\", day: \"numeric\" },\n              \"weekday\"\n            ),\n      maybeMacro = (token) => {\n        const formatOpts = Formatter.macroTokenToFormatOpts(token);\n        if (formatOpts) {\n          return this.formatWithSystemDefault(dt, formatOpts);\n        } else {\n          return token;\n        }\n      },\n      era = (length) =>\n        knownEnglish ? English.eraForDateTime(dt, length) : string({ era: length }, \"era\"),\n      tokenToString = (token) => {\n        // Where possible: https://cldr.unicode.org/translation/date-time/date-time-symbols\n        switch (token) {\n          // ms\n          case \"S\":\n            return this.num(dt.millisecond);\n          case \"u\":\n          // falls through\n          case \"SSS\":\n            return this.num(dt.millisecond, 3);\n          // seconds\n          case \"s\":\n            return this.num(dt.second);\n          case \"ss\":\n            return this.num(dt.second, 2);\n          // fractional seconds\n          case \"uu\":\n            return this.num(Math.floor(dt.millisecond / 10), 2);\n          case \"uuu\":\n            return this.num(Math.floor(dt.millisecond / 100));\n          // minutes\n          case \"m\":\n            return this.num(dt.minute);\n          case \"mm\":\n            return this.num(dt.minute, 2);\n          // hours\n          case \"h\":\n            return this.num(dt.hour % 12 === 0 ? 12 : dt.hour % 12);\n          case \"hh\":\n            return this.num(dt.hour % 12 === 0 ? 12 : dt.hour % 12, 2);\n          case \"H\":\n            return this.num(dt.hour);\n          case \"HH\":\n            return this.num(dt.hour, 2);\n          // offset\n          case \"Z\":\n            // like +6\n            return formatOffset({ format: \"narrow\", allowZ: this.opts.allowZ });\n          case \"ZZ\":\n            // like +06:00\n            return formatOffset({ format: \"short\", allowZ: this.opts.allowZ });\n          case \"ZZZ\":\n            // like +0600\n            return formatOffset({ format: \"techie\", allowZ: this.opts.allowZ });\n          case \"ZZZZ\":\n            // like EST\n            return dt.zone.offsetName(dt.ts, { format: \"short\", locale: this.loc.locale });\n          case \"ZZZZZ\":\n            // like Eastern Standard Time\n            return dt.zone.offsetName(dt.ts, { format: \"long\", locale: this.loc.locale });\n          // zone\n          case \"z\":\n            // like America/New_York\n            return dt.zoneName;\n          // meridiems\n          case \"a\":\n            return meridiem();\n          // dates\n          case \"d\":\n            return useDateTimeFormatter ? string({ day: \"numeric\" }, \"day\") : this.num(dt.day);\n          case \"dd\":\n            return useDateTimeFormatter ? string({ day: \"2-digit\" }, \"day\") : this.num(dt.day, 2);\n          // weekdays - standalone\n          case \"c\":\n            // like 1\n            return this.num(dt.weekday);\n          case \"ccc\":\n            // like 'Tues'\n            return weekday(\"short\", true);\n          case \"cccc\":\n            // like 'Tuesday'\n            return weekday(\"long\", true);\n          case \"ccccc\":\n            // like 'T'\n            return weekday(\"narrow\", true);\n          // weekdays - format\n          case \"E\":\n            // like 1\n            return this.num(dt.weekday);\n          case \"EEE\":\n            // like 'Tues'\n            return weekday(\"short\", false);\n          case \"EEEE\":\n            // like 'Tuesday'\n            return weekday(\"long\", false);\n          case \"EEEEE\":\n            // like 'T'\n            return weekday(\"narrow\", false);\n          // months - standalone\n          case \"L\":\n            // like 1\n            return useDateTimeFormatter\n              ? string({ month: \"numeric\", day: \"numeric\" }, \"month\")\n              : this.num(dt.month);\n          case \"LL\":\n            // like 01, doesn't seem to work\n            return useDateTimeFormatter\n              ? string({ month: \"2-digit\", day: \"numeric\" }, \"month\")\n              : this.num(dt.month, 2);\n          case \"LLL\":\n            // like Jan\n            return month(\"short\", true);\n          case \"LLLL\":\n            // like January\n            return month(\"long\", true);\n          case \"LLLLL\":\n            // like J\n            return month(\"narrow\", true);\n          // months - format\n          case \"M\":\n            // like 1\n            return useDateTimeFormatter\n              ? string({ month: \"numeric\" }, \"month\")\n              : this.num(dt.month);\n          case \"MM\":\n            // like 01\n            return useDateTimeFormatter\n              ? string({ month: \"2-digit\" }, \"month\")\n              : this.num(dt.month, 2);\n          case \"MMM\":\n            // like Jan\n            return month(\"short\", false);\n          case \"MMMM\":\n            // like January\n            return month(\"long\", false);\n          case \"MMMMM\":\n            // like J\n            return month(\"narrow\", false);\n          // years\n          case \"y\":\n            // like 2014\n            return useDateTimeFormatter ? string({ year: \"numeric\" }, \"year\") : this.num(dt.year);\n          case \"yy\":\n            // like 14\n            return useDateTimeFormatter\n              ? string({ year: \"2-digit\" }, \"year\")\n              : this.num(dt.year.toString().slice(-2), 2);\n          case \"yyyy\":\n            // like 0012\n            return useDateTimeFormatter\n              ? string({ year: \"numeric\" }, \"year\")\n              : this.num(dt.year, 4);\n          case \"yyyyyy\":\n            // like 000012\n            return useDateTimeFormatter\n              ? string({ year: \"numeric\" }, \"year\")\n              : this.num(dt.year, 6);\n          // eras\n          case \"G\":\n            // like AD\n            return era(\"short\");\n          case \"GG\":\n            // like Anno Domini\n            return era(\"long\");\n          case \"GGGGG\":\n            return era(\"narrow\");\n          case \"kk\":\n            return this.num(dt.weekYear.toString().slice(-2), 2);\n          case \"kkkk\":\n            return this.num(dt.weekYear, 4);\n          case \"W\":\n            return this.num(dt.weekNumber);\n          case \"WW\":\n            return this.num(dt.weekNumber, 2);\n          case \"n\":\n            return this.num(dt.localWeekNumber);\n          case \"nn\":\n            return this.num(dt.localWeekNumber, 2);\n          case \"ii\":\n            return this.num(dt.localWeekYear.toString().slice(-2), 2);\n          case \"iiii\":\n            return this.num(dt.localWeekYear, 4);\n          case \"o\":\n            return this.num(dt.ordinal);\n          case \"ooo\":\n            return this.num(dt.ordinal, 3);\n          case \"q\":\n            // like 1\n            return this.num(dt.quarter);\n          case \"qq\":\n            // like 01\n            return this.num(dt.quarter, 2);\n          case \"X\":\n            return this.num(Math.floor(dt.ts / 1000));\n          case \"x\":\n            return this.num(dt.ts);\n          default:\n            return maybeMacro(token);\n        }\n      };\n\n    return stringifyTokens(Formatter.parseFormat(fmt), tokenToString);\n  }\n\n  formatDurationFromString(dur, fmt) {\n    const invertLargest = this.opts.signMode === \"negativeLargestOnly\" ? -1 : 1;\n    const tokenToField = (token) => {\n        switch (token[0]) {\n          case \"S\":\n            return \"milliseconds\";\n          case \"s\":\n            return \"seconds\";\n          case \"m\":\n            return \"minutes\";\n          case \"h\":\n            return \"hours\";\n          case \"d\":\n            return \"days\";\n          case \"w\":\n            return \"weeks\";\n          case \"M\":\n            return \"months\";\n          case \"y\":\n            return \"years\";\n          default:\n            return null;\n        }\n      },\n      tokenToString = (lildur, info) => (token) => {\n        const mapped = tokenToField(token);\n        if (mapped) {\n          const inversionFactor =\n            info.isNegativeDuration && mapped !== info.largestUnit ? invertLargest : 1;\n          let signDisplay;\n          if (this.opts.signMode === \"negativeLargestOnly\" && mapped !== info.largestUnit) {\n            signDisplay = \"never\";\n          } else if (this.opts.signMode === \"all\") {\n            signDisplay = \"always\";\n          } else {\n            // \"auto\" and \"negative\" are the same, but \"auto\" has better support\n            signDisplay = \"auto\";\n          }\n          return this.num(lildur.get(mapped) * inversionFactor, token.length, signDisplay);\n        } else {\n          return token;\n        }\n      },\n      tokens = Formatter.parseFormat(fmt),\n      realTokens = tokens.reduce(\n        (found, { literal, val }) => (literal ? found : found.concat(val)),\n        []\n      ),\n      collapsed = dur.shiftTo(...realTokens.map(tokenToField).filter((t) => t)),\n      durationInfo = {\n        isNegativeDuration: collapsed < 0,\n        // this relies on \"collapsed\" being based on \"shiftTo\", which builds up the object\n        // in order\n        largestUnit: Object.keys(collapsed.values)[0],\n      };\n    return stringifyTokens(tokens, tokenToString(collapsed, durationInfo));\n  }\n}\n", "import {\n  untruncate<PERSON>ear,\n  signed<PERSON>ffset,\n  parseInteger,\n  parse<PERSON>illis,\n  isUndefined,\n  parseFloating,\n} from \"./util.js\";\nimport * as English from \"./english.js\";\nimport FixedOffsetZone from \"../zones/fixedOffsetZone.js\";\nimport IANAZone from \"../zones/IANAZone.js\";\n\n/*\n * This file handles parsing for well-specified formats. Here's how it works:\n * Two things go into parsing: a regex to match with and an extractor to take apart the groups in the match.\n * An extractor is just a function that takes a regex match array and returns a { year: ..., month: ... } object\n * parse() does the work of executing the regex and applying the extractor. It takes multiple regex/extractor pairs to try in sequence.\n * Extractors can take a \"cursor\" representing the offset in the match to look at. This makes it easy to combine extractors.\n * combineExtractors() does the work of combining them, keeping track of the cursor through multiple extractions.\n * Some extractions are super dumb and simpleParse and fromStrings help DRY them.\n */\n\nconst ianaRegex = /[A-Za-z_+-]{1,256}(?::?\\/[A-Za-z0-9_+-]{1,256}(?:\\/[A-Za-z0-9_+-]{1,256})?)?/;\n\nfunction combineRegexes(...regexes) {\n  const full = regexes.reduce((f, r) => f + r.source, \"\");\n  return RegExp(`^${full}$`);\n}\n\nfunction combineExtractors(...extractors) {\n  return (m) =>\n    extractors\n      .reduce(\n        ([mergedVals, mergedZone, cursor], ex) => {\n          const [val, zone, next] = ex(m, cursor);\n          return [{ ...mergedVals, ...val }, zone || mergedZone, next];\n        },\n        [{}, null, 1]\n      )\n      .slice(0, 2);\n}\n\nfunction parse(s, ...patterns) {\n  if (s == null) {\n    return [null, null];\n  }\n\n  for (const [regex, extractor] of patterns) {\n    const m = regex.exec(s);\n    if (m) {\n      return extractor(m);\n    }\n  }\n  return [null, null];\n}\n\nfunction simpleParse(...keys) {\n  return (match, cursor) => {\n    const ret = {};\n    let i;\n\n    for (i = 0; i < keys.length; i++) {\n      ret[keys[i]] = parseInteger(match[cursor + i]);\n    }\n    return [ret, null, cursor + i];\n  };\n}\n\n// ISO and SQL parsing\nconst offsetRegex = /(?:([Zz])|([+-]\\d\\d)(?::?(\\d\\d))?)/;\nconst isoExtendedZone = `(?:${offsetRegex.source}?(?:\\\\[(${ianaRegex.source})\\\\])?)?`;\nconst isoTimeBaseRegex = /(\\d\\d)(?::?(\\d\\d)(?::?(\\d\\d)(?:[.,](\\d{1,30}))?)?)?/;\nconst isoTimeRegex = RegExp(`${isoTimeBaseRegex.source}${isoExtendedZone}`);\nconst isoTimeExtensionRegex = RegExp(`(?:[Tt]${isoTimeRegex.source})?`);\nconst isoYmdRegex = /([+-]\\d{6}|\\d{4})(?:-?(\\d\\d)(?:-?(\\d\\d))?)?/;\nconst isoWeekRegex = /(\\d{4})-?W(\\d\\d)(?:-?(\\d))?/;\nconst isoOrdinalRegex = /(\\d{4})-?(\\d{3})/;\nconst extractISOWeekData = simpleParse(\"weekYear\", \"weekNumber\", \"weekDay\");\nconst extractISOOrdinalData = simpleParse(\"year\", \"ordinal\");\nconst sqlYmdRegex = /(\\d{4})-(\\d\\d)-(\\d\\d)/; // dumbed-down version of the ISO one\nconst sqlTimeRegex = RegExp(\n  `${isoTimeBaseRegex.source} ?(?:${offsetRegex.source}|(${ianaRegex.source}))?`\n);\nconst sqlTimeExtensionRegex = RegExp(`(?: ${sqlTimeRegex.source})?`);\n\nfunction int(match, pos, fallback) {\n  const m = match[pos];\n  return isUndefined(m) ? fallback : parseInteger(m);\n}\n\nfunction extractISOYmd(match, cursor) {\n  const item = {\n    year: int(match, cursor),\n    month: int(match, cursor + 1, 1),\n    day: int(match, cursor + 2, 1),\n  };\n\n  return [item, null, cursor + 3];\n}\n\nfunction extractISOTime(match, cursor) {\n  const item = {\n    hours: int(match, cursor, 0),\n    minutes: int(match, cursor + 1, 0),\n    seconds: int(match, cursor + 2, 0),\n    milliseconds: parseMillis(match[cursor + 3]),\n  };\n\n  return [item, null, cursor + 4];\n}\n\nfunction extractISOOffset(match, cursor) {\n  const local = !match[cursor] && !match[cursor + 1],\n    fullOffset = signedOffset(match[cursor + 1], match[cursor + 2]),\n    zone = local ? null : FixedOffsetZone.instance(fullOffset);\n  return [{}, zone, cursor + 3];\n}\n\nfunction extractIANAZone(match, cursor) {\n  const zone = match[cursor] ? IANAZone.create(match[cursor]) : null;\n  return [{}, zone, cursor + 1];\n}\n\n// ISO time parsing\n\nconst isoTimeOnly = RegExp(`^T?${isoTimeBaseRegex.source}$`);\n\n// ISO duration parsing\n\nconst isoDuration =\n  /^-?P(?:(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)Y)?(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)M)?(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)W)?(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)D)?(?:T(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)H)?(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)M)?(?:(-?\\d{1,20})(?:[.,](-?\\d{1,20}))?S)?)?)$/;\n\nfunction extractISODuration(match) {\n  const [s, yearStr, monthStr, weekStr, dayStr, hourStr, minuteStr, secondStr, millisecondsStr] =\n    match;\n\n  const hasNegativePrefix = s[0] === \"-\";\n  const negativeSeconds = secondStr && secondStr[0] === \"-\";\n\n  const maybeNegate = (num, force = false) =>\n    num !== undefined && (force || (num && hasNegativePrefix)) ? -num : num;\n\n  return [\n    {\n      years: maybeNegate(parseFloating(yearStr)),\n      months: maybeNegate(parseFloating(monthStr)),\n      weeks: maybeNegate(parseFloating(weekStr)),\n      days: maybeNegate(parseFloating(dayStr)),\n      hours: maybeNegate(parseFloating(hourStr)),\n      minutes: maybeNegate(parseFloating(minuteStr)),\n      seconds: maybeNegate(parseFloating(secondStr), secondStr === \"-0\"),\n      milliseconds: maybeNegate(parseMillis(millisecondsStr), negativeSeconds),\n    },\n  ];\n}\n\n// These are a little braindead. EDT *should* tell us that we're in, say, America/New_York\n// and not just that we're in -240 *right now*. But since I don't think these are used that often\n// I'm just going to ignore that\nconst obsOffsets = {\n  GMT: 0,\n  EDT: -4 * 60,\n  EST: -5 * 60,\n  CDT: -5 * 60,\n  CST: -6 * 60,\n  MDT: -6 * 60,\n  MST: -7 * 60,\n  PDT: -7 * 60,\n  PST: -8 * 60,\n};\n\nfunction fromStrings(weekdayStr, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr) {\n  const result = {\n    year: yearStr.length === 2 ? untruncateYear(parseInteger(yearStr)) : parseInteger(yearStr),\n    month: English.monthsShort.indexOf(monthStr) + 1,\n    day: parseInteger(dayStr),\n    hour: parseInteger(hourStr),\n    minute: parseInteger(minuteStr),\n  };\n\n  if (secondStr) result.second = parseInteger(secondStr);\n  if (weekdayStr) {\n    result.weekday =\n      weekdayStr.length > 3\n        ? English.weekdaysLong.indexOf(weekdayStr) + 1\n        : English.weekdaysShort.indexOf(weekdayStr) + 1;\n  }\n\n  return result;\n}\n\n// RFC 2822/5322\nconst rfc2822 =\n  /^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\\s)?(\\d{1,2})\\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\\s(\\d{2,4})\\s(\\d\\d):(\\d\\d)(?::(\\d\\d))?\\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\\d\\d)(\\d\\d)))$/;\n\nfunction extractRFC2822(match) {\n  const [\n      ,\n      weekdayStr,\n      dayStr,\n      monthStr,\n      yearStr,\n      hourStr,\n      minuteStr,\n      secondStr,\n      obsOffset,\n      milOffset,\n      offHourStr,\n      offMinuteStr,\n    ] = match,\n    result = fromStrings(weekdayStr, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr);\n\n  let offset;\n  if (obsOffset) {\n    offset = obsOffsets[obsOffset];\n  } else if (milOffset) {\n    offset = 0;\n  } else {\n    offset = signedOffset(offHourStr, offMinuteStr);\n  }\n\n  return [result, new FixedOffsetZone(offset)];\n}\n\nfunction preprocessRFC2822(s) {\n  // Remove comments and folding whitespace and replace multiple-spaces with a single space\n  return s\n    .replace(/\\([^()]*\\)|[\\n\\t]/g, \" \")\n    .replace(/(\\s\\s+)/g, \" \")\n    .trim();\n}\n\n// http date\n\nconst rfc1123 =\n    /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\\d\\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\\d{4}) (\\d\\d):(\\d\\d):(\\d\\d) GMT$/,\n  rfc850 =\n    /^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\\d\\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\\d\\d) (\\d\\d):(\\d\\d):(\\d\\d) GMT$/,\n  ascii =\n    /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \\d|\\d\\d) (\\d\\d):(\\d\\d):(\\d\\d) (\\d{4})$/;\n\nfunction extractRFC1123Or850(match) {\n  const [, weekdayStr, dayStr, monthStr, yearStr, hourStr, minuteStr, secondStr] = match,\n    result = fromStrings(weekdayStr, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr);\n  return [result, FixedOffsetZone.utcInstance];\n}\n\nfunction extractASCII(match) {\n  const [, weekdayStr, monthStr, dayStr, hourStr, minuteStr, secondStr, yearStr] = match,\n    result = fromStrings(weekdayStr, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr);\n  return [result, FixedOffsetZone.utcInstance];\n}\n\nconst isoYmdWithTimeExtensionRegex = combineRegexes(isoYmdRegex, isoTimeExtensionRegex);\nconst isoWeekWithTimeExtensionRegex = combineRegexes(isoWeekRegex, isoTimeExtensionRegex);\nconst isoOrdinalWithTimeExtensionRegex = combineRegexes(isoOrdinalRegex, isoTimeExtensionRegex);\nconst isoTimeCombinedRegex = combineRegexes(isoTimeRegex);\n\nconst extractISOYmdTimeAndOffset = combineExtractors(\n  extractISOYmd,\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\nconst extractISOWeekTimeAndOffset = combineExtractors(\n  extractISOWeekData,\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\nconst extractISOOrdinalDateAndTime = combineExtractors(\n  extractISOOrdinalData,\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\nconst extractISOTimeAndOffset = combineExtractors(\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\n\n/*\n * @private\n */\n\nexport function parseISODate(s) {\n  return parse(\n    s,\n    [isoYmdWithTimeExtensionRegex, extractISOYmdTimeAndOffset],\n    [isoWeekWithTimeExtensionRegex, extractISOWeekTimeAndOffset],\n    [isoOrdinalWithTimeExtensionRegex, extractISOOrdinalDateAndTime],\n    [isoTimeCombinedRegex, extractISOTimeAndOffset]\n  );\n}\n\nexport function parseRFC2822Date(s) {\n  return parse(preprocessRFC2822(s), [rfc2822, extractRFC2822]);\n}\n\nexport function parseHTTPDate(s) {\n  return parse(\n    s,\n    [rfc1123, extractRFC1123Or850],\n    [rfc850, extractRFC1123Or850],\n    [ascii, extractASCII]\n  );\n}\n\nexport function parseISODuration(s) {\n  return parse(s, [isoDuration, extractISODuration]);\n}\n\nconst extractISOTimeOnly = combineExtractors(extractISOTime);\n\nexport function parseISOTimeOnly(s) {\n  return parse(s, [isoTimeOnly, extractISOTimeOnly]);\n}\n\nconst sqlYmdWithTimeExtensionRegex = combineRegexes(sqlYmdRegex, sqlTimeExtensionRegex);\nconst sqlTimeCombinedRegex = combineRegexes(sqlTimeRegex);\n\nconst extractISOTimeOffsetAndIANAZone = combineExtractors(\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\n\nexport function parseSQL(s) {\n  return parse(\n    s,\n    [sqlYmdWithTimeExtensionRegex, extractISOYmdTimeAndOffset],\n    [sqlTimeCombinedRegex, extractISOTimeOffsetAndIANAZone]\n  );\n}\n", "import { InvalidArgumentError, InvalidDurationError, InvalidUnitError } from \"./errors.js\";\nimport Formatter from \"./impl/formatter.js\";\nimport Invalid from \"./impl/invalid.js\";\nimport Locale from \"./impl/locale.js\";\nimport { parseISODuration, parseISOTimeOnly } from \"./impl/regexParser.js\";\nimport {\n  asNumber,\n  hasOwnProperty,\n  isNumber,\n  isUndefined,\n  normalizeObject,\n  roundTo,\n} from \"./impl/util.js\";\nimport Settings from \"./settings.js\";\nimport DateTime from \"./datetime.js\";\n\nconst INVALID = \"Invalid Duration\";\n\n// unit conversion constants\nexport const lowOrderMatrix = {\n    weeks: {\n      days: 7,\n      hours: 7 * 24,\n      minutes: 7 * 24 * 60,\n      seconds: 7 * 24 * 60 * 60,\n      milliseconds: 7 * 24 * 60 * 60 * 1000,\n    },\n    days: {\n      hours: 24,\n      minutes: 24 * 60,\n      seconds: 24 * 60 * 60,\n      milliseconds: 24 * 60 * 60 * 1000,\n    },\n    hours: { minutes: 60, seconds: 60 * 60, milliseconds: 60 * 60 * 1000 },\n    minutes: { seconds: 60, milliseconds: 60 * 1000 },\n    seconds: { milliseconds: 1000 },\n  },\n  casualMatrix = {\n    years: {\n      quarters: 4,\n      months: 12,\n      weeks: 52,\n      days: 365,\n      hours: 365 * 24,\n      minutes: 365 * 24 * 60,\n      seconds: 365 * 24 * 60 * 60,\n      milliseconds: 365 * 24 * 60 * 60 * 1000,\n    },\n    quarters: {\n      months: 3,\n      weeks: 13,\n      days: 91,\n      hours: 91 * 24,\n      minutes: 91 * 24 * 60,\n      seconds: 91 * 24 * 60 * 60,\n      milliseconds: 91 * 24 * 60 * 60 * 1000,\n    },\n    months: {\n      weeks: 4,\n      days: 30,\n      hours: 30 * 24,\n      minutes: 30 * 24 * 60,\n      seconds: 30 * 24 * 60 * 60,\n      milliseconds: 30 * 24 * 60 * 60 * 1000,\n    },\n\n    ...lowOrderMatrix,\n  },\n  daysInYearAccurate = 146097.0 / 400,\n  daysInMonthAccurate = 146097.0 / 4800,\n  accurateMatrix = {\n    years: {\n      quarters: 4,\n      months: 12,\n      weeks: daysInYearAccurate / 7,\n      days: daysInYearAccurate,\n      hours: daysInYearAccurate * 24,\n      minutes: daysInYearAccurate * 24 * 60,\n      seconds: daysInYearAccurate * 24 * 60 * 60,\n      milliseconds: daysInYearAccurate * 24 * 60 * 60 * 1000,\n    },\n    quarters: {\n      months: 3,\n      weeks: daysInYearAccurate / 28,\n      days: daysInYearAccurate / 4,\n      hours: (daysInYearAccurate * 24) / 4,\n      minutes: (daysInYearAccurate * 24 * 60) / 4,\n      seconds: (daysInYearAccurate * 24 * 60 * 60) / 4,\n      milliseconds: (daysInYearAccurate * 24 * 60 * 60 * 1000) / 4,\n    },\n    months: {\n      weeks: daysInMonthAccurate / 7,\n      days: daysInMonthAccurate,\n      hours: daysInMonthAccurate * 24,\n      minutes: daysInMonthAccurate * 24 * 60,\n      seconds: daysInMonthAccurate * 24 * 60 * 60,\n      milliseconds: daysInMonthAccurate * 24 * 60 * 60 * 1000,\n    },\n    ...lowOrderMatrix,\n  };\n\n// units ordered by size\nconst orderedUnits = [\n  \"years\",\n  \"quarters\",\n  \"months\",\n  \"weeks\",\n  \"days\",\n  \"hours\",\n  \"minutes\",\n  \"seconds\",\n  \"milliseconds\",\n];\n\nconst reverseUnits = orderedUnits.slice(0).reverse();\n\n// clone really means \"create another instance just like this one, but with these changes\"\nfunction clone(dur, alts, clear = false) {\n  // deep merge for vals\n  const conf = {\n    values: clear ? alts.values : { ...dur.values, ...(alts.values || {}) },\n    loc: dur.loc.clone(alts.loc),\n    conversionAccuracy: alts.conversionAccuracy || dur.conversionAccuracy,\n    matrix: alts.matrix || dur.matrix,\n  };\n  return new Duration(conf);\n}\n\nfunction durationToMillis(matrix, vals) {\n  let sum = vals.milliseconds ?? 0;\n  for (const unit of reverseUnits.slice(1)) {\n    if (vals[unit]) {\n      sum += vals[unit] * matrix[unit][\"milliseconds\"];\n    }\n  }\n  return sum;\n}\n\n// NB: mutates parameters\nfunction normalizeValues(matrix, vals) {\n  // the logic below assumes the overall value of the duration is positive\n  // if this is not the case, factor is used to make it so\n  const factor = durationToMillis(matrix, vals) < 0 ? -1 : 1;\n\n  orderedUnits.reduceRight((previous, current) => {\n    if (!isUndefined(vals[current])) {\n      if (previous) {\n        const previousVal = vals[previous] * factor;\n        const conv = matrix[current][previous];\n\n        // if (previousVal < 0):\n        // lower order unit is negative (e.g. { years: 2, days: -2 })\n        // normalize this by reducing the higher order unit by the appropriate amount\n        // and increasing the lower order unit\n        // this can never make the higher order unit negative, because this function only operates\n        // on positive durations, so the amount of time represented by the lower order unit cannot\n        // be larger than the higher order unit\n        // else:\n        // lower order unit is positive (e.g. { years: 2, days: 450 } or { years: -2, days: 450 })\n        // in this case we attempt to convert as much as possible from the lower order unit into\n        // the higher order one\n        //\n        // Math.floor takes care of both of these cases, rounding away from 0\n        // if previousVal < 0 it makes the absolute value larger\n        // if previousVal >= it makes the absolute value smaller\n        const rollUp = Math.floor(previousVal / conv);\n        vals[current] += rollUp * factor;\n        vals[previous] -= rollUp * conv * factor;\n      }\n      return current;\n    } else {\n      return previous;\n    }\n  }, null);\n\n  // try to convert any decimals into smaller units if possible\n  // for example for { years: 2.5, days: 0, seconds: 0 } we want to get { years: 2, days: 182, hours: 12 }\n  orderedUnits.reduce((previous, current) => {\n    if (!isUndefined(vals[current])) {\n      if (previous) {\n        const fraction = vals[previous] % 1;\n        vals[previous] -= fraction;\n        vals[current] += fraction * matrix[previous][current];\n      }\n      return current;\n    } else {\n      return previous;\n    }\n  }, null);\n}\n\n// Remove all properties with a value of 0 from an object\nfunction removeZeroes(vals) {\n  const newVals = {};\n  for (const [key, value] of Object.entries(vals)) {\n    if (value !== 0) {\n      newVals[key] = value;\n    }\n  }\n  return newVals;\n}\n\n/**\n * A Duration object represents a period of time, like \"2 months\" or \"1 day, 1 hour\". Conceptually, it's just a map of units to their quantities, accompanied by some additional configuration and methods for creating, parsing, interrogating, transforming, and formatting them. They can be used on their own or in conjunction with other Luxon types; for example, you can use {@link DateTime#plus} to add a Duration object to a DateTime, producing another DateTime.\n *\n * Here is a brief overview of commonly used methods and getters in Duration:\n *\n * * **Creation** To create a Duration, use {@link Duration.fromMillis}, {@link Duration.fromObject}, or {@link Duration.fromISO}.\n * * **Unit values** See the {@link Duration#years}, {@link Duration#months}, {@link Duration#weeks}, {@link Duration#days}, {@link Duration#hours}, {@link Duration#minutes}, {@link Duration#seconds}, {@link Duration#milliseconds} accessors.\n * * **Configuration** See  {@link Duration#locale} and {@link Duration#numberingSystem} accessors.\n * * **Transformation** To create new Durations out of old ones use {@link Duration#plus}, {@link Duration#minus}, {@link Duration#normalize}, {@link Duration#set}, {@link Duration#reconfigure}, {@link Duration#shiftTo}, and {@link Duration#negate}.\n * * **Output** To convert the Duration into other representations, see {@link Duration#as}, {@link Duration#toISO}, {@link Duration#toFormat}, and {@link Duration#toJSON}\n *\n * There's are more methods documented below. In addition, for more information on subtler topics like internationalization and validity, see the external documentation.\n */\nexport default class Duration {\n  /**\n   * @private\n   */\n  constructor(config) {\n    const accurate = config.conversionAccuracy === \"longterm\" || false;\n    let matrix = accurate ? accurateMatrix : casualMatrix;\n\n    if (config.matrix) {\n      matrix = config.matrix;\n    }\n\n    /**\n     * @access private\n     */\n    this.values = config.values;\n    /**\n     * @access private\n     */\n    this.loc = config.loc || Locale.create();\n    /**\n     * @access private\n     */\n    this.conversionAccuracy = accurate ? \"longterm\" : \"casual\";\n    /**\n     * @access private\n     */\n    this.invalid = config.invalid || null;\n    /**\n     * @access private\n     */\n    this.matrix = matrix;\n    /**\n     * @access private\n     */\n    this.isLuxonDuration = true;\n  }\n\n  /**\n   * Create Duration from a number of milliseconds.\n   * @param {number} count of milliseconds\n   * @param {Object} opts - options for parsing\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @return {Duration}\n   */\n  static fromMillis(count, opts) {\n    return Duration.fromObject({ milliseconds: count }, opts);\n  }\n\n  /**\n   * Create a Duration from a JavaScript object with keys like 'years' and 'hours'.\n   * If this object is empty then a zero milliseconds duration is returned.\n   * @param {Object} obj - the object to create the DateTime from\n   * @param {number} obj.years\n   * @param {number} obj.quarters\n   * @param {number} obj.months\n   * @param {number} obj.weeks\n   * @param {number} obj.days\n   * @param {number} obj.hours\n   * @param {number} obj.minutes\n   * @param {number} obj.seconds\n   * @param {number} obj.milliseconds\n   * @param {Object} [opts=[]] - options for creating this Duration\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the preset conversion system to use\n   * @param {string} [opts.matrix=Object] - the custom conversion system to use\n   * @return {Duration}\n   */\n  static fromObject(obj, opts = {}) {\n    if (obj == null || typeof obj !== \"object\") {\n      throw new InvalidArgumentError(\n        `Duration.fromObject: argument expected to be an object, got ${\n          obj === null ? \"null\" : typeof obj\n        }`\n      );\n    }\n\n    return new Duration({\n      values: normalizeObject(obj, Duration.normalizeUnit),\n      loc: Locale.fromObject(opts),\n      conversionAccuracy: opts.conversionAccuracy,\n      matrix: opts.matrix,\n    });\n  }\n\n  /**\n   * Create a Duration from DurationLike.\n   *\n   * @param {Object | number | Duration} durationLike\n   * One of:\n   * - object with keys like 'years' and 'hours'.\n   * - number representing milliseconds\n   * - Duration instance\n   * @return {Duration}\n   */\n  static fromDurationLike(durationLike) {\n    if (isNumber(durationLike)) {\n      return Duration.fromMillis(durationLike);\n    } else if (Duration.isDuration(durationLike)) {\n      return durationLike;\n    } else if (typeof durationLike === \"object\") {\n      return Duration.fromObject(durationLike);\n    } else {\n      throw new InvalidArgumentError(\n        `Unknown duration argument ${durationLike} of type ${typeof durationLike}`\n      );\n    }\n  }\n\n  /**\n   * Create a Duration from an ISO 8601 duration string.\n   * @param {string} text - text to parse\n   * @param {Object} opts - options for parsing\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the preset conversion system to use\n   * @param {string} [opts.matrix=Object] - the preset conversion system to use\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Durations\n   * @example Duration.fromISO('P3Y6M1W4DT12H30M5S').toObject() //=> { years: 3, months: 6, weeks: 1, days: 4, hours: 12, minutes: 30, seconds: 5 }\n   * @example Duration.fromISO('PT23H').toObject() //=> { hours: 23 }\n   * @example Duration.fromISO('P5Y3M').toObject() //=> { years: 5, months: 3 }\n   * @return {Duration}\n   */\n  static fromISO(text, opts) {\n    const [parsed] = parseISODuration(text);\n    if (parsed) {\n      return Duration.fromObject(parsed, opts);\n    } else {\n      return Duration.invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ISO 8601`);\n    }\n  }\n\n  /**\n   * Create a Duration from an ISO 8601 time string.\n   * @param {string} text - text to parse\n   * @param {Object} opts - options for parsing\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the preset conversion system to use\n   * @param {string} [opts.matrix=Object] - the conversion system to use\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Times\n   * @example Duration.fromISOTime('11:22:33.444').toObject() //=> { hours: 11, minutes: 22, seconds: 33, milliseconds: 444 }\n   * @example Duration.fromISOTime('11:00').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @example Duration.fromISOTime('T11:00').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @example Duration.fromISOTime('1100').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @example Duration.fromISOTime('T1100').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @return {Duration}\n   */\n  static fromISOTime(text, opts) {\n    const [parsed] = parseISOTimeOnly(text);\n    if (parsed) {\n      return Duration.fromObject(parsed, opts);\n    } else {\n      return Duration.invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ISO 8601`);\n    }\n  }\n\n  /**\n   * Create an invalid Duration.\n   * @param {string} reason - simple string of why this datetime is invalid. Should not contain parameters or anything else data-dependent\n   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information\n   * @return {Duration}\n   */\n  static invalid(reason, explanation = null) {\n    if (!reason) {\n      throw new InvalidArgumentError(\"need to specify a reason the Duration is invalid\");\n    }\n\n    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);\n\n    if (Settings.throwOnInvalid) {\n      throw new InvalidDurationError(invalid);\n    } else {\n      return new Duration({ invalid });\n    }\n  }\n\n  /**\n   * @private\n   */\n  static normalizeUnit(unit) {\n    const normalized = {\n      year: \"years\",\n      years: \"years\",\n      quarter: \"quarters\",\n      quarters: \"quarters\",\n      month: \"months\",\n      months: \"months\",\n      week: \"weeks\",\n      weeks: \"weeks\",\n      day: \"days\",\n      days: \"days\",\n      hour: \"hours\",\n      hours: \"hours\",\n      minute: \"minutes\",\n      minutes: \"minutes\",\n      second: \"seconds\",\n      seconds: \"seconds\",\n      millisecond: \"milliseconds\",\n      milliseconds: \"milliseconds\",\n    }[unit ? unit.toLowerCase() : unit];\n\n    if (!normalized) throw new InvalidUnitError(unit);\n\n    return normalized;\n  }\n\n  /**\n   * Check if an object is a Duration. Works across context boundaries\n   * @param {object} o\n   * @return {boolean}\n   */\n  static isDuration(o) {\n    return (o && o.isLuxonDuration) || false;\n  }\n\n  /**\n   * Get  the locale of a Duration, such 'en-GB'\n   * @type {string}\n   */\n  get locale() {\n    return this.isValid ? this.loc.locale : null;\n  }\n\n  /**\n   * Get the numbering system of a Duration, such 'beng'. The numbering system is used when formatting the Duration\n   *\n   * @type {string}\n   */\n  get numberingSystem() {\n    return this.isValid ? this.loc.numberingSystem : null;\n  }\n\n  /**\n   * Returns a string representation of this Duration formatted according to the specified format string. You may use these tokens:\n   * * `S` for milliseconds\n   * * `s` for seconds\n   * * `m` for minutes\n   * * `h` for hours\n   * * `d` for days\n   * * `w` for weeks\n   * * `M` for months\n   * * `y` for years\n   * Notes:\n   * * Add padding by repeating the token, e.g. \"yy\" pads the years to two digits, \"hhhh\" pads the hours out to four digits\n   * * Tokens can be escaped by wrapping with single quotes.\n   * * The duration will be converted to the set of units in the format string using {@link Duration#shiftTo} and the Durations's conversion accuracy setting.\n   * @param {string} fmt - the format string\n   * @param {Object} opts - options\n   * @param {boolean} [opts.floor=true] - floor numerical values\n   * @param {'negative'|'all'|'negativeLargestOnly'} [opts.signMode=negative] - How to handle signs\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toFormat(\"y d s\") //=> \"1 6 2\"\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toFormat(\"yy dd sss\") //=> \"01 06 002\"\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toFormat(\"M S\") //=> \"12 518402000\"\n   * @example Duration.fromObject({ days: 6, seconds: 2 }).toFormat(\"d s\", { signMode: \"all\" }) //=> \"+6 +2\"\n   * @example Duration.fromObject({ days: -6, seconds: -2 }).toFormat(\"d s\", { signMode: \"all\" }) //=> \"-6 -2\"\n   * @example Duration.fromObject({ days: -6, seconds: -2 }).toFormat(\"d s\", { signMode: \"negativeLargestOnly\" }) //=> \"-6 2\"\n   * @return {string}\n   */\n  toFormat(fmt, opts = {}) {\n    // reverse-compat since 1.2; we always round down now, never up, and we do it by default\n    const fmtOpts = {\n      ...opts,\n      floor: opts.round !== false && opts.floor !== false,\n    };\n    return this.isValid\n      ? Formatter.create(this.loc, fmtOpts).formatDurationFromString(this, fmt)\n      : INVALID;\n  }\n\n  /**\n   * Returns a string representation of a Duration with all units included.\n   * To modify its behavior, use `listStyle` and any Intl.NumberFormat option, though `unitDisplay` is especially relevant.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/NumberFormat/NumberFormat#options\n   * @param {Object} opts - Formatting options. Accepts the same keys as the options parameter of the native `Intl.NumberFormat` constructor, as well as `listStyle`.\n   * @param {string} [opts.listStyle='narrow'] - How to format the merged list. Corresponds to the `style` property of the options parameter of the native `Intl.ListFormat` constructor.\n   * @param {boolean} [opts.showZeros=true] - Show all units previously used by the duration even if they are zero\n   * @example\n   * ```js\n   * var dur = Duration.fromObject({ months: 1, weeks: 0, hours: 5, minutes: 6 })\n   * dur.toHuman() //=> '1 month, 0 weeks, 5 hours, 6 minutes'\n   * dur.toHuman({ listStyle: \"long\" }) //=> '1 month, 0 weeks, 5 hours, and 6 minutes'\n   * dur.toHuman({ unitDisplay: \"short\" }) //=> '1 mth, 0 wks, 5 hr, 6 min'\n   * dur.toHuman({ showZeros: false }) //=> '1 month, 5 hours, 6 minutes'\n   * ```\n   */\n  toHuman(opts = {}) {\n    if (!this.isValid) return INVALID;\n\n    const showZeros = opts.showZeros !== false;\n\n    const l = orderedUnits\n      .map((unit) => {\n        const val = this.values[unit];\n        if (isUndefined(val) || (val === 0 && !showZeros)) {\n          return null;\n        }\n        return this.loc\n          .numberFormatter({ style: \"unit\", unitDisplay: \"long\", ...opts, unit: unit.slice(0, -1) })\n          .format(val);\n      })\n      .filter((n) => n);\n\n    return this.loc\n      .listFormatter({ type: \"conjunction\", style: opts.listStyle || \"narrow\", ...opts })\n      .format(l);\n  }\n\n  /**\n   * Returns a JavaScript object with this Duration's values.\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toObject() //=> { years: 1, days: 6, seconds: 2 }\n   * @return {Object}\n   */\n  toObject() {\n    if (!this.isValid) return {};\n    return { ...this.values };\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this Duration.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Durations\n   * @example Duration.fromObject({ years: 3, seconds: 45 }).toISO() //=> 'P3YT45S'\n   * @example Duration.fromObject({ months: 4, seconds: 45 }).toISO() //=> 'P4MT45S'\n   * @example Duration.fromObject({ months: 5 }).toISO() //=> 'P5M'\n   * @example Duration.fromObject({ minutes: 5 }).toISO() //=> 'PT5M'\n   * @example Duration.fromObject({ milliseconds: 6 }).toISO() //=> 'PT0.006S'\n   * @return {string}\n   */\n  toISO() {\n    // we could use the formatter, but this is an easier way to get the minimum string\n    if (!this.isValid) return null;\n\n    let s = \"P\";\n    if (this.years !== 0) s += this.years + \"Y\";\n    if (this.months !== 0 || this.quarters !== 0) s += this.months + this.quarters * 3 + \"M\";\n    if (this.weeks !== 0) s += this.weeks + \"W\";\n    if (this.days !== 0) s += this.days + \"D\";\n    if (this.hours !== 0 || this.minutes !== 0 || this.seconds !== 0 || this.milliseconds !== 0)\n      s += \"T\";\n    if (this.hours !== 0) s += this.hours + \"H\";\n    if (this.minutes !== 0) s += this.minutes + \"M\";\n    if (this.seconds !== 0 || this.milliseconds !== 0)\n      // this will handle \"floating point madness\" by removing extra decimal places\n      // https://stackoverflow.com/questions/588004/is-floating-point-math-broken\n      s += roundTo(this.seconds + this.milliseconds / 1000, 3) + \"S\";\n    if (s === \"P\") s += \"T0S\";\n    return s;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this Duration, formatted as a time of day.\n   * Note that this will return null if the duration is invalid, negative, or equal to or greater than 24 hours.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Times\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includePrefix=false] - include the `T` prefix\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example Duration.fromObject({ hours: 11 }).toISOTime() //=> '11:00:00.000'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ suppressMilliseconds: true }) //=> '11:00:00'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ suppressSeconds: true }) //=> '11:00'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ includePrefix: true }) //=> 'T11:00:00.000'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ format: 'basic' }) //=> '110000.000'\n   * @return {string}\n   */\n  toISOTime(opts = {}) {\n    if (!this.isValid) return null;\n\n    const millis = this.toMillis();\n    if (millis < 0 || millis >= 86400000) return null;\n\n    opts = {\n      suppressMilliseconds: false,\n      suppressSeconds: false,\n      includePrefix: false,\n      format: \"extended\",\n      ...opts,\n      includeOffset: false,\n    };\n\n    const dateTime = DateTime.fromMillis(millis, { zone: \"UTC\" });\n    return dateTime.toISOTime(opts);\n  }\n\n  /**\n   * Returns an ISO 8601 representation of this Duration appropriate for use in JSON.\n   * @return {string}\n   */\n  toJSON() {\n    return this.toISO();\n  }\n\n  /**\n   * Returns an ISO 8601 representation of this Duration appropriate for use in debugging.\n   * @return {string}\n   */\n  toString() {\n    return this.toISO();\n  }\n\n  /**\n   * Returns a string representation of this Duration appropriate for the REPL.\n   * @return {string}\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n    if (this.isValid) {\n      return `Duration { values: ${JSON.stringify(this.values)} }`;\n    } else {\n      return `Duration { Invalid, reason: ${this.invalidReason} }`;\n    }\n  }\n\n  /**\n   * Returns an milliseconds value of this Duration.\n   * @return {number}\n   */\n  toMillis() {\n    if (!this.isValid) return NaN;\n\n    return durationToMillis(this.matrix, this.values);\n  }\n\n  /**\n   * Returns an milliseconds value of this Duration. Alias of {@link toMillis}\n   * @return {number}\n   */\n  valueOf() {\n    return this.toMillis();\n  }\n\n  /**\n   * Make this Duration longer by the specified amount. Return a newly-constructed Duration.\n   * @param {Duration|Object|number} duration - The amount to add. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   * @return {Duration}\n   */\n  plus(duration) {\n    if (!this.isValid) return this;\n\n    const dur = Duration.fromDurationLike(duration),\n      result = {};\n\n    for (const k of orderedUnits) {\n      if (hasOwnProperty(dur.values, k) || hasOwnProperty(this.values, k)) {\n        result[k] = dur.get(k) + this.get(k);\n      }\n    }\n\n    return clone(this, { values: result }, true);\n  }\n\n  /**\n   * Make this Duration shorter by the specified amount. Return a newly-constructed Duration.\n   * @param {Duration|Object|number} duration - The amount to subtract. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   * @return {Duration}\n   */\n  minus(duration) {\n    if (!this.isValid) return this;\n\n    const dur = Duration.fromDurationLike(duration);\n    return this.plus(dur.negate());\n  }\n\n  /**\n   * Scale this Duration by the specified amount. Return a newly-constructed Duration.\n   * @param {function} fn - The function to apply to each unit. Arity is 1 or 2: the value of the unit and, optionally, the unit name. Must return a number.\n   * @example Duration.fromObject({ hours: 1, minutes: 30 }).mapUnits(x => x * 2) //=> { hours: 2, minutes: 60 }\n   * @example Duration.fromObject({ hours: 1, minutes: 30 }).mapUnits((x, u) => u === \"hours\" ? x * 2 : x) //=> { hours: 2, minutes: 30 }\n   * @return {Duration}\n   */\n  mapUnits(fn) {\n    if (!this.isValid) return this;\n    const result = {};\n    for (const k of Object.keys(this.values)) {\n      result[k] = asNumber(fn(this.values[k], k));\n    }\n    return clone(this, { values: result }, true);\n  }\n\n  /**\n   * Get the value of unit.\n   * @param {string} unit - a unit such as 'minute' or 'day'\n   * @example Duration.fromObject({years: 2, days: 3}).get('years') //=> 2\n   * @example Duration.fromObject({years: 2, days: 3}).get('months') //=> 0\n   * @example Duration.fromObject({years: 2, days: 3}).get('days') //=> 3\n   * @return {number}\n   */\n  get(unit) {\n    return this[Duration.normalizeUnit(unit)];\n  }\n\n  /**\n   * \"Set\" the values of specified units. Return a newly-constructed Duration.\n   * @param {Object} values - a mapping of units to numbers\n   * @example dur.set({ years: 2017 })\n   * @example dur.set({ hours: 8, minutes: 30 })\n   * @return {Duration}\n   */\n  set(values) {\n    if (!this.isValid) return this;\n\n    const mixed = { ...this.values, ...normalizeObject(values, Duration.normalizeUnit) };\n    return clone(this, { values: mixed });\n  }\n\n  /**\n   * \"Set\" the locale and/or numberingSystem.  Returns a newly-constructed Duration.\n   * @example dur.reconfigure({ locale: 'en-GB' })\n   * @return {Duration}\n   */\n  reconfigure({ locale, numberingSystem, conversionAccuracy, matrix } = {}) {\n    const loc = this.loc.clone({ locale, numberingSystem });\n    const opts = { loc, matrix, conversionAccuracy };\n    return clone(this, opts);\n  }\n\n  /**\n   * Return the length of the duration in the specified unit.\n   * @param {string} unit - a unit such as 'minutes' or 'days'\n   * @example Duration.fromObject({years: 1}).as('days') //=> 365\n   * @example Duration.fromObject({years: 1}).as('months') //=> 12\n   * @example Duration.fromObject({hours: 60}).as('days') //=> 2.5\n   * @return {number}\n   */\n  as(unit) {\n    return this.isValid ? this.shiftTo(unit).get(unit) : NaN;\n  }\n\n  /**\n   * Reduce this Duration to its canonical representation in its current units.\n   * Assuming the overall value of the Duration is positive, this means:\n   * - excessive values for lower-order units are converted to higher-order units (if possible, see first and second example)\n   * - negative lower-order units are converted to higher order units (there must be such a higher order unit, otherwise\n   *   the overall value would be negative, see third example)\n   * - fractional values for higher-order units are converted to lower-order units (if possible, see fourth example)\n   *\n   * If the overall value is negative, the result of this method is equivalent to `this.negate().normalize().negate()`.\n   * @example Duration.fromObject({ years: 2, days: 5000 }).normalize().toObject() //=> { years: 15, days: 255 }\n   * @example Duration.fromObject({ days: 5000 }).normalize().toObject() //=> { days: 5000 }\n   * @example Duration.fromObject({ hours: 12, minutes: -45 }).normalize().toObject() //=> { hours: 11, minutes: 15 }\n   * @example Duration.fromObject({ years: 2.5, days: 0, hours: 0 }).normalize().toObject() //=> { years: 2, days: 182, hours: 12 }\n   * @return {Duration}\n   */\n  normalize() {\n    if (!this.isValid) return this;\n    const vals = this.toObject();\n    normalizeValues(this.matrix, vals);\n    return clone(this, { values: vals }, true);\n  }\n\n  /**\n   * Rescale units to its largest representation\n   * @example Duration.fromObject({ milliseconds: 90000 }).rescale().toObject() //=> { minutes: 1, seconds: 30 }\n   * @return {Duration}\n   */\n  rescale() {\n    if (!this.isValid) return this;\n    const vals = removeZeroes(this.normalize().shiftToAll().toObject());\n    return clone(this, { values: vals }, true);\n  }\n\n  /**\n   * Convert this Duration into its representation in a different set of units.\n   * @example Duration.fromObject({ hours: 1, seconds: 30 }).shiftTo('minutes', 'milliseconds').toObject() //=> { minutes: 60, milliseconds: 30000 }\n   * @return {Duration}\n   */\n  shiftTo(...units) {\n    if (!this.isValid) return this;\n\n    if (units.length === 0) {\n      return this;\n    }\n\n    units = units.map((u) => Duration.normalizeUnit(u));\n\n    const built = {},\n      accumulated = {},\n      vals = this.toObject();\n    let lastUnit;\n\n    for (const k of orderedUnits) {\n      if (units.indexOf(k) >= 0) {\n        lastUnit = k;\n\n        let own = 0;\n\n        // anything we haven't boiled down yet should get boiled to this unit\n        for (const ak in accumulated) {\n          own += this.matrix[ak][k] * accumulated[ak];\n          accumulated[ak] = 0;\n        }\n\n        // plus anything that's already in this unit\n        if (isNumber(vals[k])) {\n          own += vals[k];\n        }\n\n        // only keep the integer part for now in the hopes of putting any decimal part\n        // into a smaller unit later\n        const i = Math.trunc(own);\n        built[k] = i;\n        accumulated[k] = (own * 1000 - i * 1000) / 1000;\n\n        // otherwise, keep it in the wings to boil it later\n      } else if (isNumber(vals[k])) {\n        accumulated[k] = vals[k];\n      }\n    }\n\n    // anything leftover becomes the decimal for the last unit\n    // lastUnit must be defined since units is not empty\n    for (const key in accumulated) {\n      if (accumulated[key] !== 0) {\n        built[lastUnit] +=\n          key === lastUnit ? accumulated[key] : accumulated[key] / this.matrix[lastUnit][key];\n      }\n    }\n\n    normalizeValues(this.matrix, built);\n    return clone(this, { values: built }, true);\n  }\n\n  /**\n   * Shift this Duration to all available units.\n   * Same as shiftTo(\"years\", \"months\", \"weeks\", \"days\", \"hours\", \"minutes\", \"seconds\", \"milliseconds\")\n   * @return {Duration}\n   */\n  shiftToAll() {\n    if (!this.isValid) return this;\n    return this.shiftTo(\n      \"years\",\n      \"months\",\n      \"weeks\",\n      \"days\",\n      \"hours\",\n      \"minutes\",\n      \"seconds\",\n      \"milliseconds\"\n    );\n  }\n\n  /**\n   * Return the negative of this Duration.\n   * @example Duration.fromObject({ hours: 1, seconds: 30 }).negate().toObject() //=> { hours: -1, seconds: -30 }\n   * @return {Duration}\n   */\n  negate() {\n    if (!this.isValid) return this;\n    const negated = {};\n    for (const k of Object.keys(this.values)) {\n      negated[k] = this.values[k] === 0 ? 0 : -this.values[k];\n    }\n    return clone(this, { values: negated }, true);\n  }\n\n  /**\n   * Removes all units with values equal to 0 from this Duration.\n   * @example Duration.fromObject({ years: 2, days: 0, hours: 0, minutes: 0 }).removeZeros().toObject() //=> { years: 2 }\n   * @return {Duration}\n   */\n  removeZeros() {\n    if (!this.isValid) return this;\n    const vals = removeZeroes(this.values);\n    return clone(this, { values: vals }, true);\n  }\n\n  /**\n   * Get the years.\n   * @type {number}\n   */\n  get years() {\n    return this.isValid ? this.values.years || 0 : NaN;\n  }\n\n  /**\n   * Get the quarters.\n   * @type {number}\n   */\n  get quarters() {\n    return this.isValid ? this.values.quarters || 0 : NaN;\n  }\n\n  /**\n   * Get the months.\n   * @type {number}\n   */\n  get months() {\n    return this.isValid ? this.values.months || 0 : NaN;\n  }\n\n  /**\n   * Get the weeks\n   * @type {number}\n   */\n  get weeks() {\n    return this.isValid ? this.values.weeks || 0 : NaN;\n  }\n\n  /**\n   * Get the days.\n   * @type {number}\n   */\n  get days() {\n    return this.isValid ? this.values.days || 0 : NaN;\n  }\n\n  /**\n   * Get the hours.\n   * @type {number}\n   */\n  get hours() {\n    return this.isValid ? this.values.hours || 0 : NaN;\n  }\n\n  /**\n   * Get the minutes.\n   * @type {number}\n   */\n  get minutes() {\n    return this.isValid ? this.values.minutes || 0 : NaN;\n  }\n\n  /**\n   * Get the seconds.\n   * @return {number}\n   */\n  get seconds() {\n    return this.isValid ? this.values.seconds || 0 : NaN;\n  }\n\n  /**\n   * Get the milliseconds.\n   * @return {number}\n   */\n  get milliseconds() {\n    return this.isValid ? this.values.milliseconds || 0 : NaN;\n  }\n\n  /**\n   * Returns whether the Duration is invalid. Invalid durations are returned by diff operations\n   * on invalid DateTimes or Intervals.\n   * @return {boolean}\n   */\n  get isValid() {\n    return this.invalid === null;\n  }\n\n  /**\n   * Returns an error code if this Duration became invalid, or null if the Duration is valid\n   * @return {string}\n   */\n  get invalidReason() {\n    return this.invalid ? this.invalid.reason : null;\n  }\n\n  /**\n   * Returns an explanation of why this Duration became invalid, or null if the Duration is valid\n   * @type {string}\n   */\n  get invalidExplanation() {\n    return this.invalid ? this.invalid.explanation : null;\n  }\n\n  /**\n   * Equality check\n   * Two Durations are equal iff they have the same units and the same values for each unit.\n   * @param {Duration} other\n   * @return {boolean}\n   */\n  equals(other) {\n    if (!this.isValid || !other.isValid) {\n      return false;\n    }\n\n    if (!this.loc.equals(other.loc)) {\n      return false;\n    }\n\n    function eq(v1, v2) {\n      // Consider 0 and undefined as equal\n      if (v1 === undefined || v1 === 0) return v2 === undefined || v2 === 0;\n      return v1 === v2;\n    }\n\n    for (const u of orderedUnits) {\n      if (!eq(this.values[u], other.values[u])) {\n        return false;\n      }\n    }\n    return true;\n  }\n}\n", "import DateTime, { friendlyDateTime } from \"./datetime.js\";\nimport Duration from \"./duration.js\";\nimport Settings from \"./settings.js\";\nimport { InvalidArgumentError, InvalidIntervalError } from \"./errors.js\";\nimport Invalid from \"./impl/invalid.js\";\nimport Formatter from \"./impl/formatter.js\";\nimport * as Formats from \"./impl/formats.js\";\n\nconst INVALID = \"Invalid Interval\";\n\n// checks if the start is equal to or before the end\nfunction validateStartEnd(start, end) {\n  if (!start || !start.isValid) {\n    return Interval.invalid(\"missing or invalid start\");\n  } else if (!end || !end.isValid) {\n    return Interval.invalid(\"missing or invalid end\");\n  } else if (end < start) {\n    return Interval.invalid(\n      \"end before start\",\n      `The end of an interval must be after its start, but you had start=${start.toISO()} and end=${end.toISO()}`\n    );\n  } else {\n    return null;\n  }\n}\n\n/**\n * An Interval object represents a half-open interval of time, where each endpoint is a {@link DateTime}. Conceptually, it's a container for those two endpoints, accompanied by methods for creating, parsing, interrogating, comparing, transforming, and formatting them.\n *\n * Here is a brief overview of the most commonly used methods and getters in Interval:\n *\n * * **Creation** To create an Interval, use {@link Interval.fromDateTimes}, {@link Interval.after}, {@link Interval.before}, or {@link Interval.fromISO}.\n * * **Accessors** Use {@link Interval#start} and {@link Interval#end} to get the start and end.\n * * **Interrogation** To analyze the Interval, use {@link Interval#count}, {@link Interval#length}, {@link Interval#hasSame}, {@link Interval#contains}, {@link Interval#isAfter}, or {@link Interval#isBefore}.\n * * **Transformation** To create other Intervals out of this one, use {@link Interval#set}, {@link Interval#splitAt}, {@link Interval#splitBy}, {@link Interval#divideEqually}, {@link Interval.merge}, {@link Interval.xor}, {@link Interval#union}, {@link Interval#intersection}, or {@link Interval#difference}.\n * * **Comparison** To compare this Interval to another one, use {@link Interval#equals}, {@link Interval#overlaps}, {@link Interval#abutsStart}, {@link Interval#abutsEnd}, {@link Interval#engulfs}\n * * **Output** To convert the Interval into other representations, see {@link Interval#toString}, {@link Interval#toLocaleString}, {@link Interval#toISO}, {@link Interval#toISODate}, {@link Interval#toISOTime}, {@link Interval#toFormat}, and {@link Interval#toDuration}.\n */\nexport default class Interval {\n  /**\n   * @private\n   */\n  constructor(config) {\n    /**\n     * @access private\n     */\n    this.s = config.start;\n    /**\n     * @access private\n     */\n    this.e = config.end;\n    /**\n     * @access private\n     */\n    this.invalid = config.invalid || null;\n    /**\n     * @access private\n     */\n    this.isLuxonInterval = true;\n  }\n\n  /**\n   * Create an invalid Interval.\n   * @param {string} reason - simple string of why this Interval is invalid. Should not contain parameters or anything else data-dependent\n   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information\n   * @return {Interval}\n   */\n  static invalid(reason, explanation = null) {\n    if (!reason) {\n      throw new InvalidArgumentError(\"need to specify a reason the Interval is invalid\");\n    }\n\n    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);\n\n    if (Settings.throwOnInvalid) {\n      throw new InvalidIntervalError(invalid);\n    } else {\n      return new Interval({ invalid });\n    }\n  }\n\n  /**\n   * Create an Interval from a start DateTime and an end DateTime. Inclusive of the start but not the end.\n   * @param {DateTime|Date|Object} start\n   * @param {DateTime|Date|Object} end\n   * @return {Interval}\n   */\n  static fromDateTimes(start, end) {\n    const builtStart = friendlyDateTime(start),\n      builtEnd = friendlyDateTime(end);\n\n    const validateError = validateStartEnd(builtStart, builtEnd);\n\n    if (validateError == null) {\n      return new Interval({\n        start: builtStart,\n        end: builtEnd,\n      });\n    } else {\n      return validateError;\n    }\n  }\n\n  /**\n   * Create an Interval from a start DateTime and a Duration to extend to.\n   * @param {DateTime|Date|Object} start\n   * @param {Duration|Object|number} duration - the length of the Interval.\n   * @return {Interval}\n   */\n  static after(start, duration) {\n    const dur = Duration.fromDurationLike(duration),\n      dt = friendlyDateTime(start);\n    return Interval.fromDateTimes(dt, dt.plus(dur));\n  }\n\n  /**\n   * Create an Interval from an end DateTime and a Duration to extend backwards to.\n   * @param {DateTime|Date|Object} end\n   * @param {Duration|Object|number} duration - the length of the Interval.\n   * @return {Interval}\n   */\n  static before(end, duration) {\n    const dur = Duration.fromDurationLike(duration),\n      dt = friendlyDateTime(end);\n    return Interval.fromDateTimes(dt.minus(dur), dt);\n  }\n\n  /**\n   * Create an Interval from an ISO 8601 string.\n   * Accepts `<start>/<end>`, `<start>/<duration>`, and `<duration>/<end>` formats.\n   * @param {string} text - the ISO string to parse\n   * @param {Object} [opts] - options to pass {@link DateTime#fromISO} and optionally {@link Duration#fromISO}\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @return {Interval}\n   */\n  static fromISO(text, opts) {\n    const [s, e] = (text || \"\").split(\"/\", 2);\n    if (s && e) {\n      let start, startIsValid;\n      try {\n        start = DateTime.fromISO(s, opts);\n        startIsValid = start.isValid;\n      } catch (e) {\n        startIsValid = false;\n      }\n\n      let end, endIsValid;\n      try {\n        end = DateTime.fromISO(e, opts);\n        endIsValid = end.isValid;\n      } catch (e) {\n        endIsValid = false;\n      }\n\n      if (startIsValid && endIsValid) {\n        return Interval.fromDateTimes(start, end);\n      }\n\n      if (startIsValid) {\n        const dur = Duration.fromISO(e, opts);\n        if (dur.isValid) {\n          return Interval.after(start, dur);\n        }\n      } else if (endIsValid) {\n        const dur = Duration.fromISO(s, opts);\n        if (dur.isValid) {\n          return Interval.before(end, dur);\n        }\n      }\n    }\n    return Interval.invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ISO 8601`);\n  }\n\n  /**\n   * Check if an object is an Interval. Works across context boundaries\n   * @param {object} o\n   * @return {boolean}\n   */\n  static isInterval(o) {\n    return (o && o.isLuxonInterval) || false;\n  }\n\n  /**\n   * Returns the start of the Interval\n   * @type {DateTime}\n   */\n  get start() {\n    return this.isValid ? this.s : null;\n  }\n\n  /**\n   * Returns the end of the Interval. This is the first instant which is not part of the interval\n   * (Interval is half-open).\n   * @type {DateTime}\n   */\n  get end() {\n    return this.isValid ? this.e : null;\n  }\n\n  /**\n   * Returns the last DateTime included in the interval (since end is not part of the interval)\n   * @type {DateTime}\n   */\n  get lastDateTime() {\n    return this.isValid ? (this.e ? this.e.minus(1) : null) : null;\n  }\n\n  /**\n   * Returns whether this Interval's end is at least its start, meaning that the Interval isn't 'backwards'.\n   * @type {boolean}\n   */\n  get isValid() {\n    return this.invalidReason === null;\n  }\n\n  /**\n   * Returns an error code if this Interval is invalid, or null if the Interval is valid\n   * @type {string}\n   */\n  get invalidReason() {\n    return this.invalid ? this.invalid.reason : null;\n  }\n\n  /**\n   * Returns an explanation of why this Interval became invalid, or null if the Interval is valid\n   * @type {string}\n   */\n  get invalidExplanation() {\n    return this.invalid ? this.invalid.explanation : null;\n  }\n\n  /**\n   * Returns the length of the Interval in the specified unit.\n   * @param {string} unit - the unit (such as 'hours' or 'days') to return the length in.\n   * @return {number}\n   */\n  length(unit = \"milliseconds\") {\n    return this.isValid ? this.toDuration(...[unit]).get(unit) : NaN;\n  }\n\n  /**\n   * Returns the count of minutes, hours, days, months, or years included in the Interval, even in part.\n   * Unlike {@link Interval#length} this counts sections of the calendar, not periods of time, e.g. specifying 'day'\n   * asks 'what dates are included in this interval?', not 'how many days long is this interval?'\n   * @param {string} [unit='milliseconds'] - the unit of time to count.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week; this operation will always use the locale of the start DateTime\n   * @return {number}\n   */\n  count(unit = \"milliseconds\", opts) {\n    if (!this.isValid) return NaN;\n    const start = this.start.startOf(unit, opts);\n    let end;\n    if (opts?.useLocaleWeeks) {\n      end = this.end.reconfigure({ locale: start.locale });\n    } else {\n      end = this.end;\n    }\n    end = end.startOf(unit, opts);\n    return Math.floor(end.diff(start, unit).get(unit)) + (end.valueOf() !== this.end.valueOf());\n  }\n\n  /**\n   * Returns whether this Interval's start and end are both in the same unit of time\n   * @param {string} unit - the unit of time to check sameness on\n   * @return {boolean}\n   */\n  hasSame(unit) {\n    return this.isValid ? this.isEmpty() || this.e.minus(1).hasSame(this.s, unit) : false;\n  }\n\n  /**\n   * Return whether this Interval has the same start and end DateTimes.\n   * @return {boolean}\n   */\n  isEmpty() {\n    return this.s.valueOf() === this.e.valueOf();\n  }\n\n  /**\n   * Return whether this Interval's start is after the specified DateTime.\n   * @param {DateTime} dateTime\n   * @return {boolean}\n   */\n  isAfter(dateTime) {\n    if (!this.isValid) return false;\n    return this.s > dateTime;\n  }\n\n  /**\n   * Return whether this Interval's end is before the specified DateTime.\n   * @param {DateTime} dateTime\n   * @return {boolean}\n   */\n  isBefore(dateTime) {\n    if (!this.isValid) return false;\n    return this.e <= dateTime;\n  }\n\n  /**\n   * Return whether this Interval contains the specified DateTime.\n   * @param {DateTime} dateTime\n   * @return {boolean}\n   */\n  contains(dateTime) {\n    if (!this.isValid) return false;\n    return this.s <= dateTime && this.e > dateTime;\n  }\n\n  /**\n   * \"Sets\" the start and/or end dates. Returns a newly-constructed Interval.\n   * @param {Object} values - the values to set\n   * @param {DateTime} values.start - the starting DateTime\n   * @param {DateTime} values.end - the ending DateTime\n   * @return {Interval}\n   */\n  set({ start, end } = {}) {\n    if (!this.isValid) return this;\n    return Interval.fromDateTimes(start || this.s, end || this.e);\n  }\n\n  /**\n   * Split this Interval at each of the specified DateTimes\n   * @param {...DateTime} dateTimes - the unit of time to count.\n   * @return {Array}\n   */\n  splitAt(...dateTimes) {\n    if (!this.isValid) return [];\n    const sorted = dateTimes\n        .map(friendlyDateTime)\n        .filter((d) => this.contains(d))\n        .sort((a, b) => a.toMillis() - b.toMillis()),\n      results = [];\n    let { s } = this,\n      i = 0;\n\n    while (s < this.e) {\n      const added = sorted[i] || this.e,\n        next = +added > +this.e ? this.e : added;\n      results.push(Interval.fromDateTimes(s, next));\n      s = next;\n      i += 1;\n    }\n\n    return results;\n  }\n\n  /**\n   * Split this Interval into smaller Intervals, each of the specified length.\n   * Left over time is grouped into a smaller interval\n   * @param {Duration|Object|number} duration - The length of each resulting interval.\n   * @return {Array}\n   */\n  splitBy(duration) {\n    const dur = Duration.fromDurationLike(duration);\n\n    if (!this.isValid || !dur.isValid || dur.as(\"milliseconds\") === 0) {\n      return [];\n    }\n\n    let { s } = this,\n      idx = 1,\n      next;\n\n    const results = [];\n    while (s < this.e) {\n      const added = this.start.plus(dur.mapUnits((x) => x * idx));\n      next = +added > +this.e ? this.e : added;\n      results.push(Interval.fromDateTimes(s, next));\n      s = next;\n      idx += 1;\n    }\n\n    return results;\n  }\n\n  /**\n   * Split this Interval into the specified number of smaller intervals.\n   * @param {number} numberOfParts - The number of Intervals to divide the Interval into.\n   * @return {Array}\n   */\n  divideEqually(numberOfParts) {\n    if (!this.isValid) return [];\n    return this.splitBy(this.length() / numberOfParts).slice(0, numberOfParts);\n  }\n\n  /**\n   * Return whether this Interval overlaps with the specified Interval\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  overlaps(other) {\n    return this.e > other.s && this.s < other.e;\n  }\n\n  /**\n   * Return whether this Interval's end is adjacent to the specified Interval's start.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  abutsStart(other) {\n    if (!this.isValid) return false;\n    return +this.e === +other.s;\n  }\n\n  /**\n   * Return whether this Interval's start is adjacent to the specified Interval's end.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  abutsEnd(other) {\n    if (!this.isValid) return false;\n    return +other.e === +this.s;\n  }\n\n  /**\n   * Returns true if this Interval fully contains the specified Interval, specifically if the intersect (of this Interval and the other Interval) is equal to the other Interval; false otherwise.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  engulfs(other) {\n    if (!this.isValid) return false;\n    return this.s <= other.s && this.e >= other.e;\n  }\n\n  /**\n   * Return whether this Interval has the same start and end as the specified Interval.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  equals(other) {\n    if (!this.isValid || !other.isValid) {\n      return false;\n    }\n\n    return this.s.equals(other.s) && this.e.equals(other.e);\n  }\n\n  /**\n   * Return an Interval representing the intersection of this Interval and the specified Interval.\n   * Specifically, the resulting Interval has the maximum start time and the minimum end time of the two Intervals.\n   * Returns null if the intersection is empty, meaning, the intervals don't intersect.\n   * @param {Interval} other\n   * @return {Interval}\n   */\n  intersection(other) {\n    if (!this.isValid) return this;\n    const s = this.s > other.s ? this.s : other.s,\n      e = this.e < other.e ? this.e : other.e;\n\n    if (s >= e) {\n      return null;\n    } else {\n      return Interval.fromDateTimes(s, e);\n    }\n  }\n\n  /**\n   * Return an Interval representing the union of this Interval and the specified Interval.\n   * Specifically, the resulting Interval has the minimum start time and the maximum end time of the two Intervals.\n   * @param {Interval} other\n   * @return {Interval}\n   */\n  union(other) {\n    if (!this.isValid) return this;\n    const s = this.s < other.s ? this.s : other.s,\n      e = this.e > other.e ? this.e : other.e;\n    return Interval.fromDateTimes(s, e);\n  }\n\n  /**\n   * Merge an array of Intervals into an equivalent minimal set of Intervals.\n   * Combines overlapping and adjacent Intervals.\n   * The resulting array will contain the Intervals in ascending order, that is, starting with the earliest Interval\n   * and ending with the latest.\n   *\n   * @param {Array} intervals\n   * @return {Array}\n   */\n  static merge(intervals) {\n    const [found, final] = intervals\n      .sort((a, b) => a.s - b.s)\n      .reduce(\n        ([sofar, current], item) => {\n          if (!current) {\n            return [sofar, item];\n          } else if (current.overlaps(item) || current.abutsStart(item)) {\n            return [sofar, current.union(item)];\n          } else {\n            return [sofar.concat([current]), item];\n          }\n        },\n        [[], null]\n      );\n    if (final) {\n      found.push(final);\n    }\n    return found;\n  }\n\n  /**\n   * Return an array of Intervals representing the spans of time that only appear in one of the specified Intervals.\n   * @param {Array} intervals\n   * @return {Array}\n   */\n  static xor(intervals) {\n    let start = null,\n      currentCount = 0;\n    const results = [],\n      ends = intervals.map((i) => [\n        { time: i.s, type: \"s\" },\n        { time: i.e, type: \"e\" },\n      ]),\n      flattened = Array.prototype.concat(...ends),\n      arr = flattened.sort((a, b) => a.time - b.time);\n\n    for (const i of arr) {\n      currentCount += i.type === \"s\" ? 1 : -1;\n\n      if (currentCount === 1) {\n        start = i.time;\n      } else {\n        if (start && +start !== +i.time) {\n          results.push(Interval.fromDateTimes(start, i.time));\n        }\n\n        start = null;\n      }\n    }\n\n    return Interval.merge(results);\n  }\n\n  /**\n   * Return an Interval representing the span of time in this Interval that doesn't overlap with any of the specified Intervals.\n   * @param {...Interval} intervals\n   * @return {Array}\n   */\n  difference(...intervals) {\n    return Interval.xor([this].concat(intervals))\n      .map((i) => this.intersection(i))\n      .filter((i) => i && !i.isEmpty());\n  }\n\n  /**\n   * Returns a string representation of this Interval appropriate for debugging.\n   * @return {string}\n   */\n  toString() {\n    if (!this.isValid) return INVALID;\n    return `[${this.s.toISO()} – ${this.e.toISO()})`;\n  }\n\n  /**\n   * Returns a string representation of this Interval appropriate for the REPL.\n   * @return {string}\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n    if (this.isValid) {\n      return `Interval { start: ${this.s.toISO()}, end: ${this.e.toISO()} }`;\n    } else {\n      return `Interval { Invalid, reason: ${this.invalidReason} }`;\n    }\n  }\n\n  /**\n   * Returns a localized string representing this Interval. Accepts the same options as the\n   * Intl.DateTimeFormat constructor and any presets defined by Luxon, such as\n   * {@link DateTime.DATE_FULL} or {@link DateTime.TIME_SIMPLE}. The exact behavior of this method\n   * is browser-specific, but in general it will return an appropriate representation of the\n   * Interval in the assigned locale. Defaults to the system's locale if no locale has been\n   * specified.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param {Object} [formatOpts=DateTime.DATE_SHORT] - Either a DateTime preset or\n   * Intl.DateTimeFormat constructor options.\n   * @param {Object} opts - Options to override the configuration of the start DateTime.\n   * @example Interval.fromISO('2022-11-07T09:00Z/2022-11-08T09:00Z').toLocaleString(); //=> 11/7/2022 – 11/8/2022\n   * @example Interval.fromISO('2022-11-07T09:00Z/2022-11-08T09:00Z').toLocaleString(DateTime.DATE_FULL); //=> November 7 – 8, 2022\n   * @example Interval.fromISO('2022-11-07T09:00Z/2022-11-08T09:00Z').toLocaleString(DateTime.DATE_FULL, { locale: 'fr-FR' }); //=> 7–8 novembre 2022\n   * @example Interval.fromISO('2022-11-07T17:00Z/2022-11-07T19:00Z').toLocaleString(DateTime.TIME_SIMPLE); //=> 6:00 – 8:00 PM\n   * @example Interval.fromISO('2022-11-07T17:00Z/2022-11-07T19:00Z').toLocaleString({ weekday: 'short', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit' }); //=> Mon, Nov 07, 6:00 – 8:00 p\n   * @return {string}\n   */\n  toLocaleString(formatOpts = Formats.DATE_SHORT, opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.s.loc.clone(opts), formatOpts).formatInterval(this)\n      : INVALID;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this Interval.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @param {Object} opts - The same options as {@link DateTime#toISO}\n   * @return {string}\n   */\n  toISO(opts) {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toISO(opts)}/${this.e.toISO(opts)}`;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of date of this Interval.\n   * The time components are ignored.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @return {string}\n   */\n  toISODate() {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toISODate()}/${this.e.toISODate()}`;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of time of this Interval.\n   * The date components are ignored.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @param {Object} opts - The same options as {@link DateTime#toISO}\n   * @return {string}\n   */\n  toISOTime(opts) {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toISOTime(opts)}/${this.e.toISOTime(opts)}`;\n  }\n\n  /**\n   * Returns a string representation of this Interval formatted according to the specified format\n   * string. **You may not want this.** See {@link Interval#toLocaleString} for a more flexible\n   * formatting tool.\n   * @param {string} dateFormat - The format string. This string formats the start and end time.\n   * See {@link DateTime#toFormat} for details.\n   * @param {Object} opts - Options.\n   * @param {string} [opts.separator =  ' – '] - A separator to place between the start and end\n   * representations.\n   * @return {string}\n   */\n  toFormat(dateFormat, { separator = \" – \" } = {}) {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toFormat(dateFormat)}${separator}${this.e.toFormat(dateFormat)}`;\n  }\n\n  /**\n   * Return a Duration representing the time spanned by this interval.\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or units (such as 'hours' or 'days') to include in the duration.\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration().toObject() //=> { milliseconds: 88489257 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration('days').toObject() //=> { days: 1.0241812152777778 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration(['hours', 'minutes']).toObject() //=> { hours: 24, minutes: 34.82095 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration(['hours', 'minutes', 'seconds']).toObject() //=> { hours: 24, minutes: 34, seconds: 49.257 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration('seconds').toObject() //=> { seconds: 88489.257 }\n   * @return {Duration}\n   */\n  toDuration(unit, opts) {\n    if (!this.isValid) {\n      return Duration.invalid(this.invalidReason);\n    }\n    return this.e.diff(this.s, unit, opts);\n  }\n\n  /**\n   * Run mapFn on the interval start and end, returning a new Interval from the resulting DateTimes\n   * @param {function} mapFn\n   * @return {Interval}\n   * @example Interval.fromDateTimes(dt1, dt2).mapEndpoints(endpoint => endpoint.toUTC())\n   * @example Interval.fromDateTimes(dt1, dt2).mapEndpoints(endpoint => endpoint.plus({ hours: 2 }))\n   */\n  mapEndpoints(mapFn) {\n    return Interval.fromDateTimes(mapFn(this.s), mapFn(this.e));\n  }\n}\n", "import DateTime from \"./datetime.js\";\nimport Settings from \"./settings.js\";\nimport Locale from \"./impl/locale.js\";\nimport IANAZone from \"./zones/IANAZone.js\";\nimport { normalizeZone } from \"./impl/zoneUtil.js\";\n\nimport { hasLocaleWeekInfo, hasRelative } from \"./impl/util.js\";\n\n/**\n * The Info class contains static methods for retrieving general time and date related data. For example, it has methods for finding out if a time zone has a DST, for listing the months in any supported locale, and for discovering which of Luxon features are available in the current environment.\n */\nexport default class Info {\n  /**\n   * Return whether the specified zone contains a DST.\n   * @param {string|Zone} [zone='local'] - Zone to check. Defaults to the environment's local zone.\n   * @return {boolean}\n   */\n  static hasDST(zone = Settings.defaultZone) {\n    const proto = DateTime.now().setZone(zone).set({ month: 12 });\n\n    return !zone.isUniversal && proto.offset !== proto.set({ month: 6 }).offset;\n  }\n\n  /**\n   * Return whether the specified zone is a valid IANA specifier.\n   * @param {string} zone - Zone to check\n   * @return {boolean}\n   */\n  static isValidIANAZone(zone) {\n    return IANAZone.isValidZone(zone);\n  }\n\n  /**\n   * Converts the input into a {@link Zone} instance.\n   *\n   * * If `input` is already a Zone instance, it is returned unchanged.\n   * * If `input` is a string containing a valid time zone name, a Zone instance\n   *   with that name is returned.\n   * * If `input` is a string that doesn't refer to a known time zone, a Zone\n   *   instance with {@link Zone#isValid} == false is returned.\n   * * If `input is a number, a Zone instance with the specified fixed offset\n   *   in minutes is returned.\n   * * If `input` is `null` or `undefined`, the default zone is returned.\n   * @param {string|Zone|number} [input] - the value to be converted\n   * @return {Zone}\n   */\n  static normalizeZone(input) {\n    return normalizeZone(input, Settings.defaultZone);\n  }\n\n  /**\n   * Get the weekday on which the week starts according to the given locale.\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @returns {number} the start of the week, 1 for Monday through 7 for Sunday\n   */\n  static getStartOfWeek({ locale = null, locObj = null } = {}) {\n    return (locObj || Locale.create(locale)).getStartOfWeek();\n  }\n\n  /**\n   * Get the minimum number of days necessary in a week before it is considered part of the next year according\n   * to the given locale.\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @returns {number}\n   */\n  static getMinimumDaysInFirstWeek({ locale = null, locObj = null } = {}) {\n    return (locObj || Locale.create(locale)).getMinDaysInFirstWeek();\n  }\n\n  /**\n   * Get the weekdays, which are considered the weekend according to the given locale\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @returns {number[]} an array of weekdays, 1 for Monday through 7 for Sunday\n   */\n  static getWeekendWeekdays({ locale = null, locObj = null } = {}) {\n    // copy the array, because we cache it internally\n    return (locObj || Locale.create(locale)).getWeekendDays().slice();\n  }\n\n  /**\n   * Return an array of standalone month names.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param {string} [length='long'] - the length of the month representation, such as \"numeric\", \"2-digit\", \"narrow\", \"short\", \"long\"\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.numberingSystem=null] - the numbering system\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @param {string} [opts.outputCalendar='gregory'] - the calendar\n   * @example Info.months()[0] //=> 'January'\n   * @example Info.months('short')[0] //=> 'Jan'\n   * @example Info.months('numeric')[0] //=> '1'\n   * @example Info.months('short', { locale: 'fr-CA' } )[0] //=> 'janv.'\n   * @example Info.months('numeric', { locale: 'ar' })[0] //=> '١'\n   * @example Info.months('long', { outputCalendar: 'islamic' })[0] //=> 'Rabiʻ I'\n   * @return {Array}\n   */\n  static months(\n    length = \"long\",\n    { locale = null, numberingSystem = null, locObj = null, outputCalendar = \"gregory\" } = {}\n  ) {\n    return (locObj || Locale.create(locale, numberingSystem, outputCalendar)).months(length);\n  }\n\n  /**\n   * Return an array of format month names.\n   * Format months differ from standalone months in that they're meant to appear next to the day of the month. In some languages, that\n   * changes the string.\n   * See {@link Info#months}\n   * @param {string} [length='long'] - the length of the month representation, such as \"numeric\", \"2-digit\", \"narrow\", \"short\", \"long\"\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.numberingSystem=null] - the numbering system\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @param {string} [opts.outputCalendar='gregory'] - the calendar\n   * @return {Array}\n   */\n  static monthsFormat(\n    length = \"long\",\n    { locale = null, numberingSystem = null, locObj = null, outputCalendar = \"gregory\" } = {}\n  ) {\n    return (locObj || Locale.create(locale, numberingSystem, outputCalendar)).months(length, true);\n  }\n\n  /**\n   * Return an array of standalone week names.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param {string} [length='long'] - the length of the weekday representation, such as \"narrow\", \"short\", \"long\".\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.numberingSystem=null] - the numbering system\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @example Info.weekdays()[0] //=> 'Monday'\n   * @example Info.weekdays('short')[0] //=> 'Mon'\n   * @example Info.weekdays('short', { locale: 'fr-CA' })[0] //=> 'lun.'\n   * @example Info.weekdays('short', { locale: 'ar' })[0] //=> 'الاثنين'\n   * @return {Array}\n   */\n  static weekdays(length = \"long\", { locale = null, numberingSystem = null, locObj = null } = {}) {\n    return (locObj || Locale.create(locale, numberingSystem, null)).weekdays(length);\n  }\n\n  /**\n   * Return an array of format week names.\n   * Format weekdays differ from standalone weekdays in that they're meant to appear next to more date information. In some languages, that\n   * changes the string.\n   * See {@link Info#weekdays}\n   * @param {string} [length='long'] - the length of the month representation, such as \"narrow\", \"short\", \"long\".\n   * @param {Object} opts - options\n   * @param {string} [opts.locale=null] - the locale code\n   * @param {string} [opts.numberingSystem=null] - the numbering system\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @return {Array}\n   */\n  static weekdaysFormat(\n    length = \"long\",\n    { locale = null, numberingSystem = null, locObj = null } = {}\n  ) {\n    return (locObj || Locale.create(locale, numberingSystem, null)).weekdays(length, true);\n  }\n\n  /**\n   * Return an array of meridiems.\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @example Info.meridiems() //=> [ 'AM', 'PM' ]\n   * @example Info.meridiems({ locale: 'my' }) //=> [ 'နံနက်', 'ညနေ' ]\n   * @return {Array}\n   */\n  static meridiems({ locale = null } = {}) {\n    return Locale.create(locale).meridiems();\n  }\n\n  /**\n   * Return an array of eras, such as ['BC', 'AD']. The locale can be specified, but the calendar system is always Gregorian.\n   * @param {string} [length='short'] - the length of the era representation, such as \"short\" or \"long\".\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @example Info.eras() //=> [ 'BC', 'AD' ]\n   * @example Info.eras('long') //=> [ 'Before Christ', 'Anno Domini' ]\n   * @example Info.eras('long', { locale: 'fr' }) //=> [ 'avant Jésus-Christ', 'après Jésus-Christ' ]\n   * @return {Array}\n   */\n  static eras(length = \"short\", { locale = null } = {}) {\n    return Locale.create(locale, null, \"gregory\").eras(length);\n  }\n\n  /**\n   * Return the set of available features in this environment.\n   * Some features of Luxon are not available in all environments. For example, on older browsers, relative time formatting support is not available. Use this function to figure out if that's the case.\n   * Keys:\n   * * `relative`: whether this environment supports relative time formatting\n   * * `localeWeek`: whether this environment supports different weekdays for the start of the week based on the locale\n   * @example Info.features() //=> { relative: false, localeWeek: true }\n   * @return {Object}\n   */\n  static features() {\n    return { relative: hasRelative(), localeWeek: hasLocaleWeekInfo() };\n  }\n}\n", "import Duration from \"../duration.js\";\n\nfunction dayDiff(earlier, later) {\n  const utcDayStart = (dt) => dt.toUTC(0, { keepLocalTime: true }).startOf(\"day\").valueOf(),\n    ms = utcDayStart(later) - utcDayStart(earlier);\n  return Math.floor(Duration.fromMillis(ms).as(\"days\"));\n}\n\nfunction highOrderDiffs(cursor, later, units) {\n  const differs = [\n    [\"years\", (a, b) => b.year - a.year],\n    [\"quarters\", (a, b) => b.quarter - a.quarter + (b.year - a.year) * 4],\n    [\"months\", (a, b) => b.month - a.month + (b.year - a.year) * 12],\n    [\n      \"weeks\",\n      (a, b) => {\n        const days = dayDiff(a, b);\n        return (days - (days % 7)) / 7;\n      },\n    ],\n    [\"days\", dayDiff],\n  ];\n\n  const results = {};\n  const earlier = cursor;\n  let lowestOrder, highWater;\n\n  /* This loop tries to diff using larger units first.\n     If we overshoot, we backtrack and try the next smaller unit.\n     \"cursor\" starts out at the earlier timestamp and moves closer and closer to \"later\"\n     as we use smaller and smaller units.\n     highWater keeps track of where we would be if we added one more of the smallest unit,\n     this is used later to potentially convert any difference smaller than the smallest higher order unit\n     into a fraction of that smallest higher order unit\n  */\n  for (const [unit, differ] of differs) {\n    if (units.indexOf(unit) >= 0) {\n      lowestOrder = unit;\n\n      results[unit] = differ(cursor, later);\n      highWater = earlier.plus(results);\n\n      if (highWater > later) {\n        // we overshot the end point, backtrack cursor by 1\n        results[unit]--;\n        cursor = earlier.plus(results);\n\n        // if we are still overshooting now, we need to backtrack again\n        // this happens in certain situations when diffing times in different zones,\n        // because this calculation ignores time zones\n        if (cursor > later) {\n          // keep the \"overshot by 1\" around as highWater\n          highWater = cursor;\n          // backtrack cursor by 1\n          results[unit]--;\n          cursor = earlier.plus(results);\n        }\n      } else {\n        cursor = highWater;\n      }\n    }\n  }\n\n  return [cursor, results, highWater, lowestOrder];\n}\n\nexport default function (earlier, later, units, opts) {\n  let [cursor, results, highWater, lowestOrder] = highOrderDiffs(earlier, later, units);\n\n  const remainingMillis = later - cursor;\n\n  const lowerOrderUnits = units.filter(\n    (u) => [\"hours\", \"minutes\", \"seconds\", \"milliseconds\"].indexOf(u) >= 0\n  );\n\n  if (lowerOrderUnits.length === 0) {\n    if (highWater < later) {\n      highWater = cursor.plus({ [lowestOrder]: 1 });\n    }\n\n    if (highWater !== cursor) {\n      results[lowestOrder] = (results[lowestOrder] || 0) + remainingMillis / (highWater - cursor);\n    }\n  }\n\n  const duration = Duration.fromObject(results, opts);\n\n  if (lowerOrderUnits.length > 0) {\n    return Duration.fromMillis(remainingMillis, opts)\n      .shiftTo(...lowerOrderUnits)\n      .plus(duration);\n  } else {\n    return duration;\n  }\n}\n", "import { parseM<PERSON>is, isUndefined, untruncate<PERSON>ear, signedOffset, hasOwnProperty } from \"./util.js\";\nimport Formatter from \"./formatter.js\";\nimport FixedOffsetZone from \"../zones/fixedOffsetZone.js\";\nimport IANAZone from \"../zones/IANAZone.js\";\nimport DateTime from \"../datetime.js\";\nimport { digitRegex, parseDigits } from \"./digits.js\";\nimport { ConflictingSpecificationError } from \"../errors.js\";\n\nconst MISSING_FTP = \"missing Intl.DateTimeFormat.formatToParts support\";\n\nfunction intUnit(regex, post = (i) => i) {\n  return { regex, deser: ([s]) => post(parseDigits(s)) };\n}\n\nconst NBSP = String.fromCharCode(160);\nconst spaceOrNBSP = `[ ${NBSP}]`;\nconst spaceOrNBSPRegExp = new RegExp(spaceOrNBSP, \"g\");\n\nfunction fixListRegex(s) {\n  // make dots optional and also make them literal\n  // make space and non breakable space characters interchangeable\n  return s.replace(/\\./g, \"\\\\.?\").replace(spaceOrNBSPRegExp, spaceOrNBSP);\n}\n\nfunction stripInsensitivities(s) {\n  return s\n    .replace(/\\./g, \"\") // ignore dots that were made optional\n    .replace(spaceOrNBSPRegExp, \" \") // interchange space and nbsp\n    .toLowerCase();\n}\n\nfunction oneOf(strings, startIndex) {\n  if (strings === null) {\n    return null;\n  } else {\n    return {\n      regex: RegExp(strings.map(fixListRegex).join(\"|\")),\n      deser: ([s]) =>\n        strings.findIndex((i) => stripInsensitivities(s) === stripInsensitivities(i)) + startIndex,\n    };\n  }\n}\n\nfunction offset(regex, groups) {\n  return { regex, deser: ([, h, m]) => signedOffset(h, m), groups };\n}\n\nfunction simple(regex) {\n  return { regex, deser: ([s]) => s };\n}\n\nfunction escapeToken(value) {\n  return value.replace(/[\\-\\[\\]{}()*+?.,\\\\\\^$|#\\s]/g, \"\\\\$&\");\n}\n\n/**\n * @param token\n * @param {Locale} loc\n */\nfunction unitForToken(token, loc) {\n  const one = digitRegex(loc),\n    two = digitRegex(loc, \"{2}\"),\n    three = digitRegex(loc, \"{3}\"),\n    four = digitRegex(loc, \"{4}\"),\n    six = digitRegex(loc, \"{6}\"),\n    oneOrTwo = digitRegex(loc, \"{1,2}\"),\n    oneToThree = digitRegex(loc, \"{1,3}\"),\n    oneToSix = digitRegex(loc, \"{1,6}\"),\n    oneToNine = digitRegex(loc, \"{1,9}\"),\n    twoToFour = digitRegex(loc, \"{2,4}\"),\n    fourToSix = digitRegex(loc, \"{4,6}\"),\n    literal = (t) => ({ regex: RegExp(escapeToken(t.val)), deser: ([s]) => s, literal: true }),\n    unitate = (t) => {\n      if (token.literal) {\n        return literal(t);\n      }\n      switch (t.val) {\n        // era\n        case \"G\":\n          return oneOf(loc.eras(\"short\"), 0);\n        case \"GG\":\n          return oneOf(loc.eras(\"long\"), 0);\n        // years\n        case \"y\":\n          return intUnit(oneToSix);\n        case \"yy\":\n          return intUnit(twoToFour, untruncateYear);\n        case \"yyyy\":\n          return intUnit(four);\n        case \"yyyyy\":\n          return intUnit(fourToSix);\n        case \"yyyyyy\":\n          return intUnit(six);\n        // months\n        case \"M\":\n          return intUnit(oneOrTwo);\n        case \"MM\":\n          return intUnit(two);\n        case \"MMM\":\n          return oneOf(loc.months(\"short\", true), 1);\n        case \"MMMM\":\n          return oneOf(loc.months(\"long\", true), 1);\n        case \"L\":\n          return intUnit(oneOrTwo);\n        case \"LL\":\n          return intUnit(two);\n        case \"LLL\":\n          return oneOf(loc.months(\"short\", false), 1);\n        case \"LLLL\":\n          return oneOf(loc.months(\"long\", false), 1);\n        // dates\n        case \"d\":\n          return intUnit(oneOrTwo);\n        case \"dd\":\n          return intUnit(two);\n        // ordinals\n        case \"o\":\n          return intUnit(oneToThree);\n        case \"ooo\":\n          return intUnit(three);\n        // time\n        case \"HH\":\n          return intUnit(two);\n        case \"H\":\n          return intUnit(oneOrTwo);\n        case \"hh\":\n          return intUnit(two);\n        case \"h\":\n          return intUnit(oneOrTwo);\n        case \"mm\":\n          return intUnit(two);\n        case \"m\":\n          return intUnit(oneOrTwo);\n        case \"q\":\n          return intUnit(oneOrTwo);\n        case \"qq\":\n          return intUnit(two);\n        case \"s\":\n          return intUnit(oneOrTwo);\n        case \"ss\":\n          return intUnit(two);\n        case \"S\":\n          return intUnit(oneToThree);\n        case \"SSS\":\n          return intUnit(three);\n        case \"u\":\n          return simple(oneToNine);\n        case \"uu\":\n          return simple(oneOrTwo);\n        case \"uuu\":\n          return intUnit(one);\n        // meridiem\n        case \"a\":\n          return oneOf(loc.meridiems(), 0);\n        // weekYear (k)\n        case \"kkkk\":\n          return intUnit(four);\n        case \"kk\":\n          return intUnit(twoToFour, untruncateYear);\n        // weekNumber (W)\n        case \"W\":\n          return intUnit(oneOrTwo);\n        case \"WW\":\n          return intUnit(two);\n        // weekdays\n        case \"E\":\n        case \"c\":\n          return intUnit(one);\n        case \"EEE\":\n          return oneOf(loc.weekdays(\"short\", false), 1);\n        case \"EEEE\":\n          return oneOf(loc.weekdays(\"long\", false), 1);\n        case \"ccc\":\n          return oneOf(loc.weekdays(\"short\", true), 1);\n        case \"cccc\":\n          return oneOf(loc.weekdays(\"long\", true), 1);\n        // offset/zone\n        case \"Z\":\n        case \"ZZ\":\n          return offset(new RegExp(`([+-]${oneOrTwo.source})(?::(${two.source}))?`), 2);\n        case \"ZZZ\":\n          return offset(new RegExp(`([+-]${oneOrTwo.source})(${two.source})?`), 2);\n        // we don't support ZZZZ (PST) or ZZZZZ (Pacific Standard Time) in parsing\n        // because we don't have any way to figure out what they are\n        case \"z\":\n          return simple(/[a-z_+-/]{1,256}?/i);\n        // this special-case \"token\" represents a place where a macro-token expanded into a white-space literal\n        // in this case we accept any non-newline white-space\n        case \" \":\n          return simple(/[^\\S\\n\\r]/);\n        default:\n          return literal(t);\n      }\n    };\n\n  const unit = unitate(token) || {\n    invalidReason: MISSING_FTP,\n  };\n\n  unit.token = token;\n\n  return unit;\n}\n\nconst partTypeStyleToTokenVal = {\n  year: {\n    \"2-digit\": \"yy\",\n    numeric: \"yyyyy\",\n  },\n  month: {\n    numeric: \"M\",\n    \"2-digit\": \"MM\",\n    short: \"MMM\",\n    long: \"MMMM\",\n  },\n  day: {\n    numeric: \"d\",\n    \"2-digit\": \"dd\",\n  },\n  weekday: {\n    short: \"EEE\",\n    long: \"EEEE\",\n  },\n  dayperiod: \"a\",\n  dayPeriod: \"a\",\n  hour12: {\n    numeric: \"h\",\n    \"2-digit\": \"hh\",\n  },\n  hour24: {\n    numeric: \"H\",\n    \"2-digit\": \"HH\",\n  },\n  minute: {\n    numeric: \"m\",\n    \"2-digit\": \"mm\",\n  },\n  second: {\n    numeric: \"s\",\n    \"2-digit\": \"ss\",\n  },\n  timeZoneName: {\n    long: \"ZZZZZ\",\n    short: \"ZZZ\",\n  },\n};\n\nfunction tokenForPart(part, formatOpts, resolvedOpts) {\n  const { type, value } = part;\n\n  if (type === \"literal\") {\n    const isSpace = /^\\s+$/.test(value);\n    return {\n      literal: !isSpace,\n      val: isSpace ? \" \" : value,\n    };\n  }\n\n  const style = formatOpts[type];\n\n  // The user might have explicitly specified hour12 or hourCycle\n  // if so, respect their decision\n  // if not, refer back to the resolvedOpts, which are based on the locale\n  let actualType = type;\n  if (type === \"hour\") {\n    if (formatOpts.hour12 != null) {\n      actualType = formatOpts.hour12 ? \"hour12\" : \"hour24\";\n    } else if (formatOpts.hourCycle != null) {\n      if (formatOpts.hourCycle === \"h11\" || formatOpts.hourCycle === \"h12\") {\n        actualType = \"hour12\";\n      } else {\n        actualType = \"hour24\";\n      }\n    } else {\n      // tokens only differentiate between 24 hours or not,\n      // so we do not need to check hourCycle here, which is less supported anyways\n      actualType = resolvedOpts.hour12 ? \"hour12\" : \"hour24\";\n    }\n  }\n  let val = partTypeStyleToTokenVal[actualType];\n  if (typeof val === \"object\") {\n    val = val[style];\n  }\n\n  if (val) {\n    return {\n      literal: false,\n      val,\n    };\n  }\n\n  return undefined;\n}\n\nfunction buildRegex(units) {\n  const re = units.map((u) => u.regex).reduce((f, r) => `${f}(${r.source})`, \"\");\n  return [`^${re}$`, units];\n}\n\nfunction match(input, regex, handlers) {\n  const matches = input.match(regex);\n\n  if (matches) {\n    const all = {};\n    let matchIndex = 1;\n    for (const i in handlers) {\n      if (hasOwnProperty(handlers, i)) {\n        const h = handlers[i],\n          groups = h.groups ? h.groups + 1 : 1;\n        if (!h.literal && h.token) {\n          all[h.token.val[0]] = h.deser(matches.slice(matchIndex, matchIndex + groups));\n        }\n        matchIndex += groups;\n      }\n    }\n    return [matches, all];\n  } else {\n    return [matches, {}];\n  }\n}\n\nfunction dateTimeFromMatches(matches) {\n  const toField = (token) => {\n    switch (token) {\n      case \"S\":\n        return \"millisecond\";\n      case \"s\":\n        return \"second\";\n      case \"m\":\n        return \"minute\";\n      case \"h\":\n      case \"H\":\n        return \"hour\";\n      case \"d\":\n        return \"day\";\n      case \"o\":\n        return \"ordinal\";\n      case \"L\":\n      case \"M\":\n        return \"month\";\n      case \"y\":\n        return \"year\";\n      case \"E\":\n      case \"c\":\n        return \"weekday\";\n      case \"W\":\n        return \"weekNumber\";\n      case \"k\":\n        return \"weekYear\";\n      case \"q\":\n        return \"quarter\";\n      default:\n        return null;\n    }\n  };\n\n  let zone = null;\n  let specificOffset;\n  if (!isUndefined(matches.z)) {\n    zone = IANAZone.create(matches.z);\n  }\n\n  if (!isUndefined(matches.Z)) {\n    if (!zone) {\n      zone = new FixedOffsetZone(matches.Z);\n    }\n    specificOffset = matches.Z;\n  }\n\n  if (!isUndefined(matches.q)) {\n    matches.M = (matches.q - 1) * 3 + 1;\n  }\n\n  if (!isUndefined(matches.h)) {\n    if (matches.h < 12 && matches.a === 1) {\n      matches.h += 12;\n    } else if (matches.h === 12 && matches.a === 0) {\n      matches.h = 0;\n    }\n  }\n\n  if (matches.G === 0 && matches.y) {\n    matches.y = -matches.y;\n  }\n\n  if (!isUndefined(matches.u)) {\n    matches.S = parseMillis(matches.u);\n  }\n\n  const vals = Object.keys(matches).reduce((r, k) => {\n    const f = toField(k);\n    if (f) {\n      r[f] = matches[k];\n    }\n\n    return r;\n  }, {});\n\n  return [vals, zone, specificOffset];\n}\n\nlet dummyDateTimeCache = null;\n\nfunction getDummyDateTime() {\n  if (!dummyDateTimeCache) {\n    dummyDateTimeCache = DateTime.fromMillis(1555555555555);\n  }\n\n  return dummyDateTimeCache;\n}\n\nfunction maybeExpandMacroToken(token, locale) {\n  if (token.literal) {\n    return token;\n  }\n\n  const formatOpts = Formatter.macroTokenToFormatOpts(token.val);\n  const tokens = formatOptsToTokens(formatOpts, locale);\n\n  if (tokens == null || tokens.includes(undefined)) {\n    return token;\n  }\n\n  return tokens;\n}\n\nexport function expandMacroTokens(tokens, locale) {\n  return Array.prototype.concat(...tokens.map((t) => maybeExpandMacroToken(t, locale)));\n}\n\n/**\n * @private\n */\n\nexport class TokenParser {\n  constructor(locale, format) {\n    this.locale = locale;\n    this.format = format;\n    this.tokens = expandMacroTokens(Formatter.parseFormat(format), locale);\n    this.units = this.tokens.map((t) => unitForToken(t, locale));\n    this.disqualifyingUnit = this.units.find((t) => t.invalidReason);\n\n    if (!this.disqualifyingUnit) {\n      const [regexString, handlers] = buildRegex(this.units);\n      this.regex = RegExp(regexString, \"i\");\n      this.handlers = handlers;\n    }\n  }\n\n  explainFromTokens(input) {\n    if (!this.isValid) {\n      return { input, tokens: this.tokens, invalidReason: this.invalidReason };\n    } else {\n      const [rawMatches, matches] = match(input, this.regex, this.handlers),\n        [result, zone, specificOffset] = matches\n          ? dateTimeFromMatches(matches)\n          : [null, null, undefined];\n      if (hasOwnProperty(matches, \"a\") && hasOwnProperty(matches, \"H\")) {\n        throw new ConflictingSpecificationError(\n          \"Can't include meridiem when specifying 24-hour format\"\n        );\n      }\n      return {\n        input,\n        tokens: this.tokens,\n        regex: this.regex,\n        rawMatches,\n        matches,\n        result,\n        zone,\n        specificOffset,\n      };\n    }\n  }\n\n  get isValid() {\n    return !this.disqualifyingUnit;\n  }\n\n  get invalidReason() {\n    return this.disqualifyingUnit ? this.disqualifyingUnit.invalidReason : null;\n  }\n}\n\nexport function explainFromTokens(locale, input, format) {\n  const parser = new TokenParser(locale, format);\n  return parser.explainFromTokens(input);\n}\n\nexport function parseFromTokens(locale, input, format) {\n  const { result, zone, specificOffset, invalidReason } = explainFromTokens(locale, input, format);\n  return [result, zone, specificOffset, invalidReason];\n}\n\nexport function formatOptsToTokens(formatOpts, locale) {\n  if (!formatOpts) {\n    return null;\n  }\n\n  const formatter = Formatter.create(locale, formatOpts);\n  const df = formatter.dtFormatter(getDummyDateTime());\n  const parts = df.formatToParts();\n  const resolvedOpts = df.resolvedOptions();\n  return parts.map((p) => tokenForPart(p, formatOpts, resolvedOpts));\n}\n", "import Duration from \"./duration.js\";\nimport Interval from \"./interval.js\";\nimport Settings from \"./settings.js\";\nimport Info from \"./info.js\";\nimport Formatter from \"./impl/formatter.js\";\nimport FixedOffsetZone from \"./zones/fixedOffsetZone.js\";\nimport Locale from \"./impl/locale.js\";\nimport {\n  isUndefined,\n  maybeArray,\n  isDate,\n  isNumber,\n  bestBy,\n  daysInMonth,\n  daysInYear,\n  isLeapYear,\n  weeksInWeekYear,\n  normalizeObject,\n  roundTo,\n  objToLocalTS,\n  padStart,\n} from \"./impl/util.js\";\nimport { normalizeZone } from \"./impl/zoneUtil.js\";\nimport diff from \"./impl/diff.js\";\nimport { parseRFC2822Date, parseISODate, parseHTTPDate, parseSQL } from \"./impl/regexParser.js\";\nimport {\n  parseFromTokens,\n  explainFromTokens,\n  formatOptsToTokens,\n  expandMacroTokens,\n  TokenParser,\n} from \"./impl/tokenParser.js\";\nimport {\n  gregorianToWeek,\n  weekToGregorian,\n  gregorianToOrdinal,\n  ordinalToGregorian,\n  hasInvalidGregorianData,\n  hasInvalidWeekData,\n  hasInvalidOrdinalData,\n  hasInvalidTimeData,\n  usesLocalWeekValues,\n  isoWeekdayToLocal,\n} from \"./impl/conversions.js\";\nimport * as Formats from \"./impl/formats.js\";\nimport {\n  InvalidArgumentError,\n  ConflictingSpecificationError,\n  InvalidUnitError,\n  InvalidDateTimeError,\n} from \"./errors.js\";\nimport Invalid from \"./impl/invalid.js\";\n\nconst INVALID = \"Invalid DateTime\";\nconst MAX_DATE = 8.64e15;\n\nfunction unsupportedZone(zone) {\n  return new Invalid(\"unsupported zone\", `the zone \"${zone.name}\" is not supported`);\n}\n\n// we cache week data on the DT object and this intermediates the cache\n/**\n * @param {DateTime} dt\n */\nfunction possiblyCachedWeekData(dt) {\n  if (dt.weekData === null) {\n    dt.weekData = gregorianToWeek(dt.c);\n  }\n  return dt.weekData;\n}\n\n/**\n * @param {DateTime} dt\n */\nfunction possiblyCachedLocalWeekData(dt) {\n  if (dt.localWeekData === null) {\n    dt.localWeekData = gregorianToWeek(\n      dt.c,\n      dt.loc.getMinDaysInFirstWeek(),\n      dt.loc.getStartOfWeek()\n    );\n  }\n  return dt.localWeekData;\n}\n\n// clone really means, \"make a new object with these modifications\". all \"setters\" really use this\n// to create a new object while only changing some of the properties\nfunction clone(inst, alts) {\n  const current = {\n    ts: inst.ts,\n    zone: inst.zone,\n    c: inst.c,\n    o: inst.o,\n    loc: inst.loc,\n    invalid: inst.invalid,\n  };\n  return new DateTime({ ...current, ...alts, old: current });\n}\n\n// find the right offset a given local time. The o input is our guess, which determines which\n// offset we'll pick in ambiguous cases (e.g. there are two 3 AMs b/c Fallback DST)\nfunction fixOffset(localTS, o, tz) {\n  // Our UTC time is just a guess because our offset is just a guess\n  let utcGuess = localTS - o * 60 * 1000;\n\n  // Test whether the zone matches the offset for this ts\n  const o2 = tz.offset(utcGuess);\n\n  // If so, offset didn't change and we're done\n  if (o === o2) {\n    return [utcGuess, o];\n  }\n\n  // If not, change the ts by the difference in the offset\n  utcGuess -= (o2 - o) * 60 * 1000;\n\n  // If that gives us the local time we want, we're done\n  const o3 = tz.offset(utcGuess);\n  if (o2 === o3) {\n    return [utcGuess, o2];\n  }\n\n  // If it's different, we're in a hole time. The offset has changed, but the we don't adjust the time\n  return [localTS - Math.min(o2, o3) * 60 * 1000, Math.max(o2, o3)];\n}\n\n// convert an epoch timestamp into a calendar object with the given offset\nfunction tsToObj(ts, offset) {\n  ts += offset * 60 * 1000;\n\n  const d = new Date(ts);\n\n  return {\n    year: d.getUTCFullYear(),\n    month: d.getUTCMonth() + 1,\n    day: d.getUTCDate(),\n    hour: d.getUTCHours(),\n    minute: d.getUTCMinutes(),\n    second: d.getUTCSeconds(),\n    millisecond: d.getUTCMilliseconds(),\n  };\n}\n\n// convert a calendar object to a epoch timestamp\nfunction objToTS(obj, offset, zone) {\n  return fixOffset(objToLocalTS(obj), offset, zone);\n}\n\n// create a new DT instance by adding a duration, adjusting for DSTs\nfunction adjustTime(inst, dur) {\n  const oPre = inst.o,\n    year = inst.c.year + Math.trunc(dur.years),\n    month = inst.c.month + Math.trunc(dur.months) + Math.trunc(dur.quarters) * 3,\n    c = {\n      ...inst.c,\n      year,\n      month,\n      day:\n        Math.min(inst.c.day, daysInMonth(year, month)) +\n        Math.trunc(dur.days) +\n        Math.trunc(dur.weeks) * 7,\n    },\n    millisToAdd = Duration.fromObject({\n      years: dur.years - Math.trunc(dur.years),\n      quarters: dur.quarters - Math.trunc(dur.quarters),\n      months: dur.months - Math.trunc(dur.months),\n      weeks: dur.weeks - Math.trunc(dur.weeks),\n      days: dur.days - Math.trunc(dur.days),\n      hours: dur.hours,\n      minutes: dur.minutes,\n      seconds: dur.seconds,\n      milliseconds: dur.milliseconds,\n    }).as(\"milliseconds\"),\n    localTS = objToLocalTS(c);\n\n  let [ts, o] = fixOffset(localTS, oPre, inst.zone);\n\n  if (millisToAdd !== 0) {\n    ts += millisToAdd;\n    // that could have changed the offset by going over a DST, but we want to keep the ts the same\n    o = inst.zone.offset(ts);\n  }\n\n  return { ts, o };\n}\n\n// helper useful in turning the results of parsing into real dates\n// by handling the zone options\nfunction parseDataToDateTime(parsed, parsedZone, opts, format, text, specificOffset) {\n  const { setZone, zone } = opts;\n  if ((parsed && Object.keys(parsed).length !== 0) || parsedZone) {\n    const interpretationZone = parsedZone || zone,\n      inst = DateTime.fromObject(parsed, {\n        ...opts,\n        zone: interpretationZone,\n        specificOffset,\n      });\n    return setZone ? inst : inst.setZone(zone);\n  } else {\n    return DateTime.invalid(\n      new Invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ${format}`)\n    );\n  }\n}\n\n// if you want to output a technical format (e.g. RFC 2822), this helper\n// helps handle the details\nfunction toTechFormat(dt, format, allowZ = true) {\n  return dt.isValid\n    ? Formatter.create(Locale.create(\"en-US\"), {\n        allowZ,\n        forceSimple: true,\n      }).formatDateTimeFromString(dt, format)\n    : null;\n}\n\nfunction toISODate(o, extended, precision) {\n  const longFormat = o.c.year > 9999 || o.c.year < 0;\n  let c = \"\";\n  if (longFormat && o.c.year >= 0) c += \"+\";\n  c += padStart(o.c.year, longFormat ? 6 : 4);\n  if (precision === \"year\") return c;\n  if (extended) {\n    c += \"-\";\n    c += padStart(o.c.month);\n    if (precision === \"month\") return c;\n    c += \"-\";\n  } else {\n    c += padStart(o.c.month);\n    if (precision === \"month\") return c;\n  }\n  c += padStart(o.c.day);\n  return c;\n}\n\nfunction toISOTime(\n  o,\n  extended,\n  suppressSeconds,\n  suppressMilliseconds,\n  includeOffset,\n  extendedZone,\n  precision\n) {\n  let showSeconds = !suppressSeconds || o.c.millisecond !== 0 || o.c.second !== 0,\n    c = \"\";\n  switch (precision) {\n    case \"day\":\n    case \"month\":\n    case \"year\":\n      break;\n    default:\n      c += padStart(o.c.hour);\n      if (precision === \"hour\") break;\n      if (extended) {\n        c += \":\";\n        c += padStart(o.c.minute);\n        if (precision === \"minute\") break;\n        if (showSeconds) {\n          c += \":\";\n          c += padStart(o.c.second);\n        }\n      } else {\n        c += padStart(o.c.minute);\n        if (precision === \"minute\") break;\n        if (showSeconds) {\n          c += padStart(o.c.second);\n        }\n      }\n      if (precision === \"second\") break;\n      if (showSeconds && (!suppressMilliseconds || o.c.millisecond !== 0)) {\n        c += \".\";\n        c += padStart(o.c.millisecond, 3);\n      }\n  }\n\n  if (includeOffset) {\n    if (o.isOffsetFixed && o.offset === 0 && !extendedZone) {\n      c += \"Z\";\n    } else if (o.o < 0) {\n      c += \"-\";\n      c += padStart(Math.trunc(-o.o / 60));\n      c += \":\";\n      c += padStart(Math.trunc(-o.o % 60));\n    } else {\n      c += \"+\";\n      c += padStart(Math.trunc(o.o / 60));\n      c += \":\";\n      c += padStart(Math.trunc(o.o % 60));\n    }\n  }\n\n  if (extendedZone) {\n    c += \"[\" + o.zone.ianaName + \"]\";\n  }\n  return c;\n}\n\n// defaults for unspecified units in the supported calendars\nconst defaultUnitValues = {\n    month: 1,\n    day: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0,\n  },\n  defaultWeekUnitValues = {\n    weekNumber: 1,\n    weekday: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0,\n  },\n  defaultOrdinalUnitValues = {\n    ordinal: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0,\n  };\n\n// Units in the supported calendars, sorted by bigness\nconst orderedUnits = [\"year\", \"month\", \"day\", \"hour\", \"minute\", \"second\", \"millisecond\"],\n  orderedWeekUnits = [\n    \"weekYear\",\n    \"weekNumber\",\n    \"weekday\",\n    \"hour\",\n    \"minute\",\n    \"second\",\n    \"millisecond\",\n  ],\n  orderedOrdinalUnits = [\"year\", \"ordinal\", \"hour\", \"minute\", \"second\", \"millisecond\"];\n\n// standardize case and plurality in units\nfunction normalizeUnit(unit) {\n  const normalized = {\n    year: \"year\",\n    years: \"year\",\n    month: \"month\",\n    months: \"month\",\n    day: \"day\",\n    days: \"day\",\n    hour: \"hour\",\n    hours: \"hour\",\n    minute: \"minute\",\n    minutes: \"minute\",\n    quarter: \"quarter\",\n    quarters: \"quarter\",\n    second: \"second\",\n    seconds: \"second\",\n    millisecond: \"millisecond\",\n    milliseconds: \"millisecond\",\n    weekday: \"weekday\",\n    weekdays: \"weekday\",\n    weeknumber: \"weekNumber\",\n    weeksnumber: \"weekNumber\",\n    weeknumbers: \"weekNumber\",\n    weekyear: \"weekYear\",\n    weekyears: \"weekYear\",\n    ordinal: \"ordinal\",\n  }[unit.toLowerCase()];\n\n  if (!normalized) throw new InvalidUnitError(unit);\n\n  return normalized;\n}\n\nfunction normalizeUnitWithLocalWeeks(unit) {\n  switch (unit.toLowerCase()) {\n    case \"localweekday\":\n    case \"localweekdays\":\n      return \"localWeekday\";\n    case \"localweeknumber\":\n    case \"localweeknumbers\":\n      return \"localWeekNumber\";\n    case \"localweekyear\":\n    case \"localweekyears\":\n      return \"localWeekYear\";\n    default:\n      return normalizeUnit(unit);\n  }\n}\n\n// cache offsets for zones based on the current timestamp when this function is\n// first called. When we are handling a datetime from components like (year,\n// month, day, hour) in a time zone, we need a guess about what the timezone\n// offset is so that we can convert into a UTC timestamp. One way is to find the\n// offset of now in the zone. The actual date may have a different offset (for\n// example, if we handle a date in June while we're in December in a zone that\n// observes DST), but we can check and adjust that.\n//\n// When handling many dates, calculating the offset for now every time is\n// expensive. It's just a guess, so we can cache the offset to use even if we\n// are right on a time change boundary (we'll just correct in the other\n// direction). Using a timestamp from first read is a slight optimization for\n// handling dates close to the current date, since those dates will usually be\n// in the same offset (we could set the timestamp statically, instead). We use a\n// single timestamp for all zones to make things a bit more predictable.\n//\n// This is safe for quickDT (used by local() and utc()) because we don't fill in\n// higher-order units from tsNow (as we do in fromObject, this requires that\n// offset is calculated from tsNow).\n/**\n * @param {Zone} zone\n * @return {number}\n */\nfunction guessOffsetForZone(zone) {\n  if (zoneOffsetTs === undefined) {\n    zoneOffsetTs = Settings.now();\n  }\n\n  // Do not cache anything but IANA zones, because it is not safe to do so.\n  // Guessing an offset which is not present in the zone can cause wrong results from fixOffset\n  if (zone.type !== \"iana\") {\n    return zone.offset(zoneOffsetTs);\n  }\n  const zoneName = zone.name;\n  let offsetGuess = zoneOffsetGuessCache.get(zoneName);\n  if (offsetGuess === undefined) {\n    offsetGuess = zone.offset(zoneOffsetTs);\n    zoneOffsetGuessCache.set(zoneName, offsetGuess);\n  }\n  return offsetGuess;\n}\n\n// this is a dumbed down version of fromObject() that runs about 60% faster\n// but doesn't do any validation, makes a bunch of assumptions about what units\n// are present, and so on.\nfunction quickDT(obj, opts) {\n  const zone = normalizeZone(opts.zone, Settings.defaultZone);\n  if (!zone.isValid) {\n    return DateTime.invalid(unsupportedZone(zone));\n  }\n\n  const loc = Locale.fromObject(opts);\n\n  let ts, o;\n\n  // assume we have the higher-order units\n  if (!isUndefined(obj.year)) {\n    for (const u of orderedUnits) {\n      if (isUndefined(obj[u])) {\n        obj[u] = defaultUnitValues[u];\n      }\n    }\n\n    const invalid = hasInvalidGregorianData(obj) || hasInvalidTimeData(obj);\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    }\n\n    const offsetProvis = guessOffsetForZone(zone);\n    [ts, o] = objToTS(obj, offsetProvis, zone);\n  } else {\n    ts = Settings.now();\n  }\n\n  return new DateTime({ ts, zone, loc, o });\n}\n\nfunction diffRelative(start, end, opts) {\n  const round = isUndefined(opts.round) ? true : opts.round,\n    rounding = isUndefined(opts.rounding) ? \"trunc\" : opts.rounding,\n    format = (c, unit) => {\n      c = roundTo(c, round || opts.calendary ? 0 : 2, opts.calendary ? \"round\" : rounding);\n      const formatter = end.loc.clone(opts).relFormatter(opts);\n      return formatter.format(c, unit);\n    },\n    differ = (unit) => {\n      if (opts.calendary) {\n        if (!end.hasSame(start, unit)) {\n          return end.startOf(unit).diff(start.startOf(unit), unit).get(unit);\n        } else return 0;\n      } else {\n        return end.diff(start, unit).get(unit);\n      }\n    };\n\n  if (opts.unit) {\n    return format(differ(opts.unit), opts.unit);\n  }\n\n  for (const unit of opts.units) {\n    const count = differ(unit);\n    if (Math.abs(count) >= 1) {\n      return format(count, unit);\n    }\n  }\n  return format(start > end ? -0 : 0, opts.units[opts.units.length - 1]);\n}\n\nfunction lastOpts(argList) {\n  let opts = {},\n    args;\n  if (argList.length > 0 && typeof argList[argList.length - 1] === \"object\") {\n    opts = argList[argList.length - 1];\n    args = Array.from(argList).slice(0, argList.length - 1);\n  } else {\n    args = Array.from(argList);\n  }\n  return [opts, args];\n}\n\n/**\n * Timestamp to use for cached zone offset guesses (exposed for test)\n */\nlet zoneOffsetTs;\n/**\n * Cache for zone offset guesses (exposed for test).\n *\n * This optimizes quickDT via guessOffsetForZone to avoid repeated calls of\n * zone.offset().\n */\nconst zoneOffsetGuessCache = new Map();\n\n/**\n * A DateTime is an immutable data structure representing a specific date and time and accompanying methods. It contains class and instance methods for creating, parsing, interrogating, transforming, and formatting them.\n *\n * A DateTime comprises of:\n * * A timestamp. Each DateTime instance refers to a specific millisecond of the Unix epoch.\n * * A time zone. Each instance is considered in the context of a specific zone (by default the local system's zone).\n * * Configuration properties that effect how output strings are formatted, such as `locale`, `numberingSystem`, and `outputCalendar`.\n *\n * Here is a brief overview of the most commonly used functionality it provides:\n *\n * * **Creation**: To create a DateTime from its components, use one of its factory class methods: {@link DateTime.local}, {@link DateTime.utc}, and (most flexibly) {@link DateTime.fromObject}. To create one from a standard string format, use {@link DateTime.fromISO}, {@link DateTime.fromHTTP}, and {@link DateTime.fromRFC2822}. To create one from a custom string format, use {@link DateTime.fromFormat}. To create one from a native JS date, use {@link DateTime.fromJSDate}.\n * * **Gregorian calendar and time**: To examine the Gregorian properties of a DateTime individually (i.e as opposed to collectively through {@link DateTime#toObject}), use the {@link DateTime#year}, {@link DateTime#month},\n * {@link DateTime#day}, {@link DateTime#hour}, {@link DateTime#minute}, {@link DateTime#second}, {@link DateTime#millisecond} accessors.\n * * **Week calendar**: For ISO week calendar attributes, see the {@link DateTime#weekYear}, {@link DateTime#weekNumber}, and {@link DateTime#weekday} accessors.\n * * **Configuration** See the {@link DateTime#locale} and {@link DateTime#numberingSystem} accessors.\n * * **Transformation**: To transform the DateTime into other DateTimes, use {@link DateTime#set}, {@link DateTime#reconfigure}, {@link DateTime#setZone}, {@link DateTime#setLocale}, {@link DateTime.plus}, {@link DateTime#minus}, {@link DateTime#endOf}, {@link DateTime#startOf}, {@link DateTime#toUTC}, and {@link DateTime#toLocal}.\n * * **Output**: To convert the DateTime to other representations, use the {@link DateTime#toRelative}, {@link DateTime#toRelativeCalendar}, {@link DateTime#toJSON}, {@link DateTime#toISO}, {@link DateTime#toHTTP}, {@link DateTime#toObject}, {@link DateTime#toRFC2822}, {@link DateTime#toString}, {@link DateTime#toLocaleString}, {@link DateTime#toFormat}, {@link DateTime#toMillis} and {@link DateTime#toJSDate}.\n *\n * There's plenty others documented below. In addition, for more information on subtler topics like internationalization, time zones, alternative calendars, validity, and so on, see the external documentation.\n */\nexport default class DateTime {\n  /**\n   * @access private\n   */\n  constructor(config) {\n    const zone = config.zone || Settings.defaultZone;\n\n    let invalid =\n      config.invalid ||\n      (Number.isNaN(config.ts) ? new Invalid(\"invalid input\") : null) ||\n      (!zone.isValid ? unsupportedZone(zone) : null);\n    /**\n     * @access private\n     */\n    this.ts = isUndefined(config.ts) ? Settings.now() : config.ts;\n\n    let c = null,\n      o = null;\n    if (!invalid) {\n      const unchanged = config.old && config.old.ts === this.ts && config.old.zone.equals(zone);\n\n      if (unchanged) {\n        [c, o] = [config.old.c, config.old.o];\n      } else {\n        // If an offset has been passed and we have not been called from\n        // clone(), we can trust it and avoid the offset calculation.\n        const ot = isNumber(config.o) && !config.old ? config.o : zone.offset(this.ts);\n        c = tsToObj(this.ts, ot);\n        invalid = Number.isNaN(c.year) ? new Invalid(\"invalid input\") : null;\n        c = invalid ? null : c;\n        o = invalid ? null : ot;\n      }\n    }\n\n    /**\n     * @access private\n     */\n    this._zone = zone;\n    /**\n     * @access private\n     */\n    this.loc = config.loc || Locale.create();\n    /**\n     * @access private\n     */\n    this.invalid = invalid;\n    /**\n     * @access private\n     */\n    this.weekData = null;\n    /**\n     * @access private\n     */\n    this.localWeekData = null;\n    /**\n     * @access private\n     */\n    this.c = c;\n    /**\n     * @access private\n     */\n    this.o = o;\n    /**\n     * @access private\n     */\n    this.isLuxonDateTime = true;\n  }\n\n  // CONSTRUCT\n\n  /**\n   * Create a DateTime for the current instant, in the system's time zone.\n   *\n   * Use Settings to override these default values if needed.\n   * @example DateTime.now().toISO() //~> now in the ISO format\n   * @return {DateTime}\n   */\n  static now() {\n    return new DateTime({});\n  }\n\n  /**\n   * Create a local DateTime\n   * @param {number} [year] - The calendar year. If omitted (as in, call `local()` with no arguments), the current time will be used\n   * @param {number} [month=1] - The month, 1-indexed\n   * @param {number} [day=1] - The day of the month, 1-indexed\n   * @param {number} [hour=0] - The hour of the day, in 24-hour time\n   * @param {number} [minute=0] - The minute of the hour, meaning a number between 0 and 59\n   * @param {number} [second=0] - The second of the minute, meaning a number between 0 and 59\n   * @param {number} [millisecond=0] - The millisecond of the second, meaning a number between 0 and 999\n   * @example DateTime.local()                                  //~> now\n   * @example DateTime.local({ zone: \"America/New_York\" })      //~> now, in US east coast time\n   * @example DateTime.local(2017)                              //~> 2017-01-01T00:00:00\n   * @example DateTime.local(2017, 3)                           //~> 2017-03-01T00:00:00\n   * @example DateTime.local(2017, 3, 12, { locale: \"fr\" })     //~> 2017-03-12T00:00:00, with a French locale\n   * @example DateTime.local(2017, 3, 12, 5)                    //~> 2017-03-12T05:00:00\n   * @example DateTime.local(2017, 3, 12, 5, { zone: \"utc\" })   //~> 2017-03-12T05:00:00, in UTC\n   * @example DateTime.local(2017, 3, 12, 5, 45)                //~> 2017-03-12T05:45:00\n   * @example DateTime.local(2017, 3, 12, 5, 45, 10)            //~> 2017-03-12T05:45:10\n   * @example DateTime.local(2017, 3, 12, 5, 45, 10, 765)       //~> 2017-03-12T05:45:10.765\n   * @return {DateTime}\n   */\n  static local() {\n    const [opts, args] = lastOpts(arguments),\n      [year, month, day, hour, minute, second, millisecond] = args;\n    return quickDT({ year, month, day, hour, minute, second, millisecond }, opts);\n  }\n\n  /**\n   * Create a DateTime in UTC\n   * @param {number} [year] - The calendar year. If omitted (as in, call `utc()` with no arguments), the current time will be used\n   * @param {number} [month=1] - The month, 1-indexed\n   * @param {number} [day=1] - The day of the month\n   * @param {number} [hour=0] - The hour of the day, in 24-hour time\n   * @param {number} [minute=0] - The minute of the hour, meaning a number between 0 and 59\n   * @param {number} [second=0] - The second of the minute, meaning a number between 0 and 59\n   * @param {number} [millisecond=0] - The millisecond of the second, meaning a number between 0 and 999\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} [options.outputCalendar] - the output calendar to set on the resulting DateTime instance\n   * @param {string} [options.numberingSystem] - the numbering system to set on the resulting DateTime instance\n   * @param {string} [options.weekSettings] - the week settings to set on the resulting DateTime instance\n   * @example DateTime.utc()                                              //~> now\n   * @example DateTime.utc(2017)                                          //~> 2017-01-01T00:00:00Z\n   * @example DateTime.utc(2017, 3)                                       //~> 2017-03-01T00:00:00Z\n   * @example DateTime.utc(2017, 3, 12)                                   //~> 2017-03-12T00:00:00Z\n   * @example DateTime.utc(2017, 3, 12, 5)                                //~> 2017-03-12T05:00:00Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45)                            //~> 2017-03-12T05:45:00Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45, { locale: \"fr\" })          //~> 2017-03-12T05:45:00Z with a French locale\n   * @example DateTime.utc(2017, 3, 12, 5, 45, 10)                        //~> 2017-03-12T05:45:10Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45, 10, 765, { locale: \"fr\" }) //~> 2017-03-12T05:45:10.765Z with a French locale\n   * @return {DateTime}\n   */\n  static utc() {\n    const [opts, args] = lastOpts(arguments),\n      [year, month, day, hour, minute, second, millisecond] = args;\n\n    opts.zone = FixedOffsetZone.utcInstance;\n    return quickDT({ year, month, day, hour, minute, second, millisecond }, opts);\n  }\n\n  /**\n   * Create a DateTime from a JavaScript Date object. Uses the default zone.\n   * @param {Date} date - a JavaScript Date object\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @return {DateTime}\n   */\n  static fromJSDate(date, options = {}) {\n    const ts = isDate(date) ? date.valueOf() : NaN;\n    if (Number.isNaN(ts)) {\n      return DateTime.invalid(\"invalid input\");\n    }\n\n    const zoneToUse = normalizeZone(options.zone, Settings.defaultZone);\n    if (!zoneToUse.isValid) {\n      return DateTime.invalid(unsupportedZone(zoneToUse));\n    }\n\n    return new DateTime({\n      ts: ts,\n      zone: zoneToUse,\n      loc: Locale.fromObject(options),\n    });\n  }\n\n  /**\n   * Create a DateTime from a number of milliseconds since the epoch (meaning since 1 January 1970 00:00:00 UTC). Uses the default zone.\n   * @param {number} milliseconds - a number of milliseconds since 1970 UTC\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} options.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} options.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} options.weekSettings - the week settings to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromMillis(milliseconds, options = {}) {\n    if (!isNumber(milliseconds)) {\n      throw new InvalidArgumentError(\n        `fromMillis requires a numerical input, but received a ${typeof milliseconds} with value ${milliseconds}`\n      );\n    } else if (milliseconds < -MAX_DATE || milliseconds > MAX_DATE) {\n      // this isn't perfect because we can still end up out of range because of additional shifting, but it's a start\n      return DateTime.invalid(\"Timestamp out of range\");\n    } else {\n      return new DateTime({\n        ts: milliseconds,\n        zone: normalizeZone(options.zone, Settings.defaultZone),\n        loc: Locale.fromObject(options),\n      });\n    }\n  }\n\n  /**\n   * Create a DateTime from a number of seconds since the epoch (meaning since 1 January 1970 00:00:00 UTC). Uses the default zone.\n   * @param {number} seconds - a number of seconds since 1970 UTC\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} options.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} options.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} options.weekSettings - the week settings to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromSeconds(seconds, options = {}) {\n    if (!isNumber(seconds)) {\n      throw new InvalidArgumentError(\"fromSeconds requires a numerical input\");\n    } else {\n      return new DateTime({\n        ts: seconds * 1000,\n        zone: normalizeZone(options.zone, Settings.defaultZone),\n        loc: Locale.fromObject(options),\n      });\n    }\n  }\n\n  /**\n   * Create a DateTime from a JavaScript object with keys like 'year' and 'hour' with reasonable defaults.\n   * @param {Object} obj - the object to create the DateTime from\n   * @param {number} obj.year - a year, such as 1987\n   * @param {number} obj.month - a month, 1-12\n   * @param {number} obj.day - a day of the month, 1-31, depending on the month\n   * @param {number} obj.ordinal - day of the year, 1-365 or 366\n   * @param {number} obj.weekYear - an ISO week year\n   * @param {number} obj.weekNumber - an ISO week number, between 1 and 52 or 53, depending on the year\n   * @param {number} obj.weekday - an ISO weekday, 1-7, where 1 is Monday and 7 is Sunday\n   * @param {number} obj.localWeekYear - a week year, according to the locale\n   * @param {number} obj.localWeekNumber - a week number, between 1 and 52 or 53, depending on the year, according to the locale\n   * @param {number} obj.localWeekday - a weekday, 1-7, where 1 is the first and 7 is the last day of the week, according to the locale\n   * @param {number} obj.hour - hour of the day, 0-23\n   * @param {number} obj.minute - minute of the hour, 0-59\n   * @param {number} obj.second - second of the minute, 0-59\n   * @param {number} obj.millisecond - millisecond of the second, 0-999\n   * @param {Object} opts - options for creating this DateTime\n   * @param {string|Zone} [opts.zone='local'] - interpret the numbers in the context of a particular zone. Can take any value taken as the first argument to setZone()\n   * @param {string} [opts.locale='system\\'s locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromObject({ year: 1982, month: 5, day: 25}).toISODate() //=> '1982-05-25'\n   * @example DateTime.fromObject({ year: 1982 }).toISODate() //=> '1982-01-01'\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }) //~> today at 10:26:06\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'utc' }),\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'local' })\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'America/New_York' })\n   * @example DateTime.fromObject({ weekYear: 2016, weekNumber: 2, weekday: 3 }).toISODate() //=> '2016-01-13'\n   * @example DateTime.fromObject({ localWeekYear: 2022, localWeekNumber: 1, localWeekday: 1 }, { locale: \"en-US\" }).toISODate() //=> '2021-12-26'\n   * @return {DateTime}\n   */\n  static fromObject(obj, opts = {}) {\n    obj = obj || {};\n    const zoneToUse = normalizeZone(opts.zone, Settings.defaultZone);\n    if (!zoneToUse.isValid) {\n      return DateTime.invalid(unsupportedZone(zoneToUse));\n    }\n\n    const loc = Locale.fromObject(opts);\n    const normalized = normalizeObject(obj, normalizeUnitWithLocalWeeks);\n    const { minDaysInFirstWeek, startOfWeek } = usesLocalWeekValues(normalized, loc);\n\n    const tsNow = Settings.now(),\n      offsetProvis = !isUndefined(opts.specificOffset)\n        ? opts.specificOffset\n        : zoneToUse.offset(tsNow),\n      containsOrdinal = !isUndefined(normalized.ordinal),\n      containsGregorYear = !isUndefined(normalized.year),\n      containsGregorMD = !isUndefined(normalized.month) || !isUndefined(normalized.day),\n      containsGregor = containsGregorYear || containsGregorMD,\n      definiteWeekDef = normalized.weekYear || normalized.weekNumber;\n\n    // cases:\n    // just a weekday -> this week's instance of that weekday, no worries\n    // (gregorian data or ordinal) + (weekYear or weekNumber) -> error\n    // (gregorian month or day) + ordinal -> error\n    // otherwise just use weeks or ordinals or gregorian, depending on what's specified\n\n    if ((containsGregor || containsOrdinal) && definiteWeekDef) {\n      throw new ConflictingSpecificationError(\n        \"Can't mix weekYear/weekNumber units with year/month/day or ordinals\"\n      );\n    }\n\n    if (containsGregorMD && containsOrdinal) {\n      throw new ConflictingSpecificationError(\"Can't mix ordinal dates with month/day\");\n    }\n\n    const useWeekData = definiteWeekDef || (normalized.weekday && !containsGregor);\n\n    // configure ourselves to deal with gregorian dates or week stuff\n    let units,\n      defaultValues,\n      objNow = tsToObj(tsNow, offsetProvis);\n    if (useWeekData) {\n      units = orderedWeekUnits;\n      defaultValues = defaultWeekUnitValues;\n      objNow = gregorianToWeek(objNow, minDaysInFirstWeek, startOfWeek);\n    } else if (containsOrdinal) {\n      units = orderedOrdinalUnits;\n      defaultValues = defaultOrdinalUnitValues;\n      objNow = gregorianToOrdinal(objNow);\n    } else {\n      units = orderedUnits;\n      defaultValues = defaultUnitValues;\n    }\n\n    // set default values for missing stuff\n    let foundFirst = false;\n    for (const u of units) {\n      const v = normalized[u];\n      if (!isUndefined(v)) {\n        foundFirst = true;\n      } else if (foundFirst) {\n        normalized[u] = defaultValues[u];\n      } else {\n        normalized[u] = objNow[u];\n      }\n    }\n\n    // make sure the values we have are in range\n    const higherOrderInvalid = useWeekData\n        ? hasInvalidWeekData(normalized, minDaysInFirstWeek, startOfWeek)\n        : containsOrdinal\n        ? hasInvalidOrdinalData(normalized)\n        : hasInvalidGregorianData(normalized),\n      invalid = higherOrderInvalid || hasInvalidTimeData(normalized);\n\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    }\n\n    // compute the actual time\n    const gregorian = useWeekData\n        ? weekToGregorian(normalized, minDaysInFirstWeek, startOfWeek)\n        : containsOrdinal\n        ? ordinalToGregorian(normalized)\n        : normalized,\n      [tsFinal, offsetFinal] = objToTS(gregorian, offsetProvis, zoneToUse),\n      inst = new DateTime({\n        ts: tsFinal,\n        zone: zoneToUse,\n        o: offsetFinal,\n        loc,\n      });\n\n    // gregorian data + weekday serves only to validate\n    if (normalized.weekday && containsGregor && obj.weekday !== inst.weekday) {\n      return DateTime.invalid(\n        \"mismatched weekday\",\n        `you can't specify both a weekday of ${normalized.weekday} and a date of ${inst.toISO()}`\n      );\n    }\n\n    if (!inst.isValid) {\n      return DateTime.invalid(inst.invalid);\n    }\n\n    return inst;\n  }\n\n  /**\n   * Create a DateTime from an ISO 8601 string\n   * @param {string} text - the ISO string\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the time to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a fixed-offset zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} [opts.outputCalendar] - the output calendar to set on the resulting DateTime instance\n   * @param {string} [opts.numberingSystem] - the numbering system to set on the resulting DateTime instance\n   * @param {string} [opts.weekSettings] - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123')\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123+06:00')\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123+06:00', {setZone: true})\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123', {zone: 'utc'})\n   * @example DateTime.fromISO('2016-W05-4')\n   * @return {DateTime}\n   */\n  static fromISO(text, opts = {}) {\n    const [vals, parsedZone] = parseISODate(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"ISO 8601\", text);\n  }\n\n  /**\n   * Create a DateTime from an RFC 2822 string\n   * @param {string} text - the RFC 2822 string\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - convert the time to this zone. Since the offset is always specified in the string itself, this has no effect on the interpretation of string, merely the zone the resulting DateTime is expressed in.\n   * @param {boolean} [opts.setZone=false] - override the zone with a fixed-offset zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromRFC2822('25 Nov 2016 13:23:12 GMT')\n   * @example DateTime.fromRFC2822('Fri, 25 Nov 2016 13:23:12 +0600')\n   * @example DateTime.fromRFC2822('25 Nov 2016 13:23 Z')\n   * @return {DateTime}\n   */\n  static fromRFC2822(text, opts = {}) {\n    const [vals, parsedZone] = parseRFC2822Date(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"RFC 2822\", text);\n  }\n\n  /**\n   * Create a DateTime from an HTTP header date\n   * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec3.html#sec3.3.1\n   * @param {string} text - the HTTP header date\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - convert the time to this zone. Since HTTP dates are always in UTC, this has no effect on the interpretation of string, merely the zone the resulting DateTime is expressed in.\n   * @param {boolean} [opts.setZone=false] - override the zone with the fixed-offset zone specified in the string. For HTTP dates, this is always UTC, so this option is equivalent to setting the `zone` option to 'utc', but this option is included for consistency with similar methods.\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromHTTP('Sun, 06 Nov 1994 08:49:37 GMT')\n   * @example DateTime.fromHTTP('Sunday, 06-Nov-94 08:49:37 GMT')\n   * @example DateTime.fromHTTP('Sun Nov  6 08:49:37 1994')\n   * @return {DateTime}\n   */\n  static fromHTTP(text, opts = {}) {\n    const [vals, parsedZone] = parseHTTPDate(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"HTTP\", opts);\n  }\n\n  /**\n   * Create a DateTime from an input string and format string.\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale. For a table of tokens and their interpretations, see [here](https://moment.github.io/luxon/#/parsing?id=table-of-tokens).\n   * @param {string} text - the string to parse\n   * @param {string} fmt - the format the string is expected to be in (see the link below for the formats)\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the DateTime to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='en-US'] - a locale string to use when parsing. Will also set the DateTime to this locale\n   * @param {string} opts.numberingSystem - the numbering system to use when parsing. Will also set the resulting DateTime to this numbering system\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromFormat(text, fmt, opts = {}) {\n    if (isUndefined(text) || isUndefined(fmt)) {\n      throw new InvalidArgumentError(\"fromFormat requires an input string and a format\");\n    }\n\n    const { locale = null, numberingSystem = null } = opts,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      }),\n      [vals, parsedZone, specificOffset, invalid] = parseFromTokens(localeToUse, text, fmt);\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    } else {\n      return parseDataToDateTime(vals, parsedZone, opts, `format ${fmt}`, text, specificOffset);\n    }\n  }\n\n  /**\n   * @deprecated use fromFormat instead\n   */\n  static fromString(text, fmt, opts = {}) {\n    return DateTime.fromFormat(text, fmt, opts);\n  }\n\n  /**\n   * Create a DateTime from a SQL date, time, or datetime\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale\n   * @param {string} text - the string to parse\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the DateTime to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='en-US'] - a locale string to use when parsing. Will also set the DateTime to this locale\n   * @param {string} opts.numberingSystem - the numbering system to use when parsing. Will also set the resulting DateTime to this numbering system\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @example DateTime.fromSQL('2017-05-15')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342+06:00')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342 America/Los_Angeles')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342 America/Los_Angeles', { setZone: true })\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342', { zone: 'America/Los_Angeles' })\n   * @example DateTime.fromSQL('09:12:34.342')\n   * @return {DateTime}\n   */\n  static fromSQL(text, opts = {}) {\n    const [vals, parsedZone] = parseSQL(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"SQL\", text);\n  }\n\n  /**\n   * Create an invalid DateTime.\n   * @param {string} reason - simple string of why this DateTime is invalid. Should not contain parameters or anything else data-dependent.\n   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information\n   * @return {DateTime}\n   */\n  static invalid(reason, explanation = null) {\n    if (!reason) {\n      throw new InvalidArgumentError(\"need to specify a reason the DateTime is invalid\");\n    }\n\n    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);\n\n    if (Settings.throwOnInvalid) {\n      throw new InvalidDateTimeError(invalid);\n    } else {\n      return new DateTime({ invalid });\n    }\n  }\n\n  /**\n   * Check if an object is an instance of DateTime. Works across context boundaries\n   * @param {object} o\n   * @return {boolean}\n   */\n  static isDateTime(o) {\n    return (o && o.isLuxonDateTime) || false;\n  }\n\n  /**\n   * Produce the format string for a set of options\n   * @param formatOpts\n   * @param localeOpts\n   * @returns {string}\n   */\n  static parseFormatForOpts(formatOpts, localeOpts = {}) {\n    const tokenList = formatOptsToTokens(formatOpts, Locale.fromObject(localeOpts));\n    return !tokenList ? null : tokenList.map((t) => (t ? t.val : null)).join(\"\");\n  }\n\n  /**\n   * Produce the the fully expanded format token for the locale\n   * Does NOT quote characters, so quoted tokens will not round trip correctly\n   * @param fmt\n   * @param localeOpts\n   * @returns {string}\n   */\n  static expandFormat(fmt, localeOpts = {}) {\n    const expanded = expandMacroTokens(Formatter.parseFormat(fmt), Locale.fromObject(localeOpts));\n    return expanded.map((t) => t.val).join(\"\");\n  }\n\n  static resetCache() {\n    zoneOffsetTs = undefined;\n    zoneOffsetGuessCache.clear();\n  }\n\n  // INFO\n\n  /**\n   * Get the value of unit.\n   * @param {string} unit - a unit such as 'minute' or 'day'\n   * @example DateTime.local(2017, 7, 4).get('month'); //=> 7\n   * @example DateTime.local(2017, 7, 4).get('day'); //=> 4\n   * @return {number}\n   */\n  get(unit) {\n    return this[unit];\n  }\n\n  /**\n   * Returns whether the DateTime is valid. Invalid DateTimes occur when:\n   * * The DateTime was created from invalid calendar information, such as the 13th month or February 30\n   * * The DateTime was created by an operation on another invalid date\n   * @type {boolean}\n   */\n  get isValid() {\n    return this.invalid === null;\n  }\n\n  /**\n   * Returns an error code if this DateTime is invalid, or null if the DateTime is valid\n   * @type {string}\n   */\n  get invalidReason() {\n    return this.invalid ? this.invalid.reason : null;\n  }\n\n  /**\n   * Returns an explanation of why this DateTime became invalid, or null if the DateTime is valid\n   * @type {string}\n   */\n  get invalidExplanation() {\n    return this.invalid ? this.invalid.explanation : null;\n  }\n\n  /**\n   * Get the locale of a DateTime, such 'en-GB'. The locale is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get locale() {\n    return this.isValid ? this.loc.locale : null;\n  }\n\n  /**\n   * Get the numbering system of a DateTime, such 'beng'. The numbering system is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get numberingSystem() {\n    return this.isValid ? this.loc.numberingSystem : null;\n  }\n\n  /**\n   * Get the output calendar of a DateTime, such 'islamic'. The output calendar is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get outputCalendar() {\n    return this.isValid ? this.loc.outputCalendar : null;\n  }\n\n  /**\n   * Get the time zone associated with this DateTime.\n   * @type {Zone}\n   */\n  get zone() {\n    return this._zone;\n  }\n\n  /**\n   * Get the name of the time zone.\n   * @type {string}\n   */\n  get zoneName() {\n    return this.isValid ? this.zone.name : null;\n  }\n\n  /**\n   * Get the year\n   * @example DateTime.local(2017, 5, 25).year //=> 2017\n   * @type {number}\n   */\n  get year() {\n    return this.isValid ? this.c.year : NaN;\n  }\n\n  /**\n   * Get the quarter\n   * @example DateTime.local(2017, 5, 25).quarter //=> 2\n   * @type {number}\n   */\n  get quarter() {\n    return this.isValid ? Math.ceil(this.c.month / 3) : NaN;\n  }\n\n  /**\n   * Get the month (1-12).\n   * @example DateTime.local(2017, 5, 25).month //=> 5\n   * @type {number}\n   */\n  get month() {\n    return this.isValid ? this.c.month : NaN;\n  }\n\n  /**\n   * Get the day of the month (1-30ish).\n   * @example DateTime.local(2017, 5, 25).day //=> 25\n   * @type {number}\n   */\n  get day() {\n    return this.isValid ? this.c.day : NaN;\n  }\n\n  /**\n   * Get the hour of the day (0-23).\n   * @example DateTime.local(2017, 5, 25, 9).hour //=> 9\n   * @type {number}\n   */\n  get hour() {\n    return this.isValid ? this.c.hour : NaN;\n  }\n\n  /**\n   * Get the minute of the hour (0-59).\n   * @example DateTime.local(2017, 5, 25, 9, 30).minute //=> 30\n   * @type {number}\n   */\n  get minute() {\n    return this.isValid ? this.c.minute : NaN;\n  }\n\n  /**\n   * Get the second of the minute (0-59).\n   * @example DateTime.local(2017, 5, 25, 9, 30, 52).second //=> 52\n   * @type {number}\n   */\n  get second() {\n    return this.isValid ? this.c.second : NaN;\n  }\n\n  /**\n   * Get the millisecond of the second (0-999).\n   * @example DateTime.local(2017, 5, 25, 9, 30, 52, 654).millisecond //=> 654\n   * @type {number}\n   */\n  get millisecond() {\n    return this.isValid ? this.c.millisecond : NaN;\n  }\n\n  /**\n   * Get the week year\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2014, 12, 31).weekYear //=> 2015\n   * @type {number}\n   */\n  get weekYear() {\n    return this.isValid ? possiblyCachedWeekData(this).weekYear : NaN;\n  }\n\n  /**\n   * Get the week number of the week year (1-52ish).\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2017, 5, 25).weekNumber //=> 21\n   * @type {number}\n   */\n  get weekNumber() {\n    return this.isValid ? possiblyCachedWeekData(this).weekNumber : NaN;\n  }\n\n  /**\n   * Get the day of the week.\n   * 1 is Monday and 7 is Sunday\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2014, 11, 31).weekday //=> 4\n   * @type {number}\n   */\n  get weekday() {\n    return this.isValid ? possiblyCachedWeekData(this).weekday : NaN;\n  }\n\n  /**\n   * Returns true if this date is on a weekend according to the locale, false otherwise\n   * @returns {boolean}\n   */\n  get isWeekend() {\n    return this.isValid && this.loc.getWeekendDays().includes(this.weekday);\n  }\n\n  /**\n   * Get the day of the week according to the locale.\n   * 1 is the first day of the week and 7 is the last day of the week.\n   * If the locale assigns Sunday as the first day of the week, then a date which is a Sunday will return 1,\n   * @returns {number}\n   */\n  get localWeekday() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekday : NaN;\n  }\n\n  /**\n   * Get the week number of the week year according to the locale. Different locales assign week numbers differently,\n   * because the week can start on different days of the week (see localWeekday) and because a different number of days\n   * is required for a week to count as the first week of a year.\n   * @returns {number}\n   */\n  get localWeekNumber() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekNumber : NaN;\n  }\n\n  /**\n   * Get the week year according to the locale. Different locales assign week numbers (and therefor week years)\n   * differently, see localWeekNumber.\n   * @returns {number}\n   */\n  get localWeekYear() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekYear : NaN;\n  }\n\n  /**\n   * Get the ordinal (meaning the day of the year)\n   * @example DateTime.local(2017, 5, 25).ordinal //=> 145\n   * @type {number|DateTime}\n   */\n  get ordinal() {\n    return this.isValid ? gregorianToOrdinal(this.c).ordinal : NaN;\n  }\n\n  /**\n   * Get the human readable short month name, such as 'Oct'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).monthShort //=> Oct\n   * @type {string}\n   */\n  get monthShort() {\n    return this.isValid ? Info.months(\"short\", { locObj: this.loc })[this.month - 1] : null;\n  }\n\n  /**\n   * Get the human readable long month name, such as 'October'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).monthLong //=> October\n   * @type {string}\n   */\n  get monthLong() {\n    return this.isValid ? Info.months(\"long\", { locObj: this.loc })[this.month - 1] : null;\n  }\n\n  /**\n   * Get the human readable short weekday, such as 'Mon'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).weekdayShort //=> Mon\n   * @type {string}\n   */\n  get weekdayShort() {\n    return this.isValid ? Info.weekdays(\"short\", { locObj: this.loc })[this.weekday - 1] : null;\n  }\n\n  /**\n   * Get the human readable long weekday, such as 'Monday'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).weekdayLong //=> Monday\n   * @type {string}\n   */\n  get weekdayLong() {\n    return this.isValid ? Info.weekdays(\"long\", { locObj: this.loc })[this.weekday - 1] : null;\n  }\n\n  /**\n   * Get the UTC offset of this DateTime in minutes\n   * @example DateTime.now().offset //=> -240\n   * @example DateTime.utc().offset //=> 0\n   * @type {number}\n   */\n  get offset() {\n    return this.isValid ? +this.o : NaN;\n  }\n\n  /**\n   * Get the short human name for the zone's current offset, for example \"EST\" or \"EDT\".\n   * Defaults to the system's locale if no locale has been specified\n   * @type {string}\n   */\n  get offsetNameShort() {\n    if (this.isValid) {\n      return this.zone.offsetName(this.ts, {\n        format: \"short\",\n        locale: this.locale,\n      });\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Get the long human name for the zone's current offset, for example \"Eastern Standard Time\" or \"Eastern Daylight Time\".\n   * Defaults to the system's locale if no locale has been specified\n   * @type {string}\n   */\n  get offsetNameLong() {\n    if (this.isValid) {\n      return this.zone.offsetName(this.ts, {\n        format: \"long\",\n        locale: this.locale,\n      });\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Get whether this zone's offset ever changes, as in a DST.\n   * @type {boolean}\n   */\n  get isOffsetFixed() {\n    return this.isValid ? this.zone.isUniversal : null;\n  }\n\n  /**\n   * Get whether the DateTime is in a DST.\n   * @type {boolean}\n   */\n  get isInDST() {\n    if (this.isOffsetFixed) {\n      return false;\n    } else {\n      return (\n        this.offset > this.set({ month: 1, day: 1 }).offset ||\n        this.offset > this.set({ month: 5 }).offset\n      );\n    }\n  }\n\n  /**\n   * Get those DateTimes which have the same local time as this DateTime, but a different offset from UTC\n   * in this DateTime's zone. During DST changes local time can be ambiguous, for example\n   * `2023-10-29T02:30:00` in `Europe/Berlin` can have offset `+01:00` or `+02:00`.\n   * This method will return both possible DateTimes if this DateTime's local time is ambiguous.\n   * @returns {DateTime[]}\n   */\n  getPossibleOffsets() {\n    if (!this.isValid || this.isOffsetFixed) {\n      return [this];\n    }\n    const dayMs = 86400000;\n    const minuteMs = 60000;\n    const localTS = objToLocalTS(this.c);\n    const oEarlier = this.zone.offset(localTS - dayMs);\n    const oLater = this.zone.offset(localTS + dayMs);\n\n    const o1 = this.zone.offset(localTS - oEarlier * minuteMs);\n    const o2 = this.zone.offset(localTS - oLater * minuteMs);\n    if (o1 === o2) {\n      return [this];\n    }\n    const ts1 = localTS - o1 * minuteMs;\n    const ts2 = localTS - o2 * minuteMs;\n    const c1 = tsToObj(ts1, o1);\n    const c2 = tsToObj(ts2, o2);\n    if (\n      c1.hour === c2.hour &&\n      c1.minute === c2.minute &&\n      c1.second === c2.second &&\n      c1.millisecond === c2.millisecond\n    ) {\n      return [clone(this, { ts: ts1 }), clone(this, { ts: ts2 })];\n    }\n    return [this];\n  }\n\n  /**\n   * Returns true if this DateTime is in a leap year, false otherwise\n   * @example DateTime.local(2016).isInLeapYear //=> true\n   * @example DateTime.local(2013).isInLeapYear //=> false\n   * @type {boolean}\n   */\n  get isInLeapYear() {\n    return isLeapYear(this.year);\n  }\n\n  /**\n   * Returns the number of days in this DateTime's month\n   * @example DateTime.local(2016, 2).daysInMonth //=> 29\n   * @example DateTime.local(2016, 3).daysInMonth //=> 31\n   * @type {number}\n   */\n  get daysInMonth() {\n    return daysInMonth(this.year, this.month);\n  }\n\n  /**\n   * Returns the number of days in this DateTime's year\n   * @example DateTime.local(2016).daysInYear //=> 366\n   * @example DateTime.local(2013).daysInYear //=> 365\n   * @type {number}\n   */\n  get daysInYear() {\n    return this.isValid ? daysInYear(this.year) : NaN;\n  }\n\n  /**\n   * Returns the number of weeks in this DateTime's year\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2004).weeksInWeekYear //=> 53\n   * @example DateTime.local(2013).weeksInWeekYear //=> 52\n   * @type {number}\n   */\n  get weeksInWeekYear() {\n    return this.isValid ? weeksInWeekYear(this.weekYear) : NaN;\n  }\n\n  /**\n   * Returns the number of weeks in this DateTime's local week year\n   * @example DateTime.local(2020, 6, {locale: 'en-US'}).weeksInLocalWeekYear //=> 52\n   * @example DateTime.local(2020, 6, {locale: 'de-DE'}).weeksInLocalWeekYear //=> 53\n   * @type {number}\n   */\n  get weeksInLocalWeekYear() {\n    return this.isValid\n      ? weeksInWeekYear(\n          this.localWeekYear,\n          this.loc.getMinDaysInFirstWeek(),\n          this.loc.getStartOfWeek()\n        )\n      : NaN;\n  }\n\n  /**\n   * Returns the resolved Intl options for this DateTime.\n   * This is useful in understanding the behavior of formatting methods\n   * @param {Object} opts - the same options as toLocaleString\n   * @return {Object}\n   */\n  resolvedLocaleOptions(opts = {}) {\n    const { locale, numberingSystem, calendar } = Formatter.create(\n      this.loc.clone(opts),\n      opts\n    ).resolvedOptions(this);\n    return { locale, numberingSystem, outputCalendar: calendar };\n  }\n\n  // TRANSFORM\n\n  /**\n   * \"Set\" the DateTime's zone to UTC. Returns a newly-constructed DateTime.\n   *\n   * Equivalent to {@link DateTime#setZone}('utc')\n   * @param {number} [offset=0] - optionally, an offset from UTC in minutes\n   * @param {Object} [opts={}] - options to pass to `setZone()`\n   * @return {DateTime}\n   */\n  toUTC(offset = 0, opts = {}) {\n    return this.setZone(FixedOffsetZone.instance(offset), opts);\n  }\n\n  /**\n   * \"Set\" the DateTime's zone to the host's local zone. Returns a newly-constructed DateTime.\n   *\n   * Equivalent to `setZone('local')`\n   * @return {DateTime}\n   */\n  toLocal() {\n    return this.setZone(Settings.defaultZone);\n  }\n\n  /**\n   * \"Set\" the DateTime's zone to specified zone. Returns a newly-constructed DateTime.\n   *\n   * By default, the setter keeps the underlying time the same (as in, the same timestamp), but the new instance will report different local times and consider DSTs when making computations, as with {@link DateTime#plus}. You may wish to use {@link DateTime#toLocal} and {@link DateTime#toUTC} which provide simple convenience wrappers for commonly used zones.\n   * @param {string|Zone} [zone='local'] - a zone identifier. As a string, that can be any IANA zone supported by the host environment, or a fixed-offset name of the form 'UTC+3', or the strings 'local' or 'utc'. You may also supply an instance of a {@link DateTime#Zone} class.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.keepLocalTime=false] - If true, adjust the underlying time so that the local time stays the same, but in the target zone. You should rarely need this.\n   * @return {DateTime}\n   */\n  setZone(zone, { keepLocalTime = false, keepCalendarTime = false } = {}) {\n    zone = normalizeZone(zone, Settings.defaultZone);\n    if (zone.equals(this.zone)) {\n      return this;\n    } else if (!zone.isValid) {\n      return DateTime.invalid(unsupportedZone(zone));\n    } else {\n      let newTS = this.ts;\n      if (keepLocalTime || keepCalendarTime) {\n        const offsetGuess = zone.offset(this.ts);\n        const asObj = this.toObject();\n        [newTS] = objToTS(asObj, offsetGuess, zone);\n      }\n      return clone(this, { ts: newTS, zone });\n    }\n  }\n\n  /**\n   * \"Set\" the locale, numberingSystem, or outputCalendar. Returns a newly-constructed DateTime.\n   * @param {Object} properties - the properties to set\n   * @example DateTime.local(2017, 5, 25).reconfigure({ locale: 'en-GB' })\n   * @return {DateTime}\n   */\n  reconfigure({ locale, numberingSystem, outputCalendar } = {}) {\n    const loc = this.loc.clone({ locale, numberingSystem, outputCalendar });\n    return clone(this, { loc });\n  }\n\n  /**\n   * \"Set\" the locale. Returns a newly-constructed DateTime.\n   * Just a convenient alias for reconfigure({ locale })\n   * @example DateTime.local(2017, 5, 25).setLocale('en-GB')\n   * @return {DateTime}\n   */\n  setLocale(locale) {\n    return this.reconfigure({ locale });\n  }\n\n  /**\n   * \"Set\" the values of specified units. Returns a newly-constructed DateTime.\n   * You can only set units with this method; for \"setting\" metadata, see {@link DateTime#reconfigure} and {@link DateTime#setZone}.\n   *\n   * This method also supports setting locale-based week units, i.e. `localWeekday`, `localWeekNumber` and `localWeekYear`.\n   * They cannot be mixed with ISO-week units like `weekday`.\n   * @param {Object} values - a mapping of units to numbers\n   * @example dt.set({ year: 2017 })\n   * @example dt.set({ hour: 8, minute: 30 })\n   * @example dt.set({ weekday: 5 })\n   * @example dt.set({ year: 2005, ordinal: 234 })\n   * @return {DateTime}\n   */\n  set(values) {\n    if (!this.isValid) return this;\n\n    const normalized = normalizeObject(values, normalizeUnitWithLocalWeeks);\n    const { minDaysInFirstWeek, startOfWeek } = usesLocalWeekValues(normalized, this.loc);\n\n    const settingWeekStuff =\n        !isUndefined(normalized.weekYear) ||\n        !isUndefined(normalized.weekNumber) ||\n        !isUndefined(normalized.weekday),\n      containsOrdinal = !isUndefined(normalized.ordinal),\n      containsGregorYear = !isUndefined(normalized.year),\n      containsGregorMD = !isUndefined(normalized.month) || !isUndefined(normalized.day),\n      containsGregor = containsGregorYear || containsGregorMD,\n      definiteWeekDef = normalized.weekYear || normalized.weekNumber;\n\n    if ((containsGregor || containsOrdinal) && definiteWeekDef) {\n      throw new ConflictingSpecificationError(\n        \"Can't mix weekYear/weekNumber units with year/month/day or ordinals\"\n      );\n    }\n\n    if (containsGregorMD && containsOrdinal) {\n      throw new ConflictingSpecificationError(\"Can't mix ordinal dates with month/day\");\n    }\n\n    let mixed;\n    if (settingWeekStuff) {\n      mixed = weekToGregorian(\n        { ...gregorianToWeek(this.c, minDaysInFirstWeek, startOfWeek), ...normalized },\n        minDaysInFirstWeek,\n        startOfWeek\n      );\n    } else if (!isUndefined(normalized.ordinal)) {\n      mixed = ordinalToGregorian({ ...gregorianToOrdinal(this.c), ...normalized });\n    } else {\n      mixed = { ...this.toObject(), ...normalized };\n\n      // if we didn't set the day but we ended up on an overflow date,\n      // use the last day of the right month\n      if (isUndefined(normalized.day)) {\n        mixed.day = Math.min(daysInMonth(mixed.year, mixed.month), mixed.day);\n      }\n    }\n\n    const [ts, o] = objToTS(mixed, this.o, this.zone);\n    return clone(this, { ts, o });\n  }\n\n  /**\n   * Add a period of time to this DateTime and return the resulting DateTime\n   *\n   * Adding hours, minutes, seconds, or milliseconds increases the timestamp by the right number of milliseconds. Adding days, months, or years shifts the calendar, accounting for DSTs and leap years along the way. Thus, `dt.plus({ hours: 24 })` may result in a different time than `dt.plus({ days: 1 })` if there's a DST shift in between.\n   * @param {Duration|Object|number} duration - The amount to add. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   * @example DateTime.now().plus(123) //~> in 123 milliseconds\n   * @example DateTime.now().plus({ minutes: 15 }) //~> in 15 minutes\n   * @example DateTime.now().plus({ days: 1 }) //~> this time tomorrow\n   * @example DateTime.now().plus({ days: -1 }) //~> this time yesterday\n   * @example DateTime.now().plus({ hours: 3, minutes: 13 }) //~> in 3 hr, 13 min\n   * @example DateTime.now().plus(Duration.fromObject({ hours: 3, minutes: 13 })) //~> in 3 hr, 13 min\n   * @return {DateTime}\n   */\n  plus(duration) {\n    if (!this.isValid) return this;\n    const dur = Duration.fromDurationLike(duration);\n    return clone(this, adjustTime(this, dur));\n  }\n\n  /**\n   * Subtract a period of time to this DateTime and return the resulting DateTime\n   * See {@link DateTime#plus}\n   * @param {Duration|Object|number} duration - The amount to subtract. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   @return {DateTime}\n   */\n  minus(duration) {\n    if (!this.isValid) return this;\n    const dur = Duration.fromDurationLike(duration).negate();\n    return clone(this, adjustTime(this, dur));\n  }\n\n  /**\n   * \"Set\" this DateTime to the beginning of a unit of time.\n   * @param {string} unit - The unit to go to the beginning of. Can be 'year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second', or 'millisecond'.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week\n   * @example DateTime.local(2014, 3, 3).startOf('month').toISODate(); //=> '2014-03-01'\n   * @example DateTime.local(2014, 3, 3).startOf('year').toISODate(); //=> '2014-01-01'\n   * @example DateTime.local(2014, 3, 3).startOf('week').toISODate(); //=> '2014-03-03', weeks always start on Mondays\n   * @example DateTime.local(2014, 3, 3, 5, 30).startOf('day').toISOTime(); //=> '00:00.000-05:00'\n   * @example DateTime.local(2014, 3, 3, 5, 30).startOf('hour').toISOTime(); //=> '05:00:00.000-05:00'\n   * @return {DateTime}\n   */\n  startOf(unit, { useLocaleWeeks = false } = {}) {\n    if (!this.isValid) return this;\n\n    const o = {},\n      normalizedUnit = Duration.normalizeUnit(unit);\n    switch (normalizedUnit) {\n      case \"years\":\n        o.month = 1;\n      // falls through\n      case \"quarters\":\n      case \"months\":\n        o.day = 1;\n      // falls through\n      case \"weeks\":\n      case \"days\":\n        o.hour = 0;\n      // falls through\n      case \"hours\":\n        o.minute = 0;\n      // falls through\n      case \"minutes\":\n        o.second = 0;\n      // falls through\n      case \"seconds\":\n        o.millisecond = 0;\n        break;\n      case \"milliseconds\":\n        break;\n      // no default, invalid units throw in normalizeUnit()\n    }\n\n    if (normalizedUnit === \"weeks\") {\n      if (useLocaleWeeks) {\n        const startOfWeek = this.loc.getStartOfWeek();\n        const { weekday } = this;\n        if (weekday < startOfWeek) {\n          o.weekNumber = this.weekNumber - 1;\n        }\n        o.weekday = startOfWeek;\n      } else {\n        o.weekday = 1;\n      }\n    }\n\n    if (normalizedUnit === \"quarters\") {\n      const q = Math.ceil(this.month / 3);\n      o.month = (q - 1) * 3 + 1;\n    }\n\n    return this.set(o);\n  }\n\n  /**\n   * \"Set\" this DateTime to the end (meaning the last millisecond) of a unit of time\n   * @param {string} unit - The unit to go to the end of. Can be 'year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second', or 'millisecond'.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week\n   * @example DateTime.local(2014, 3, 3).endOf('month').toISO(); //=> '2014-03-31T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3).endOf('year').toISO(); //=> '2014-12-31T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3).endOf('week').toISO(); // => '2014-03-09T23:59:59.999-05:00', weeks start on Mondays\n   * @example DateTime.local(2014, 3, 3, 5, 30).endOf('day').toISO(); //=> '2014-03-03T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3, 5, 30).endOf('hour').toISO(); //=> '2014-03-03T05:59:59.999-05:00'\n   * @return {DateTime}\n   */\n  endOf(unit, opts) {\n    return this.isValid\n      ? this.plus({ [unit]: 1 })\n          .startOf(unit, opts)\n          .minus(1)\n      : this;\n  }\n\n  // OUTPUT\n\n  /**\n   * Returns a string representation of this DateTime formatted according to the specified format string.\n   * **You may not want this.** See {@link DateTime#toLocaleString} for a more flexible formatting tool. For a table of tokens and their interpretations, see [here](https://moment.github.io/luxon/#/formatting?id=table-of-tokens).\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale.\n   * @param {string} fmt - the format string\n   * @param {Object} opts - opts to override the configuration options on this DateTime\n   * @example DateTime.now().toFormat('yyyy LLL dd') //=> '2017 Apr 22'\n   * @example DateTime.now().setLocale('fr').toFormat('yyyy LLL dd') //=> '2017 avr. 22'\n   * @example DateTime.now().toFormat('yyyy LLL dd', { locale: \"fr\" }) //=> '2017 avr. 22'\n   * @example DateTime.now().toFormat(\"HH 'hours and' mm 'minutes'\") //=> '20 hours and 55 minutes'\n   * @return {string}\n   */\n  toFormat(fmt, opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.loc.redefaultToEN(opts)).formatDateTimeFromString(this, fmt)\n      : INVALID;\n  }\n\n  /**\n   * Returns a localized string representing this date. Accepts the same options as the Intl.DateTimeFormat constructor and any presets defined by Luxon, such as `DateTime.DATE_FULL` or `DateTime.TIME_SIMPLE`.\n   * The exact behavior of this method is browser-specific, but in general it will return an appropriate representation\n   * of the DateTime in the assigned locale.\n   * Defaults to the system's locale if no locale has been specified\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param formatOpts {Object} - Intl.DateTimeFormat constructor options and configuration options\n   * @param {Object} opts - opts to override the configuration options on this DateTime\n   * @example DateTime.now().toLocaleString(); //=> 4/20/2017\n   * @example DateTime.now().setLocale('en-gb').toLocaleString(); //=> '20/04/2017'\n   * @example DateTime.now().toLocaleString(DateTime.DATE_FULL); //=> 'April 20, 2017'\n   * @example DateTime.now().toLocaleString(DateTime.DATE_FULL, { locale: 'fr' }); //=> '28 août 2022'\n   * @example DateTime.now().toLocaleString(DateTime.TIME_SIMPLE); //=> '11:32 AM'\n   * @example DateTime.now().toLocaleString(DateTime.DATETIME_SHORT); //=> '4/20/2017, 11:32 AM'\n   * @example DateTime.now().toLocaleString({ weekday: 'long', month: 'long', day: '2-digit' }); //=> 'Thursday, April 20'\n   * @example DateTime.now().toLocaleString({ weekday: 'short', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit' }); //=> 'Thu, Apr 20, 11:27 AM'\n   * @example DateTime.now().toLocaleString({ hour: '2-digit', minute: '2-digit', hourCycle: 'h23' }); //=> '11:32'\n   * @return {string}\n   */\n  toLocaleString(formatOpts = Formats.DATE_SHORT, opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.loc.clone(opts), formatOpts).formatDateTime(this)\n      : INVALID;\n  }\n\n  /**\n   * Returns an array of format \"parts\", meaning individual tokens along with metadata. This is allows callers to post-process individual sections of the formatted output.\n   * Defaults to the system's locale if no locale has been specified\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat/formatToParts\n   * @param opts {Object} - Intl.DateTimeFormat constructor options, same as `toLocaleString`.\n   * @example DateTime.now().toLocaleParts(); //=> [\n   *                                   //=>   { type: 'day', value: '25' },\n   *                                   //=>   { type: 'literal', value: '/' },\n   *                                   //=>   { type: 'month', value: '05' },\n   *                                   //=>   { type: 'literal', value: '/' },\n   *                                   //=>   { type: 'year', value: '1982' }\n   *                                   //=> ]\n   */\n  toLocaleParts(opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.loc.clone(opts), opts).formatDateTimeParts(this)\n      : [];\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.extendedZone=false] - add the time zone format extension\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @param {string} [opts.precision='milliseconds'] - truncate output to desired presicion: 'years', 'months', 'days', 'hours', 'minutes', 'seconds' or 'milliseconds'. When precision and suppressSeconds or suppressMilliseconds are used together, precision sets the maximum unit shown in the output, however seconds or milliseconds will still be suppressed if they are 0.\n   * @example DateTime.utc(1983, 5, 25).toISO() //=> '1982-05-25T00:00:00.000Z'\n   * @example DateTime.now().toISO() //=> '2017-04-22T20:47:05.335-04:00'\n   * @example DateTime.now().toISO({ includeOffset: false }) //=> '2017-04-22T20:47:05.335'\n   * @example DateTime.now().toISO({ format: 'basic' }) //=> '20170422T204705.335-0400'\n   * @example DateTime.now().toISO({ precision: 'day' }) //=> '2017-04-22Z'\n   * @example DateTime.now().toISO({ precision: 'minute' }) //=> '2017-04-22T20:47Z'\n   * @return {string|null}\n   */\n  toISO({\n    format = \"extended\",\n    suppressSeconds = false,\n    suppressMilliseconds = false,\n    includeOffset = true,\n    extendedZone = false,\n    precision = \"milliseconds\",\n  } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    precision = normalizeUnit(precision);\n    const ext = format === \"extended\";\n\n    let c = toISODate(this, ext, precision);\n    if (orderedUnits.indexOf(precision) >= 3) c += \"T\";\n    c += toISOTime(\n      this,\n      ext,\n      suppressSeconds,\n      suppressMilliseconds,\n      includeOffset,\n      extendedZone,\n      precision\n    );\n    return c;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's date component\n   * @param {Object} opts - options\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @param {string} [opts.precision='day'] - truncate output to desired precision: 'years', 'months', or 'days'.\n   * @example DateTime.utc(1982, 5, 25).toISODate() //=> '1982-05-25'\n   * @example DateTime.utc(1982, 5, 25).toISODate({ format: 'basic' }) //=> '19820525'\n   * @example DateTime.utc(1982, 5, 25).toISODate({ precision: 'month' }) //=> '1982-05'\n   * @return {string|null}\n   */\n  toISODate({ format = \"extended\", precision = \"day\" } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n    return toISODate(this, format === \"extended\", normalizeUnit(precision));\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's week date\n   * @example DateTime.utc(1982, 5, 25).toISOWeekDate() //=> '1982-W21-2'\n   * @return {string}\n   */\n  toISOWeekDate() {\n    return toTechFormat(this, \"kkkk-'W'WW-c\");\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's time component\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.extendedZone=true] - add the time zone format extension\n   * @param {boolean} [opts.includePrefix=false] - include the `T` prefix\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @param {string} [opts.precision='milliseconds'] - truncate output to desired presicion: 'hours', 'minutes', 'seconds' or 'milliseconds'. When precision and suppressSeconds or suppressMilliseconds are used together, precision sets the maximum unit shown in the output, however seconds or milliseconds will still be suppressed if they are 0.\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime() //=> '07:34:19.361Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34, seconds: 0, milliseconds: 0 }).toISOTime({ suppressSeconds: true }) //=> '07:34Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime({ format: 'basic' }) //=> '073419.361Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime({ includePrefix: true }) //=> 'T07:34:19.361Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34, second: 56 }).toISOTime({ precision: 'minute' }) //=> '07:34Z'\n   * @return {string}\n   */\n  toISOTime({\n    suppressMilliseconds = false,\n    suppressSeconds = false,\n    includeOffset = true,\n    includePrefix = false,\n    extendedZone = false,\n    format = \"extended\",\n    precision = \"milliseconds\",\n  } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    precision = normalizeUnit(precision);\n    let c = includePrefix && orderedUnits.indexOf(precision) >= 3 ? \"T\" : \"\";\n    return (\n      c +\n      toISOTime(\n        this,\n        format === \"extended\",\n        suppressSeconds,\n        suppressMilliseconds,\n        includeOffset,\n        extendedZone,\n        precision\n      )\n    );\n  }\n\n  /**\n   * Returns an RFC 2822-compatible string representation of this DateTime\n   * @example DateTime.utc(2014, 7, 13).toRFC2822() //=> 'Sun, 13 Jul 2014 00:00:00 +0000'\n   * @example DateTime.local(2014, 7, 13).toRFC2822() //=> 'Sun, 13 Jul 2014 00:00:00 -0400'\n   * @return {string}\n   */\n  toRFC2822() {\n    return toTechFormat(this, \"EEE, dd LLL yyyy HH:mm:ss ZZZ\", false);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in HTTP headers. The output is always expressed in GMT.\n   * Specifically, the string conforms to RFC 1123.\n   * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec3.html#sec3.3.1\n   * @example DateTime.utc(2014, 7, 13).toHTTP() //=> 'Sun, 13 Jul 2014 00:00:00 GMT'\n   * @example DateTime.utc(2014, 7, 13, 19).toHTTP() //=> 'Sun, 13 Jul 2014 19:00:00 GMT'\n   * @return {string}\n   */\n  toHTTP() {\n    return toTechFormat(this.toUTC(), \"EEE, dd LLL yyyy HH:mm:ss 'GMT'\");\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL Date\n   * @example DateTime.utc(2014, 7, 13).toSQLDate() //=> '2014-07-13'\n   * @return {string|null}\n   */\n  toSQLDate() {\n    if (!this.isValid) {\n      return null;\n    }\n    return toISODate(this, true);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL Time\n   * @param {Object} opts - options\n   * @param {boolean} [opts.includeZone=false] - include the zone, such as 'America/New_York'. Overrides includeOffset.\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.includeOffsetSpace=true] - include the space between the time and the offset, such as '05:15:16.345 -04:00'\n   * @example DateTime.utc().toSQL() //=> '05:15:16.345'\n   * @example DateTime.now().toSQL() //=> '05:15:16.345 -04:00'\n   * @example DateTime.now().toSQL({ includeOffset: false }) //=> '05:15:16.345'\n   * @example DateTime.now().toSQL({ includeZone: false }) //=> '05:15:16.345 America/New_York'\n   * @return {string}\n   */\n  toSQLTime({ includeOffset = true, includeZone = false, includeOffsetSpace = true } = {}) {\n    let fmt = \"HH:mm:ss.SSS\";\n\n    if (includeZone || includeOffset) {\n      if (includeOffsetSpace) {\n        fmt += \" \";\n      }\n      if (includeZone) {\n        fmt += \"z\";\n      } else if (includeOffset) {\n        fmt += \"ZZ\";\n      }\n    }\n\n    return toTechFormat(this, fmt, true);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL DateTime\n   * @param {Object} opts - options\n   * @param {boolean} [opts.includeZone=false] - include the zone, such as 'America/New_York'. Overrides includeOffset.\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.includeOffsetSpace=true] - include the space between the time and the offset, such as '05:15:16.345 -04:00'\n   * @example DateTime.utc(2014, 7, 13).toSQL() //=> '2014-07-13 00:00:00.000 Z'\n   * @example DateTime.local(2014, 7, 13).toSQL() //=> '2014-07-13 00:00:00.000 -04:00'\n   * @example DateTime.local(2014, 7, 13).toSQL({ includeOffset: false }) //=> '2014-07-13 00:00:00.000'\n   * @example DateTime.local(2014, 7, 13).toSQL({ includeZone: true }) //=> '2014-07-13 00:00:00.000 America/New_York'\n   * @return {string}\n   */\n  toSQL(opts = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    return `${this.toSQLDate()} ${this.toSQLTime(opts)}`;\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for debugging\n   * @return {string}\n   */\n  toString() {\n    return this.isValid ? this.toISO() : INVALID;\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for the REPL.\n   * @return {string}\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n    if (this.isValid) {\n      return `DateTime { ts: ${this.toISO()}, zone: ${this.zone.name}, locale: ${this.locale} }`;\n    } else {\n      return `DateTime { Invalid, reason: ${this.invalidReason} }`;\n    }\n  }\n\n  /**\n   * Returns the epoch milliseconds of this DateTime. Alias of {@link DateTime#toMillis}\n   * @return {number}\n   */\n  valueOf() {\n    return this.toMillis();\n  }\n\n  /**\n   * Returns the epoch milliseconds of this DateTime.\n   * @return {number}\n   */\n  toMillis() {\n    return this.isValid ? this.ts : NaN;\n  }\n\n  /**\n   * Returns the epoch seconds (including milliseconds in the fractional part) of this DateTime.\n   * @return {number}\n   */\n  toSeconds() {\n    return this.isValid ? this.ts / 1000 : NaN;\n  }\n\n  /**\n   * Returns the epoch seconds (as a whole number) of this DateTime.\n   * @return {number}\n   */\n  toUnixInteger() {\n    return this.isValid ? Math.floor(this.ts / 1000) : NaN;\n  }\n\n  /**\n   * Returns an ISO 8601 representation of this DateTime appropriate for use in JSON.\n   * @return {string}\n   */\n  toJSON() {\n    return this.toISO();\n  }\n\n  /**\n   * Returns a BSON serializable equivalent to this DateTime.\n   * @return {Date}\n   */\n  toBSON() {\n    return this.toJSDate();\n  }\n\n  /**\n   * Returns a JavaScript object with this DateTime's year, month, day, and so on.\n   * @param opts - options for generating the object\n   * @param {boolean} [opts.includeConfig=false] - include configuration attributes in the output\n   * @example DateTime.now().toObject() //=> { year: 2017, month: 4, day: 22, hour: 20, minute: 49, second: 42, millisecond: 268 }\n   * @return {Object}\n   */\n  toObject(opts = {}) {\n    if (!this.isValid) return {};\n\n    const base = { ...this.c };\n\n    if (opts.includeConfig) {\n      base.outputCalendar = this.outputCalendar;\n      base.numberingSystem = this.loc.numberingSystem;\n      base.locale = this.loc.locale;\n    }\n    return base;\n  }\n\n  /**\n   * Returns a JavaScript Date equivalent to this DateTime.\n   * @return {Date}\n   */\n  toJSDate() {\n    return new Date(this.isValid ? this.ts : NaN);\n  }\n\n  // COMPARE\n\n  /**\n   * Return the difference between two DateTimes as a Duration.\n   * @param {DateTime} otherDateTime - the DateTime to compare this one to\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or array of units (such as 'hours' or 'days') to include in the duration.\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @example\n   * var i1 = DateTime.fromISO('1982-05-25T09:45'),\n   *     i2 = DateTime.fromISO('1983-10-14T10:30');\n   * i2.diff(i1).toObject() //=> { milliseconds: 43807500000 }\n   * i2.diff(i1, 'hours').toObject() //=> { hours: 12168.75 }\n   * i2.diff(i1, ['months', 'days']).toObject() //=> { months: 16, days: 19.03125 }\n   * i2.diff(i1, ['months', 'days', 'hours']).toObject() //=> { months: 16, days: 19, hours: 0.75 }\n   * @return {Duration}\n   */\n  diff(otherDateTime, unit = \"milliseconds\", opts = {}) {\n    if (!this.isValid || !otherDateTime.isValid) {\n      return Duration.invalid(\"created by diffing an invalid DateTime\");\n    }\n\n    const durOpts = { locale: this.locale, numberingSystem: this.numberingSystem, ...opts };\n\n    const units = maybeArray(unit).map(Duration.normalizeUnit),\n      otherIsLater = otherDateTime.valueOf() > this.valueOf(),\n      earlier = otherIsLater ? this : otherDateTime,\n      later = otherIsLater ? otherDateTime : this,\n      diffed = diff(earlier, later, units, durOpts);\n\n    return otherIsLater ? diffed.negate() : diffed;\n  }\n\n  /**\n   * Return the difference between this DateTime and right now.\n   * See {@link DateTime#diff}\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or units units (such as 'hours' or 'days') to include in the duration\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @return {Duration}\n   */\n  diffNow(unit = \"milliseconds\", opts = {}) {\n    return this.diff(DateTime.now(), unit, opts);\n  }\n\n  /**\n   * Return an Interval spanning between this DateTime and another DateTime\n   * @param {DateTime} otherDateTime - the other end point of the Interval\n   * @return {Interval|DateTime}\n   */\n  until(otherDateTime) {\n    return this.isValid ? Interval.fromDateTimes(this, otherDateTime) : this;\n  }\n\n  /**\n   * Return whether this DateTime is in the same unit of time as another DateTime.\n   * Higher-order units must also be identical for this function to return `true`.\n   * Note that time zones are **ignored** in this comparison, which compares the **local** calendar time. Use {@link DateTime#setZone} to convert one of the dates if needed.\n   * @param {DateTime} otherDateTime - the other DateTime\n   * @param {string} unit - the unit of time to check sameness on\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week; only the locale of this DateTime is used\n   * @example DateTime.now().hasSame(otherDT, 'day'); //~> true if otherDT is in the same current calendar day\n   * @return {boolean}\n   */\n  hasSame(otherDateTime, unit, opts) {\n    if (!this.isValid) return false;\n\n    const inputMs = otherDateTime.valueOf();\n    const adjustedToZone = this.setZone(otherDateTime.zone, { keepLocalTime: true });\n    return (\n      adjustedToZone.startOf(unit, opts) <= inputMs && inputMs <= adjustedToZone.endOf(unit, opts)\n    );\n  }\n\n  /**\n   * Equality check\n   * Two DateTimes are equal if and only if they represent the same millisecond, have the same zone and location, and are both valid.\n   * To compare just the millisecond values, use `+dt1 === +dt2`.\n   * @param {DateTime} other - the other DateTime\n   * @return {boolean}\n   */\n  equals(other) {\n    return (\n      this.isValid &&\n      other.isValid &&\n      this.valueOf() === other.valueOf() &&\n      this.zone.equals(other.zone) &&\n      this.loc.equals(other.loc)\n    );\n  }\n\n  /**\n   * Returns a string representation of a this time relative to now, such as \"in two days\". Can only internationalize if your\n   * platform supports Intl.RelativeTimeFormat. Rounds towards zero by default.\n   * @param {Object} options - options that affect the output\n   * @param {DateTime} [options.base=DateTime.now()] - the DateTime to use as the basis to which this time is compared. Defaults to now.\n   * @param {string} [options.style=\"long\"] - the style of units, must be \"long\", \"short\", or \"narrow\"\n   * @param {string|string[]} options.unit - use a specific unit or array of units; if omitted, or an array, the method will pick the best unit. Use an array or one of \"years\", \"quarters\", \"months\", \"weeks\", \"days\", \"hours\", \"minutes\", or \"seconds\"\n   * @param {boolean} [options.round=true] - whether to round the numbers in the output.\n   * @param {string} [options.rounding=\"trunc\"] - rounding method to use when rounding the numbers in the output. Can be \"trunc\" (toward zero), \"expand\" (away from zero), \"round\", \"floor\", or \"ceil\".\n   * @param {number} [options.padding=0] - padding in milliseconds. This allows you to round up the result if it fits inside the threshold. Don't use in combination with {round: false} because the decimal output will include the padding.\n   * @param {string} options.locale - override the locale of this DateTime\n   * @param {string} options.numberingSystem - override the numberingSystem of this DateTime. The Intl system may choose not to honor this\n   * @example DateTime.now().plus({ days: 1 }).toRelative() //=> \"in 1 day\"\n   * @example DateTime.now().setLocale(\"es\").toRelative({ days: 1 }) //=> \"dentro de 1 día\"\n   * @example DateTime.now().plus({ days: 1 }).toRelative({ locale: \"fr\" }) //=> \"dans 23 heures\"\n   * @example DateTime.now().minus({ days: 2 }).toRelative() //=> \"2 days ago\"\n   * @example DateTime.now().minus({ days: 2 }).toRelative({ unit: \"hours\" }) //=> \"48 hours ago\"\n   * @example DateTime.now().minus({ hours: 36 }).toRelative({ round: false }) //=> \"1.5 days ago\"\n   */\n  toRelative(options = {}) {\n    if (!this.isValid) return null;\n    const base = options.base || DateTime.fromObject({}, { zone: this.zone }),\n      padding = options.padding ? (this < base ? -options.padding : options.padding) : 0;\n    let units = [\"years\", \"months\", \"days\", \"hours\", \"minutes\", \"seconds\"];\n    let unit = options.unit;\n    if (Array.isArray(options.unit)) {\n      units = options.unit;\n      unit = undefined;\n    }\n    return diffRelative(base, this.plus(padding), {\n      ...options,\n      numeric: \"always\",\n      units,\n      unit,\n    });\n  }\n\n  /**\n   * Returns a string representation of this date relative to today, such as \"yesterday\" or \"next month\".\n   * Only internationalizes on platforms that supports Intl.RelativeTimeFormat.\n   * @param {Object} options - options that affect the output\n   * @param {DateTime} [options.base=DateTime.now()] - the DateTime to use as the basis to which this time is compared. Defaults to now.\n   * @param {string} options.locale - override the locale of this DateTime\n   * @param {string} options.unit - use a specific unit; if omitted, the method will pick the unit. Use one of \"years\", \"quarters\", \"months\", \"weeks\", or \"days\"\n   * @param {string} options.numberingSystem - override the numberingSystem of this DateTime. The Intl system may choose not to honor this\n   * @example DateTime.now().plus({ days: 1 }).toRelativeCalendar() //=> \"tomorrow\"\n   * @example DateTime.now().setLocale(\"es\").plus({ days: 1 }).toRelative() //=> \"\"mañana\"\n   * @example DateTime.now().plus({ days: 1 }).toRelativeCalendar({ locale: \"fr\" }) //=> \"demain\"\n   * @example DateTime.now().minus({ days: 2 }).toRelativeCalendar() //=> \"2 days ago\"\n   */\n  toRelativeCalendar(options = {}) {\n    if (!this.isValid) return null;\n\n    return diffRelative(options.base || DateTime.fromObject({}, { zone: this.zone }), this, {\n      ...options,\n      numeric: \"auto\",\n      units: [\"years\", \"months\", \"days\"],\n      calendary: true,\n    });\n  }\n\n  /**\n   * Return the min of several date times\n   * @param {...DateTime} dateTimes - the DateTimes from which to choose the minimum\n   * @return {DateTime} the min DateTime, or undefined if called with no argument\n   */\n  static min(...dateTimes) {\n    if (!dateTimes.every(DateTime.isDateTime)) {\n      throw new InvalidArgumentError(\"min requires all arguments be DateTimes\");\n    }\n    return bestBy(dateTimes, (i) => i.valueOf(), Math.min);\n  }\n\n  /**\n   * Return the max of several date times\n   * @param {...DateTime} dateTimes - the DateTimes from which to choose the maximum\n   * @return {DateTime} the max DateTime, or undefined if called with no argument\n   */\n  static max(...dateTimes) {\n    if (!dateTimes.every(DateTime.isDateTime)) {\n      throw new InvalidArgumentError(\"max requires all arguments be DateTimes\");\n    }\n    return bestBy(dateTimes, (i) => i.valueOf(), Math.max);\n  }\n\n  // MISC\n\n  /**\n   * Explain how a string would be parsed by fromFormat()\n   * @param {string} text - the string to parse\n   * @param {string} fmt - the format the string is expected to be in (see description)\n   * @param {Object} options - options taken by fromFormat()\n   * @return {Object}\n   */\n  static fromFormatExplain(text, fmt, options = {}) {\n    const { locale = null, numberingSystem = null } = options,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      });\n    return explainFromTokens(localeToUse, text, fmt);\n  }\n\n  /**\n   * @deprecated use fromFormatExplain instead\n   */\n  static fromStringExplain(text, fmt, options = {}) {\n    return DateTime.fromFormatExplain(text, fmt, options);\n  }\n\n  /**\n   * Build a parser for `fmt` using the given locale. This parser can be passed\n   * to {@link DateTime.fromFormatParser} to a parse a date in this format. This\n   * can be used to optimize cases where many dates need to be parsed in a\n   * specific format.\n   *\n   * @param {String} fmt - the format the string is expected to be in (see\n   * description)\n   * @param {Object} options - options used to set locale and numberingSystem\n   * for parser\n   * @returns {TokenParser} - opaque object to be used\n   */\n  static buildFormatParser(fmt, options = {}) {\n    const { locale = null, numberingSystem = null } = options,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      });\n    return new TokenParser(localeToUse, fmt);\n  }\n\n  /**\n   * Create a DateTime from an input string and format parser.\n   *\n   * The format parser must have been created with the same locale as this call.\n   *\n   * @param {String} text - the string to parse\n   * @param {TokenParser} formatParser - parser from {@link DateTime.buildFormatParser}\n   * @param {Object} opts - options taken by fromFormat()\n   * @returns {DateTime}\n   */\n  static fromFormatParser(text, formatParser, opts = {}) {\n    if (isUndefined(text) || isUndefined(formatParser)) {\n      throw new InvalidArgumentError(\n        \"fromFormatParser requires an input string and a format parser\"\n      );\n    }\n    const { locale = null, numberingSystem = null } = opts,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      });\n\n    if (!localeToUse.equals(formatParser.locale)) {\n      throw new InvalidArgumentError(\n        `fromFormatParser called with a locale of ${localeToUse}, ` +\n          `but the format parser was created for ${formatParser.locale}`\n      );\n    }\n\n    const { result, zone, specificOffset, invalidReason } = formatParser.explainFromTokens(text);\n\n    if (invalidReason) {\n      return DateTime.invalid(invalidReason);\n    } else {\n      return parseDataToDateTime(\n        result,\n        zone,\n        opts,\n        `format ${formatParser.format}`,\n        text,\n        specificOffset\n      );\n    }\n  }\n\n  // FORMAT PRESETS\n\n  /**\n   * {@link DateTime#toLocaleString} format like 10/14/1983\n   * @type {Object}\n   */\n  static get DATE_SHORT() {\n    return Formats.DATE_SHORT;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_MED() {\n    return Formats.DATE_MED;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Fri, Oct 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_MED_WITH_WEEKDAY() {\n    return Formats.DATE_MED_WITH_WEEKDAY;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_FULL() {\n    return Formats.DATE_FULL;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Tuesday, October 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_HUGE() {\n    return Formats.DATE_HUGE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_SIMPLE() {\n    return Formats.TIME_SIMPLE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_SECONDS() {\n    return Formats.TIME_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_SHORT_OFFSET() {\n    return Formats.TIME_WITH_SHORT_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_LONG_OFFSET() {\n    return Formats.TIME_WITH_LONG_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_SIMPLE() {\n    return Formats.TIME_24_SIMPLE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_SECONDS() {\n    return Formats.TIME_24_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 EDT', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_SHORT_OFFSET() {\n    return Formats.TIME_24_WITH_SHORT_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 Eastern Daylight Time', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_LONG_OFFSET() {\n    return Formats.TIME_24_WITH_LONG_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '10/14/1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_SHORT() {\n    return Formats.DATETIME_SHORT;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '10/14/1983, 9:30:33 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_SHORT_WITH_SECONDS() {\n    return Formats.DATETIME_SHORT_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED() {\n    return Formats.DATETIME_MED;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983, 9:30:33 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED_WITH_SECONDS() {\n    return Formats.DATETIME_MED_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Fri, 14 Oct 1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED_WITH_WEEKDAY() {\n    return Formats.DATETIME_MED_WITH_WEEKDAY;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983, 9:30 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_FULL() {\n    return Formats.DATETIME_FULL;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983, 9:30:33 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_FULL_WITH_SECONDS() {\n    return Formats.DATETIME_FULL_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Friday, October 14, 1983, 9:30 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_HUGE() {\n    return Formats.DATETIME_HUGE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Friday, October 14, 1983, 9:30:33 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_HUGE_WITH_SECONDS() {\n    return Formats.DATETIME_HUGE_WITH_SECONDS;\n  }\n}\n\n/**\n * @private\n */\nexport function friendlyDateTime(dateTimeish) {\n  if (DateTime.isDateTime(dateTimeish)) {\n    return dateTimeish;\n  } else if (dateTimeish && dateTimeish.valueOf && isNumber(dateTimeish.valueOf())) {\n    return DateTime.fromJSDate(dateTimeish);\n  } else if (dateTimeish && typeof dateTimeish === \"object\") {\n    return DateTime.fromObject(dateTimeish);\n  } else {\n    throw new InvalidArgumentError(\n      `Unknown datetime argument: ${dateTimeish}, of type ${typeof dateTimeish}`\n    );\n  }\n}\n", "import DateTime from \"./datetime.js\";\nimport Duration from \"./duration.js\";\nimport Interval from \"./interval.js\";\nimport Info from \"./info.js\";\nimport Zone from \"./zone.js\";\nimport FixedOffsetZone from \"./zones/fixedOffsetZone.js\";\nimport IANAZone from \"./zones/IANAZone.js\";\nimport InvalidZone from \"./zones/invalidZone.js\";\nimport SystemZone from \"./zones/systemZone.js\";\nimport Settings from \"./settings.js\";\n\nconst VERSION = \"3.7.2\";\n\nexport {\n  VERSION,\n  DateTime,\n  Duration,\n  Interval,\n  Info,\n  Zone,\n  FixedOffsetZone,\n  IANAZone,\n  InvalidZone,\n  SystemZone,\n  Settings,\n};\n"], "names": ["LuxonError", "Error", "InvalidDateTimeError", "constructor", "reason", "toMessage", "InvalidIntervalError", "InvalidDurationError", "ConflictingSpecificationError", "InvalidUnitError", "unit", "InvalidArgumentError", "ZoneIsAbstractError", "n", "s", "l", "DATE_SHORT", "year", "month", "day", "DATE_MED", "DATE_MED_WITH_WEEKDAY", "weekday", "DATE_FULL", "DATE_HUGE", "TIME_SIMPLE", "hour", "minute", "TIME_WITH_SECONDS", "second", "TIME_WITH_SHORT_OFFSET", "timeZoneName", "TIME_WITH_LONG_OFFSET", "TIME_24_SIMPLE", "hourCycle", "TIME_24_WITH_SECONDS", "TIME_24_WITH_SHORT_OFFSET", "TIME_24_WITH_LONG_OFFSET", "DATETIME_SHORT", "DATETIME_SHORT_WITH_SECONDS", "DATETIME_MED", "DATETIME_MED_WITH_SECONDS", "DATETIME_MED_WITH_WEEKDAY", "DATETIME_FULL", "DATETIME_FULL_WITH_SECONDS", "DATETIME_HUGE", "DATETIME_HUGE_WITH_SECONDS", "Zone", "type", "name", "<PERSON><PERSON><PERSON><PERSON>", "isUniversal", "offsetName", "ts", "opts", "formatOffset", "format", "offset", "equals", "otherZone", "<PERSON><PERSON><PERSON><PERSON>", "singleton", "SystemZone", "instance", "Intl", "DateTimeFormat", "resolvedOptions", "timeZone", "locale", "parseZoneInfo", "Date", "getTimezoneOffset", "dtfCache", "Map", "makeDTF", "zoneName", "dtf", "get", "undefined", "hour12", "era", "set", "typeToPos", "hackyOffset", "date", "formatted", "replace", "parsed", "exec", "fMonth", "fDay", "fYear", "fadOrBc", "fHour", "fMinute", "fSecond", "partsOffset", "formatToParts", "filled", "i", "length", "value", "pos", "isUndefined", "parseInt", "ianaZone<PERSON>ache", "IANAZone", "create", "zone", "resetCache", "clear", "isValidSpecifier", "isValidZone", "e", "valid", "NaN", "isNaN", "adOrBc", "Math", "abs", "adjustedHour", "asUTC", "objToLocalTS", "millisecond", "asTS", "over", "intlLFCache", "getCachedLF", "locString", "key", "JSON", "stringify", "ListFormat", "intlDTCache", "getCachedDTF", "intlNumCache", "getCachedINF", "inf", "NumberFormat", "intlRelCache", "getCachedRTF", "base", "cacheKeyOpts", "RelativeTimeFormat", "sysLocaleCache", "systemLocale", "intlResolvedOptionsCache", "getCachedIntResolvedOptions", "weekInfoCache", "getCachedWeekInfo", "data", "Locale", "getWeekInfo", "weekInfo", "fallbackWeekSettings", "parseLocaleString", "localeStr", "xIndex", "indexOf", "substring", "uIndex", "options", "selectedStr", "smaller", "numberingSystem", "calendar", "intlConfigString", "outputCalendar", "includes", "mapMonths", "f", "ms", "dt", "DateTime", "utc", "push", "mapWeekdays", "listStuff", "loc", "englishFn", "intlFn", "mode", "listingMode", "supportsFastNumbers", "startsWith", "PolyNumberFormatter", "intl", "forceSimple", "padTo", "floor", "otherOpts", "Object", "keys", "intlOpts", "useGrouping", "minimumIntegerDigits", "fixed", "roundTo", "padStart", "PolyDateFormatter", "originalZone", "z", "gmtOffset", "offsetZ", "setZone", "plus", "minutes", "map", "join", "toJSDate", "parts", "part", "PolyRelFormatter", "isEnglish", "style", "hasRelative", "rtf", "count", "English", "numeric", "firstDay", "minimalDays", "weekend", "fromOpts", "weekSettings", "defaultToEN", "specifiedLocale", "Settings", "defaultLocale", "localeR", "numberingSystemR", "defaultNumberingSystem", "outputCalendarR", "defaultOutputCalendar", "weekSettingsR", "validateWeekSettings", "defaultWeekSettings", "fromObject", "numbering", "parsedLocale", "parsedNumberingSystem", "parsedOutputCalendar", "weekdaysCache", "standalone", "monthsCache", "meridiemCache", "eraCache", "fastNumbersCached", "fastNumbers", "isActuallyEn", "has<PERSON>o<PERSON><PERSON><PERSON><PERSON>", "clone", "alts", "getOwnPropertyNames", "redefaultToEN", "redefaultToSystem", "months", "monthSpecialCase", "formatStr", "mapper", "extract", "dt<PERSON><PERSON><PERSON><PERSON>", "weekdays", "meridiems", "eras", "field", "df", "results", "matching", "find", "m", "toLowerCase", "numberF<PERSON>atter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getWeekSettings", "hasLocaleWeekInfo", "getStartOfWeek", "getMinDaysInFirstWeek", "getWeekendDays", "other", "toString", "FixedOffsetZone", "utcInstance", "parseSpecifier", "r", "match", "signedOffset", "InvalidZone", "normalizeZone", "input", "defaultZone", "isString", "lowered", "isNumber", "numberingSystems", "arab", "arabext", "bali", "beng", "deva", "fullwide", "gujr", "hanidec", "khmr", "knda", "laoo", "limb", "mlym", "mong", "mymr", "orya", "tamldec", "telu", "thai", "tibt", "latn", "numberingSystemsUTF16", "hanidecChars", "split", "parseDigits", "str", "code", "charCodeAt", "search", "min", "max", "digitRegexCache", "resetDigitRegexCache", "digitRegex", "append", "ns", "appendCache", "regex", "RegExp", "now", "twoDigitCutoffYear", "throwOnInvalid", "cutoffYear", "t", "resetCaches", "Invalid", "explanation", "nonLeapLadder", "<PERSON><PERSON><PERSON><PERSON>", "unitOutOfRange", "dayOfWeek", "d", "UTC", "setUTCFullYear", "getUTCFullYear", "js", "getUTCDay", "computeOrdinal", "isLeapYear", "uncomputeOrdinal", "ordinal", "table", "month0", "findIndex", "isoWeekdayToLocal", "isoWeekday", "startOfWeek", "gregorianToWeek", "greg<PERSON><PERSON><PERSON>", "minDaysInFirstWeek", "weekNumber", "weekYear", "weeksInWeekYear", "timeObject", "weekT<PERSON><PERSON><PERSON><PERSON><PERSON>", "weekData", "weekdayOfJan4", "yearInDays", "daysInYear", "gregorianToOrdinal", "gregData", "ordinalToGregorian", "ordinalData", "usesLocalWeekValues", "obj", "hasLocaleWeekData", "localWeekday", "localWeekNumber", "localWeekYear", "hasIsoWeekData", "hasInvalidWeekData", "validYear", "isInteger", "validWeek", "integerBetween", "validWeekday", "hasInvalidOrdinalData", "validOrdinal", "hasInvalidGregorianData", "valid<PERSON><PERSON><PERSON>", "validDay", "daysInMonth", "hasInvalidTimeData", "validHour", "validMinute", "validSecond", "validMillisecond", "o", "isDate", "prototype", "call", "maybeA<PERSON>y", "thing", "Array", "isArray", "bestBy", "arr", "by", "compare", "reduce", "best", "next", "pair", "pick", "a", "k", "hasOwnProperty", "prop", "settings", "some", "v", "from", "bottom", "top", "floorMod", "x", "isNeg", "padded", "parseInteger", "string", "parseFloating", "parseFloat", "parse<PERSON><PERSON><PERSON>", "fraction", "number", "digits", "rounding", "factor", "ceil", "trunc", "round", "RangeError", "mod<PERSON>onth", "modYear", "firstWeekOffset", "fwdlw", "weekOffset", "weekOffsetNext", "untruncateYear", "offsetFormat", "modified", "offHourStr", "offMinuteStr", "offHour", "Number", "offMin", "offMinSigned", "is", "asNumber", "numericValue", "isFinite", "normalizeObject", "normalizer", "normalized", "u", "hours", "sign", "monthsLong", "monthsShort", "<PERSON><PERSON><PERSON><PERSON>", "weekdaysLong", "weekdaysShort", "weekdaysNarrow", "erasLong", "erasShort", "eras<PERSON><PERSON><PERSON>", "meridiemForDateTime", "weekdayForDateTime", "monthForDateTime", "eraForDateTime", "formatRelativeTime", "narrow", "units", "years", "quarters", "weeks", "days", "seconds", "lastable", "isDay", "isInPast", "fmtValue", "singular", "lilUnits", "fmtUnit", "stringifyTokens", "splits", "tokenToString", "token", "literal", "val", "macroTokenToFormatOpts", "D", "Formats", "DD", "DDD", "DDDD", "tt", "ttt", "tttt", "T", "TT", "TTT", "TTTT", "ff", "fff", "ffff", "F", "FF", "FFF", "FFFF", "<PERSON><PERSON><PERSON>", "parseFormat", "fmt", "current", "currentFull", "bracketed", "c", "char<PERSON>t", "test", "formatOpts", "systemLoc", "formatWithSystemDefault", "formatDateTime", "formatDateTimeParts", "formatInterval", "interval", "start", "formatRange", "end", "num", "p", "signDisplay", "formatDateTimeFromString", "knownEnglish", "useDateTimeFormatter", "isOffsetFixed", "allowZ", "meridiem", "<PERSON><PERSON><PERSON><PERSON>", "slice", "quarter", "formatDurationFromString", "dur", "invertLargest", "signMode", "tokenToField", "lildur", "info", "mapped", "inversionFactor", "isNegativeDuration", "largestUnit", "tokens", "realTokens", "found", "concat", "collapsed", "shiftTo", "filter", "durationInfo", "values", "ianaRegex", "combineRegexes", "regexes", "full", "source", "combineExtractors", "extractors", "mergedVals", "mergedZone", "cursor", "ex", "parse", "patterns", "extractor", "simpleParse", "ret", "offsetRegex", "isoExtendedZone", "isoTimeBaseRegex", "isoTimeRegex", "isoTimeExtensionRegex", "isoYmdRegex", "isoWeekRegex", "isoOrdinalRegex", "extractISOWeekData", "extractISOOrdinalData", "sqlYmdRegex", "sqlTimeRegex", "sqlTimeExtensionRegex", "int", "fallback", "extractISOYmd", "item", "extractISOTime", "milliseconds", "extractISOOffset", "local", "fullOffset", "extractIANAZone", "isoTimeOnly", "isoDuration", "extractISODuration", "yearStr", "monthStr", "weekStr", "dayStr", "hourStr", "minuteStr", "secondStr", "millisecondsStr", "hasNegativePrefix", "negativeSeconds", "maybeNegate", "force", "obsOffsets", "GMT", "EDT", "EST", "CDT", "CST", "MDT", "MST", "PDT", "PST", "fromStrings", "weekdayStr", "result", "rfc2822", "extractRFC2822", "obsOffset", "milOffset", "preprocessRFC2822", "trim", "rfc1123", "rfc850", "ascii", "extractRFC1123Or850", "extractASCII", "isoYmdWithTimeExtensionRegex", "isoWeekWithTimeExtensionRegex", "isoOrdinalWithTimeExtensionRegex", "isoTimeCombinedRegex", "extractISOYmdTimeAndOffset", "extractISOWeekTimeAndOffset", "extractISOOrdinalDateAndTime", "extractISOTimeAndOffset", "parseISODate", "parseRFC2822Date", "parseHTTPDate", "parseISODuration", "extractISOTimeOnly", "parseISOTimeOnly", "sqlYmdWithTimeExtensionRegex", "sqlTimeCombinedRegex", "extractISOTimeOffsetAndIANAZone", "parseSQL", "INVALID", "lowOrderMatrix", "casualMatrix", "daysInYearAccurate", "daysInMonthAccurate", "accurateMatrix", "orderedUnits", "reverseUnits", "reverse", "conf", "conversionAccuracy", "matrix", "Duration", "durationTo<PERSON>illis", "vals", "_vals$milliseconds", "sum", "normalizeValues", "reduceRight", "previous", "previousVal", "conv", "rollUp", "removeZeroes", "newVals", "entries", "config", "accurate", "invalid", "isLuxonDuration", "fromMillis", "normalizeUnit", "fromDurationLike", "durationLike", "isDuration", "fromISO", "text", "fromISOTime", "week", "toFormat", "fmtOpts", "toHuman", "showZeros", "unitDisplay", "listStyle", "toObject", "toISO", "toISOTime", "millis", "<PERSON><PERSON><PERSON><PERSON>", "suppressMilliseconds", "suppressSeconds", "includePrefix", "includeOffset", "dateTime", "toJSON", "Symbol", "for", "invalidReason", "valueOf", "duration", "minus", "negate", "mapUnits", "fn", "mixed", "reconfigure", "as", "normalize", "rescale", "shiftToAll", "built", "accumulated", "lastUnit", "own", "ak", "negated", "removeZeros", "invalidExplanation", "eq", "v1", "v2", "validateStartEnd", "Interval", "isLuxonInterval", "fromDateTimes", "builtStart", "friendlyDateTime", "builtEnd", "validateError", "after", "before", "startIsValid", "endIsValid", "isInterval", "lastDateTime", "toDuration", "startOf", "useLocaleWeeks", "diff", "<PERSON><PERSON><PERSON>", "isEmpty", "isAfter", "isBefore", "contains", "splitAt", "dateTimes", "sorted", "sort", "b", "added", "splitBy", "idx", "divideEqually", "numberOfParts", "overlaps", "abutsStart", "abutsEnd", "engulfs", "intersection", "union", "merge", "intervals", "final", "sofar", "xor", "currentCount", "ends", "time", "flattened", "difference", "toLocaleString", "toISODate", "dateFormat", "separator", "mapEndpoints", "mapFn", "Info", "hasDST", "proto", "isValidIANAZone", "locObj", "getMinimumDaysInFirstWeek", "getWeekendWeekdays", "monthsFormat", "weekdaysFormat", "features", "relative", "localeWeek", "dayDiff", "earlier", "later", "utcDayStart", "toUTC", "keepLocalTime", "highOrderDiffs", "differs", "lowestOrder", "highWater", "differ", "remaining<PERSON>ill<PERSON>", "lowerOrderUnits", "MISSING_FTP", "intUnit", "post", "deser", "NBSP", "String", "fromCharCode", "spaceOrNBSP", "spaceOrNBSPRegExp", "fixListRegex", "stripInsensitivities", "oneOf", "strings", "startIndex", "groups", "h", "simple", "escapeToken", "unitForToken", "one", "two", "three", "four", "six", "oneOrTwo", "oneToThree", "oneToSix", "oneToNine", "twoToFour", "fourToSix", "unitate", "partTypeStyleToTokenVal", "short", "long", "dayperiod", "<PERSON><PERSON><PERSON><PERSON>", "hour24", "tokenForPart", "resolvedOpts", "isSpace", "actualType", "buildRegex", "re", "handlers", "matches", "all", "matchIndex", "dateTimeFromMatches", "to<PERSON>ield", "specificOffset", "Z", "q", "M", "G", "y", "S", "dummyDateTimeCache", "getDummyDateTime", "maybeExpandMacroToken", "formatOptsToTokens", "expandMacroTokens", "Token<PERSON><PERSON><PERSON>", "disqualifying<PERSON>nit", "regexString", "explainFromTokens", "rawMatches", "parser", "parseFromTokens", "formatter", "MAX_DATE", "unsupportedZone", "possiblyCachedWeekData", "possiblyCachedLocalWeekData", "localWeekData", "inst", "old", "fixOffset", "localTS", "tz", "ut<PERSON><PERSON><PERSON><PERSON>", "o2", "o3", "tsToObj", "getUTCMonth", "getUTCDate", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "getUTCMilliseconds", "objToTS", "adjustTime", "oPre", "millisToAdd", "parseDataToDateTime", "parsedZone", "interpretationZone", "toTechFormat", "extended", "precision", "longFormat", "extendedZone", "showSeconds", "defaultUnitValues", "defaultWeekUnitValues", "defaultOrdinalUnitValues", "orderedWeekUnits", "orderedOrdinalUnits", "weeknumber", "weeksnumber", "weeknumbers", "weekyear", "weekyears", "normalizeUnitWithLocalWeeks", "guessOffsetForZone", "zoneOffsetTs", "offsetGuess", "zoneOffsetGuessCache", "quickDT", "<PERSON><PERSON><PERSON><PERSON>", "diffRelative", "calendary", "lastOpts", "argList", "args", "unchanged", "ot", "_zone", "isLuxonDateTime", "arguments", "fromJSDate", "zoneToUse", "fromSeconds", "tsNow", "containsOrdinal", "containsGregorYear", "containsGregorMD", "<PERSON><PERSON><PERSON><PERSON>", "definiteWeekDef", "useWeekData", "defaultValues", "objNow", "<PERSON><PERSON><PERSON><PERSON>", "higherOrderInvalid", "gregorian", "tsFinal", "offsetFinal", "fromRFC2822", "fromHTTP", "fromFormat", "localeToUse", "fromString", "fromSQL", "isDateTime", "parseFormatForOpts", "localeOpts", "tokenList", "expandFormat", "expanded", "isWeekend", "monthShort", "monthLong", "weekdayShort", "weekdayLong", "offsetNameShort", "offsetNameLong", "isInDST", "getPossibleOffsets", "dayMs", "minuteMs", "oEarlier", "oLater", "o1", "ts1", "ts2", "c1", "c2", "isInLeapYear", "weeksInLocalWeekYear", "resolvedLocaleOptions", "toLocal", "keepCalendarTime", "newTS", "as<PERSON>bj", "setLocale", "settingWeekStuff", "normalizedUnit", "endOf", "toLocaleParts", "ext", "toISOWeekDate", "toRFC2822", "toHTTP", "toSQLDate", "toSQLTime", "includeZone", "includeOffsetSpace", "toSQL", "to<PERSON><PERSON><PERSON><PERSON>", "toUnixInteger", "toBSON", "includeConfig", "otherDateTime", "durOpts", "otherIsLater", "diffed", "diffNow", "until", "inputMs", "adjustedToZone", "toRelative", "padding", "toRelativeCalendar", "every", "fromFormatExplain", "fromStringExplain", "buildFormatParser", "fromFormatParser", "format<PERSON><PERSON>er", "dateTimeish", "VERSION"], "mappings": ";;;;AAAA;;AAEA;AACA;AACA;AACA,MAAMA,UAAU,SAASC,KAAK,CAAC,EAAA;;AAE/B;AACA;AACA;AACO,MAAMC,oBAAoB,SAASF,UAAU,CAAC;EACnDG,WAAWA,CAACC,MAAM,EAAE;IAClB,KAAK,CAAE,qBAAoBA,MAAM,CAACC,SAAS,EAAG,EAAC,CAAC,CAAA;AAClD,GAAA;AACF,CAAA;;AAEA;AACA;AACA;AACO,MAAMC,oBAAoB,SAASN,UAAU,CAAC;EACnDG,WAAWA,CAACC,MAAM,EAAE;IAClB,KAAK,CAAE,qBAAoBA,MAAM,CAACC,SAAS,EAAG,EAAC,CAAC,CAAA;AAClD,GAAA;AACF,CAAA;;AAEA;AACA;AACA;AACO,MAAME,oBAAoB,SAASP,UAAU,CAAC;EACnDG,WAAWA,CAACC,MAAM,EAAE;IAClB,KAAK,CAAE,qBAAoBA,MAAM,CAACC,SAAS,EAAG,EAAC,CAAC,CAAA;AAClD,GAAA;AACF,CAAA;;AAEA;AACA;AACA;AACO,MAAMG,6BAA6B,SAASR,UAAU,CAAC,EAAA;;AAE9D;AACA;AACA;AACO,MAAMS,gBAAgB,SAAST,UAAU,CAAC;EAC/CG,WAAWA,CAACO,IAAI,EAAE;AAChB,IAAA,KAAK,CAAE,CAAA,aAAA,EAAeA,IAAK,CAAA,CAAC,CAAC,CAAA;AAC/B,GAAA;AACF,CAAA;;AAEA;AACA;AACA;AACO,MAAMC,oBAAoB,SAASX,UAAU,CAAC,EAAA;;AAErD;AACA;AACA;AACO,MAAMY,mBAAmB,SAASZ,UAAU,CAAC;AAClDG,EAAAA,WAAWA,GAAG;IACZ,KAAK,CAAC,2BAA2B,CAAC,CAAA;AACpC,GAAA;AACF;;AC5DA;AACA;AACA;;AAEA,MAAMU,CAAC,GAAG,SAAS;AACjBC,EAAAA,CAAC,GAAG,OAAO;AACXC,EAAAA,CAAC,GAAG,MAAM,CAAA;AAEL,MAAMC,UAAU,GAAG;AACxBC,EAAAA,IAAI,EAAEJ,CAAC;AACPK,EAAAA,KAAK,EAAEL,CAAC;AACRM,EAAAA,GAAG,EAAEN,CAAAA;AACP,CAAC,CAAA;AAEM,MAAMO,QAAQ,GAAG;AACtBH,EAAAA,IAAI,EAAEJ,CAAC;AACPK,EAAAA,KAAK,EAAEJ,CAAC;AACRK,EAAAA,GAAG,EAAEN,CAAAA;AACP,CAAC,CAAA;AAEM,MAAMQ,qBAAqB,GAAG;AACnCJ,EAAAA,IAAI,EAAEJ,CAAC;AACPK,EAAAA,KAAK,EAAEJ,CAAC;AACRK,EAAAA,GAAG,EAAEN,CAAC;AACNS,EAAAA,OAAO,EAAER,CAAAA;AACX,CAAC,CAAA;AAEM,MAAMS,SAAS,GAAG;AACvBN,EAAAA,IAAI,EAAEJ,CAAC;AACPK,EAAAA,KAAK,EAAEH,CAAC;AACRI,EAAAA,GAAG,EAAEN,CAAAA;AACP,CAAC,CAAA;AAEM,MAAMW,SAAS,GAAG;AACvBP,EAAAA,IAAI,EAAEJ,CAAC;AACPK,EAAAA,KAAK,EAAEH,CAAC;AACRI,EAAAA,GAAG,EAAEN,CAAC;AACNS,EAAAA,OAAO,EAAEP,CAAAA;AACX,CAAC,CAAA;AAEM,MAAMU,WAAW,GAAG;AACzBC,EAAAA,IAAI,EAAEb,CAAC;AACPc,EAAAA,MAAM,EAAEd,CAAAA;AACV,CAAC,CAAA;AAEM,MAAMe,iBAAiB,GAAG;AAC/BF,EAAAA,IAAI,EAAEb,CAAC;AACPc,EAAAA,MAAM,EAAEd,CAAC;AACTgB,EAAAA,MAAM,EAAEhB,CAAAA;AACV,CAAC,CAAA;AAEM,MAAMiB,sBAAsB,GAAG;AACpCJ,EAAAA,IAAI,EAAEb,CAAC;AACPc,EAAAA,MAAM,EAAEd,CAAC;AACTgB,EAAAA,MAAM,EAAEhB,CAAC;AACTkB,EAAAA,YAAY,EAAEjB,CAAAA;AAChB,CAAC,CAAA;AAEM,MAAMkB,qBAAqB,GAAG;AACnCN,EAAAA,IAAI,EAAEb,CAAC;AACPc,EAAAA,MAAM,EAAEd,CAAC;AACTgB,EAAAA,MAAM,EAAEhB,CAAC;AACTkB,EAAAA,YAAY,EAAEhB,CAAAA;AAChB,CAAC,CAAA;AAEM,MAAMkB,cAAc,GAAG;AAC5BP,EAAAA,IAAI,EAAEb,CAAC;AACPc,EAAAA,MAAM,EAAEd,CAAC;AACTqB,EAAAA,SAAS,EAAE,KAAA;AACb,CAAC,CAAA;AAEM,MAAMC,oBAAoB,GAAG;AAClCT,EAAAA,IAAI,EAAEb,CAAC;AACPc,EAAAA,MAAM,EAAEd,CAAC;AACTgB,EAAAA,MAAM,EAAEhB,CAAC;AACTqB,EAAAA,SAAS,EAAE,KAAA;AACb,CAAC,CAAA;AAEM,MAAME,yBAAyB,GAAG;AACvCV,EAAAA,IAAI,EAAEb,CAAC;AACPc,EAAAA,MAAM,EAAEd,CAAC;AACTgB,EAAAA,MAAM,EAAEhB,CAAC;AACTqB,EAAAA,SAAS,EAAE,KAAK;AAChBH,EAAAA,YAAY,EAAEjB,CAAAA;AAChB,CAAC,CAAA;AAEM,MAAMuB,wBAAwB,GAAG;AACtCX,EAAAA,IAAI,EAAEb,CAAC;AACPc,EAAAA,MAAM,EAAEd,CAAC;AACTgB,EAAAA,MAAM,EAAEhB,CAAC;AACTqB,EAAAA,SAAS,EAAE,KAAK;AAChBH,EAAAA,YAAY,EAAEhB,CAAAA;AAChB,CAAC,CAAA;AAEM,MAAMuB,cAAc,GAAG;AAC5BrB,EAAAA,IAAI,EAAEJ,CAAC;AACPK,EAAAA,KAAK,EAAEL,CAAC;AACRM,EAAAA,GAAG,EAAEN,CAAC;AACNa,EAAAA,IAAI,EAAEb,CAAC;AACPc,EAAAA,MAAM,EAAEd,CAAAA;AACV,CAAC,CAAA;AAEM,MAAM0B,2BAA2B,GAAG;AACzCtB,EAAAA,IAAI,EAAEJ,CAAC;AACPK,EAAAA,KAAK,EAAEL,CAAC;AACRM,EAAAA,GAAG,EAAEN,CAAC;AACNa,EAAAA,IAAI,EAAEb,CAAC;AACPc,EAAAA,MAAM,EAAEd,CAAC;AACTgB,EAAAA,MAAM,EAAEhB,CAAAA;AACV,CAAC,CAAA;AAEM,MAAM2B,YAAY,GAAG;AAC1BvB,EAAAA,IAAI,EAAEJ,CAAC;AACPK,EAAAA,KAAK,EAAEJ,CAAC;AACRK,EAAAA,GAAG,EAAEN,CAAC;AACNa,EAAAA,IAAI,EAAEb,CAAC;AACPc,EAAAA,MAAM,EAAEd,CAAAA;AACV,CAAC,CAAA;AAEM,MAAM4B,yBAAyB,GAAG;AACvCxB,EAAAA,IAAI,EAAEJ,CAAC;AACPK,EAAAA,KAAK,EAAEJ,CAAC;AACRK,EAAAA,GAAG,EAAEN,CAAC;AACNa,EAAAA,IAAI,EAAEb,CAAC;AACPc,EAAAA,MAAM,EAAEd,CAAC;AACTgB,EAAAA,MAAM,EAAEhB,CAAAA;AACV,CAAC,CAAA;AAEM,MAAM6B,yBAAyB,GAAG;AACvCzB,EAAAA,IAAI,EAAEJ,CAAC;AACPK,EAAAA,KAAK,EAAEJ,CAAC;AACRK,EAAAA,GAAG,EAAEN,CAAC;AACNS,EAAAA,OAAO,EAAER,CAAC;AACVY,EAAAA,IAAI,EAAEb,CAAC;AACPc,EAAAA,MAAM,EAAEd,CAAAA;AACV,CAAC,CAAA;AAEM,MAAM8B,aAAa,GAAG;AAC3B1B,EAAAA,IAAI,EAAEJ,CAAC;AACPK,EAAAA,KAAK,EAAEH,CAAC;AACRI,EAAAA,GAAG,EAAEN,CAAC;AACNa,EAAAA,IAAI,EAAEb,CAAC;AACPc,EAAAA,MAAM,EAAEd,CAAC;AACTkB,EAAAA,YAAY,EAAEjB,CAAAA;AAChB,CAAC,CAAA;AAEM,MAAM8B,0BAA0B,GAAG;AACxC3B,EAAAA,IAAI,EAAEJ,CAAC;AACPK,EAAAA,KAAK,EAAEH,CAAC;AACRI,EAAAA,GAAG,EAAEN,CAAC;AACNa,EAAAA,IAAI,EAAEb,CAAC;AACPc,EAAAA,MAAM,EAAEd,CAAC;AACTgB,EAAAA,MAAM,EAAEhB,CAAC;AACTkB,EAAAA,YAAY,EAAEjB,CAAAA;AAChB,CAAC,CAAA;AAEM,MAAM+B,aAAa,GAAG;AAC3B5B,EAAAA,IAAI,EAAEJ,CAAC;AACPK,EAAAA,KAAK,EAAEH,CAAC;AACRI,EAAAA,GAAG,EAAEN,CAAC;AACNS,EAAAA,OAAO,EAAEP,CAAC;AACVW,EAAAA,IAAI,EAAEb,CAAC;AACPc,EAAAA,MAAM,EAAEd,CAAC;AACTkB,EAAAA,YAAY,EAAEhB,CAAAA;AAChB,CAAC,CAAA;AAEM,MAAM+B,0BAA0B,GAAG;AACxC7B,EAAAA,IAAI,EAAEJ,CAAC;AACPK,EAAAA,KAAK,EAAEH,CAAC;AACRI,EAAAA,GAAG,EAAEN,CAAC;AACNS,EAAAA,OAAO,EAAEP,CAAC;AACVW,EAAAA,IAAI,EAAEb,CAAC;AACPc,EAAAA,MAAM,EAAEd,CAAC;AACTgB,EAAAA,MAAM,EAAEhB,CAAC;AACTkB,EAAAA,YAAY,EAAEhB,CAAAA;AAChB,CAAC;;AC7KD;AACA;AACA;AACe,MAAMgC,IAAI,CAAC;AACxB;AACF;AACA;AACA;AACA;EACE,IAAIC,IAAIA,GAAG;IACT,MAAM,IAAIpC,mBAAmB,EAAE,CAAA;AACjC,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAIqC,IAAIA,GAAG;IACT,MAAM,IAAIrC,mBAAmB,EAAE,CAAA;AACjC,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIsC,QAAQA,GAAG;IACb,OAAO,IAAI,CAACD,IAAI,CAAA;AAClB,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAIE,WAAWA,GAAG;IAChB,MAAM,IAAIvC,mBAAmB,EAAE,CAAA;AACjC,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEwC,EAAAA,UAAUA,CAACC,EAAE,EAAEC,IAAI,EAAE;IACnB,MAAM,IAAI1C,mBAAmB,EAAE,CAAA;AACjC,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACE2C,EAAAA,YAAYA,CAACF,EAAE,EAAEG,MAAM,EAAE;IACvB,MAAM,IAAI5C,mBAAmB,EAAE,CAAA;AACjC,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE6C,MAAMA,CAACJ,EAAE,EAAE;IACT,MAAM,IAAIzC,mBAAmB,EAAE,CAAA;AACjC,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE8C,MAAMA,CAACC,SAAS,EAAE;IAChB,MAAM,IAAI/C,mBAAmB,EAAE,CAAA;AACjC,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAIgD,OAAOA,GAAG;IACZ,MAAM,IAAIhD,mBAAmB,EAAE,CAAA;AACjC,GAAA;AACF;;AC7FA,IAAIiD,WAAS,GAAG,IAAI,CAAA;;AAEpB;AACA;AACA;AACA;AACe,MAAMC,UAAU,SAASf,IAAI,CAAC;AAC3C;AACF;AACA;AACA;EACE,WAAWgB,QAAQA,GAAG;IACpB,IAAIF,WAAS,KAAK,IAAI,EAAE;AACtBA,MAAAA,WAAS,GAAG,IAAIC,UAAU,EAAE,CAAA;AAC9B,KAAA;AACA,IAAA,OAAOD,WAAS,CAAA;AAClB,GAAA;;AAEA;EACA,IAAIb,IAAIA,GAAG;AACT,IAAA,OAAO,QAAQ,CAAA;AACjB,GAAA;;AAEA;EACA,IAAIC,IAAIA,GAAG;IACT,OAAO,IAAIe,IAAI,CAACC,cAAc,EAAE,CAACC,eAAe,EAAE,CAACC,QAAQ,CAAA;AAC7D,GAAA;;AAEA;EACA,IAAIhB,WAAWA,GAAG;AAChB,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;;AAEA;EACAC,UAAUA,CAACC,EAAE,EAAE;IAAEG,MAAM;AAAEY,IAAAA,MAAAA;AAAO,GAAC,EAAE;AACjC,IAAA,OAAOC,aAAa,CAAChB,EAAE,EAAEG,MAAM,EAAEY,MAAM,CAAC,CAAA;AAC1C,GAAA;;AAEA;AACAb,EAAAA,YAAYA,CAACF,EAAE,EAAEG,MAAM,EAAE;IACvB,OAAOD,YAAY,CAAC,IAAI,CAACE,MAAM,CAACJ,EAAE,CAAC,EAAEG,MAAM,CAAC,CAAA;AAC9C,GAAA;;AAEA;EACAC,MAAMA,CAACJ,EAAE,EAAE;IACT,OAAO,CAAC,IAAIiB,IAAI,CAACjB,EAAE,CAAC,CAACkB,iBAAiB,EAAE,CAAA;AAC1C,GAAA;;AAEA;EACAb,MAAMA,CAACC,SAAS,EAAE;AAChB,IAAA,OAAOA,SAAS,CAACX,IAAI,KAAK,QAAQ,CAAA;AACpC,GAAA;;AAEA;EACA,IAAIY,OAAOA,GAAG;AACZ,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AACF;;ACzDA,MAAMY,QAAQ,GAAG,IAAIC,GAAG,EAAE,CAAA;AAC1B,SAASC,OAAOA,CAACC,QAAQ,EAAE;AACzB,EAAA,IAAIC,GAAG,GAAGJ,QAAQ,CAACK,GAAG,CAACF,QAAQ,CAAC,CAAA;EAChC,IAAIC,GAAG,KAAKE,SAAS,EAAE;AACrBF,IAAAA,GAAG,GAAG,IAAIZ,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;AACrCc,MAAAA,MAAM,EAAE,KAAK;AACbZ,MAAAA,QAAQ,EAAEQ,QAAQ;AAClB1D,MAAAA,IAAI,EAAE,SAAS;AACfC,MAAAA,KAAK,EAAE,SAAS;AAChBC,MAAAA,GAAG,EAAE,SAAS;AACdO,MAAAA,IAAI,EAAE,SAAS;AACfC,MAAAA,MAAM,EAAE,SAAS;AACjBE,MAAAA,MAAM,EAAE,SAAS;AACjBmD,MAAAA,GAAG,EAAE,OAAA;AACP,KAAC,CAAC,CAAA;AACFR,IAAAA,QAAQ,CAACS,GAAG,CAACN,QAAQ,EAAEC,GAAG,CAAC,CAAA;AAC7B,GAAA;AACA,EAAA,OAAOA,GAAG,CAAA;AACZ,CAAA;AAEA,MAAMM,SAAS,GAAG;AAChBjE,EAAAA,IAAI,EAAE,CAAC;AACPC,EAAAA,KAAK,EAAE,CAAC;AACRC,EAAAA,GAAG,EAAE,CAAC;AACN6D,EAAAA,GAAG,EAAE,CAAC;AACNtD,EAAAA,IAAI,EAAE,CAAC;AACPC,EAAAA,MAAM,EAAE,CAAC;AACTE,EAAAA,MAAM,EAAE,CAAA;AACV,CAAC,CAAA;AAED,SAASsD,WAAWA,CAACP,GAAG,EAAEQ,IAAI,EAAE;AAC9B,EAAA,MAAMC,SAAS,GAAGT,GAAG,CAACpB,MAAM,CAAC4B,IAAI,CAAC,CAACE,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;AACvDC,IAAAA,MAAM,GAAG,iDAAiD,CAACC,IAAI,CAACH,SAAS,CAAC;AAC1E,IAAA,GAAGI,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,CAAC,GAAGR,MAAM,CAAA;AACpE,EAAA,OAAO,CAACI,KAAK,EAAEF,MAAM,EAAEC,IAAI,EAAEE,OAAO,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,CAAC,CAAA;AAChE,CAAA;AAEA,SAASC,WAAWA,CAACpB,GAAG,EAAEQ,IAAI,EAAE;AAC9B,EAAA,MAAMC,SAAS,GAAGT,GAAG,CAACqB,aAAa,CAACb,IAAI,CAAC,CAAA;EACzC,MAAMc,MAAM,GAAG,EAAE,CAAA;AACjB,EAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,SAAS,CAACe,MAAM,EAAED,CAAC,EAAE,EAAE;IACzC,MAAM;MAAEnD,IAAI;AAAEqD,MAAAA,KAAAA;AAAM,KAAC,GAAGhB,SAAS,CAACc,CAAC,CAAC,CAAA;AACpC,IAAA,MAAMG,GAAG,GAAGpB,SAAS,CAAClC,IAAI,CAAC,CAAA;IAE3B,IAAIA,IAAI,KAAK,KAAK,EAAE;AAClBkD,MAAAA,MAAM,CAACI,GAAG,CAAC,GAAGD,KAAK,CAAA;AACrB,KAAC,MAAM,IAAI,CAACE,WAAW,CAACD,GAAG,CAAC,EAAE;MAC5BJ,MAAM,CAACI,GAAG,CAAC,GAAGE,QAAQ,CAACH,KAAK,EAAE,EAAE,CAAC,CAAA;AACnC,KAAA;AACF,GAAA;AACA,EAAA,OAAOH,MAAM,CAAA;AACf,CAAA;AAEA,MAAMO,aAAa,GAAG,IAAIhC,GAAG,EAAE,CAAA;AAC/B;AACA;AACA;AACA;AACe,MAAMiC,QAAQ,SAAS3D,IAAI,CAAC;AACzC;AACF;AACA;AACA;EACE,OAAO4D,MAAMA,CAAC1D,IAAI,EAAE;AAClB,IAAA,IAAI2D,IAAI,GAAGH,aAAa,CAAC5B,GAAG,CAAC5B,IAAI,CAAC,CAAA;IAClC,IAAI2D,IAAI,KAAK9B,SAAS,EAAE;AACtB2B,MAAAA,aAAa,CAACxB,GAAG,CAAChC,IAAI,EAAG2D,IAAI,GAAG,IAAIF,QAAQ,CAACzD,IAAI,CAAE,CAAC,CAAA;AACtD,KAAA;AACA,IAAA,OAAO2D,IAAI,CAAA;AACb,GAAA;;AAEA;AACF;AACA;AACA;EACE,OAAOC,UAAUA,GAAG;IAClBJ,aAAa,CAACK,KAAK,EAAE,CAAA;IACrBtC,QAAQ,CAACsC,KAAK,EAAE,CAAA;AAClB,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,gBAAgBA,CAACjG,CAAC,EAAE;AACzB,IAAA,OAAO,IAAI,CAACkG,WAAW,CAAClG,CAAC,CAAC,CAAA;AAC5B,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOkG,WAAWA,CAACJ,IAAI,EAAE;IACvB,IAAI,CAACA,IAAI,EAAE;AACT,MAAA,OAAO,KAAK,CAAA;AACd,KAAA;IACA,IAAI;AACF,MAAA,IAAI5C,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;AAAEE,QAAAA,QAAQ,EAAEyC,IAAAA;AAAK,OAAC,CAAC,CAACpD,MAAM,EAAE,CAAA;AAC7D,MAAA,OAAO,IAAI,CAAA;KACZ,CAAC,OAAOyD,CAAC,EAAE;AACV,MAAA,OAAO,KAAK,CAAA;AACd,KAAA;AACF,GAAA;EAEA9G,WAAWA,CAAC8C,IAAI,EAAE;AAChB,IAAA,KAAK,EAAE,CAAA;AACP;IACA,IAAI,CAAC0B,QAAQ,GAAG1B,IAAI,CAAA;AACpB;IACA,IAAI,CAACiE,KAAK,GAAGR,QAAQ,CAACM,WAAW,CAAC/D,IAAI,CAAC,CAAA;AACzC,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAID,IAAIA,GAAG;AACT,IAAA,OAAO,MAAM,CAAA;AACf,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAIC,IAAIA,GAAG;IACT,OAAO,IAAI,CAAC0B,QAAQ,CAAA;AACtB,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIxB,WAAWA,GAAG;AAChB,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,UAAUA,CAACC,EAAE,EAAE;IAAEG,MAAM;AAAEY,IAAAA,MAAAA;AAAO,GAAC,EAAE;IACjC,OAAOC,aAAa,CAAChB,EAAE,EAAEG,MAAM,EAAEY,MAAM,EAAE,IAAI,CAACnB,IAAI,CAAC,CAAA;AACrD,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACEM,EAAAA,YAAYA,CAACF,EAAE,EAAEG,MAAM,EAAE;IACvB,OAAOD,YAAY,CAAC,IAAI,CAACE,MAAM,CAACJ,EAAE,CAAC,EAAEG,MAAM,CAAC,CAAA;AAC9C,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACEC,MAAMA,CAACJ,EAAE,EAAE;AACT,IAAA,IAAI,CAAC,IAAI,CAAC6D,KAAK,EAAE,OAAOC,GAAG,CAAA;AAC3B,IAAA,MAAM/B,IAAI,GAAG,IAAId,IAAI,CAACjB,EAAE,CAAC,CAAA;AAEzB,IAAA,IAAI+D,KAAK,CAAChC,IAAI,CAAC,EAAE,OAAO+B,GAAG,CAAA;AAE3B,IAAA,MAAMvC,GAAG,GAAGF,OAAO,CAAC,IAAI,CAACzB,IAAI,CAAC,CAAA;AAC9B,IAAA,IAAI,CAAChC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEkG,MAAM,EAAE3F,IAAI,EAAEC,MAAM,EAAEE,MAAM,CAAC,GAAG+C,GAAG,CAACqB,aAAa,GACpED,WAAW,CAACpB,GAAG,EAAEQ,IAAI,CAAC,GACtBD,WAAW,CAACP,GAAG,EAAEQ,IAAI,CAAC,CAAA;IAE1B,IAAIiC,MAAM,KAAK,IAAI,EAAE;MACnBpG,IAAI,GAAG,CAACqG,IAAI,CAACC,GAAG,CAACtG,IAAI,CAAC,GAAG,CAAC,CAAA;AAC5B,KAAA;;AAEA;IACA,MAAMuG,YAAY,GAAG9F,IAAI,KAAK,EAAE,GAAG,CAAC,GAAGA,IAAI,CAAA;IAE3C,MAAM+F,KAAK,GAAGC,YAAY,CAAC;MACzBzG,IAAI;MACJC,KAAK;MACLC,GAAG;AACHO,MAAAA,IAAI,EAAE8F,YAAY;MAClB7F,MAAM;MACNE,MAAM;AACN8F,MAAAA,WAAW,EAAE,CAAA;AACf,KAAC,CAAC,CAAA;IAEF,IAAIC,IAAI,GAAG,CAACxC,IAAI,CAAA;AAChB,IAAA,MAAMyC,IAAI,GAAGD,IAAI,GAAG,IAAI,CAAA;IACxBA,IAAI,IAAIC,IAAI,IAAI,CAAC,GAAGA,IAAI,GAAG,IAAI,GAAGA,IAAI,CAAA;IACtC,OAAO,CAACJ,KAAK,GAAGG,IAAI,KAAK,EAAE,GAAG,IAAI,CAAC,CAAA;AACrC,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACElE,MAAMA,CAACC,SAAS,EAAE;AAChB,IAAA,OAAOA,SAAS,CAACX,IAAI,KAAK,MAAM,IAAIW,SAAS,CAACV,IAAI,KAAK,IAAI,CAACA,IAAI,CAAA;AAClE,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAIW,OAAOA,GAAG;IACZ,OAAO,IAAI,CAACsD,KAAK,CAAA;AACnB,GAAA;AACF;;ACpOA;;AAEA,IAAIY,WAAW,GAAG,EAAE,CAAA;AACpB,SAASC,WAAWA,CAACC,SAAS,EAAE1E,IAAI,GAAG,EAAE,EAAE;EACzC,MAAM2E,GAAG,GAAGC,IAAI,CAACC,SAAS,CAAC,CAACH,SAAS,EAAE1E,IAAI,CAAC,CAAC,CAAA;AAC7C,EAAA,IAAIsB,GAAG,GAAGkD,WAAW,CAACG,GAAG,CAAC,CAAA;EAC1B,IAAI,CAACrD,GAAG,EAAE;IACRA,GAAG,GAAG,IAAIZ,IAAI,CAACoE,UAAU,CAACJ,SAAS,EAAE1E,IAAI,CAAC,CAAA;AAC1CwE,IAAAA,WAAW,CAACG,GAAG,CAAC,GAAGrD,GAAG,CAAA;AACxB,GAAA;AACA,EAAA,OAAOA,GAAG,CAAA;AACZ,CAAA;AAEA,MAAMyD,WAAW,GAAG,IAAI5D,GAAG,EAAE,CAAA;AAC7B,SAAS6D,YAAYA,CAACN,SAAS,EAAE1E,IAAI,GAAG,EAAE,EAAE;EAC1C,MAAM2E,GAAG,GAAGC,IAAI,CAACC,SAAS,CAAC,CAACH,SAAS,EAAE1E,IAAI,CAAC,CAAC,CAAA;AAC7C,EAAA,IAAIsB,GAAG,GAAGyD,WAAW,CAACxD,GAAG,CAACoD,GAAG,CAAC,CAAA;EAC9B,IAAIrD,GAAG,KAAKE,SAAS,EAAE;IACrBF,GAAG,GAAG,IAAIZ,IAAI,CAACC,cAAc,CAAC+D,SAAS,EAAE1E,IAAI,CAAC,CAAA;AAC9C+E,IAAAA,WAAW,CAACpD,GAAG,CAACgD,GAAG,EAAErD,GAAG,CAAC,CAAA;AAC3B,GAAA;AACA,EAAA,OAAOA,GAAG,CAAA;AACZ,CAAA;AAEA,MAAM2D,YAAY,GAAG,IAAI9D,GAAG,EAAE,CAAA;AAC9B,SAAS+D,YAAYA,CAACR,SAAS,EAAE1E,IAAI,GAAG,EAAE,EAAE;EAC1C,MAAM2E,GAAG,GAAGC,IAAI,CAACC,SAAS,CAAC,CAACH,SAAS,EAAE1E,IAAI,CAAC,CAAC,CAAA;AAC7C,EAAA,IAAImF,GAAG,GAAGF,YAAY,CAAC1D,GAAG,CAACoD,GAAG,CAAC,CAAA;EAC/B,IAAIQ,GAAG,KAAK3D,SAAS,EAAE;IACrB2D,GAAG,GAAG,IAAIzE,IAAI,CAAC0E,YAAY,CAACV,SAAS,EAAE1E,IAAI,CAAC,CAAA;AAC5CiF,IAAAA,YAAY,CAACtD,GAAG,CAACgD,GAAG,EAAEQ,GAAG,CAAC,CAAA;AAC5B,GAAA;AACA,EAAA,OAAOA,GAAG,CAAA;AACZ,CAAA;AAEA,MAAME,YAAY,GAAG,IAAIlE,GAAG,EAAE,CAAA;AAC9B,SAASmE,YAAYA,CAACZ,SAAS,EAAE1E,IAAI,GAAG,EAAE,EAAE;EAC1C,MAAM;IAAEuF,IAAI;IAAE,GAAGC,YAAAA;GAAc,GAAGxF,IAAI,CAAC;EACvC,MAAM2E,GAAG,GAAGC,IAAI,CAACC,SAAS,CAAC,CAACH,SAAS,EAAEc,YAAY,CAAC,CAAC,CAAA;AACrD,EAAA,IAAIL,GAAG,GAAGE,YAAY,CAAC9D,GAAG,CAACoD,GAAG,CAAC,CAAA;EAC/B,IAAIQ,GAAG,KAAK3D,SAAS,EAAE;IACrB2D,GAAG,GAAG,IAAIzE,IAAI,CAAC+E,kBAAkB,CAACf,SAAS,EAAE1E,IAAI,CAAC,CAAA;AAClDqF,IAAAA,YAAY,CAAC1D,GAAG,CAACgD,GAAG,EAAEQ,GAAG,CAAC,CAAA;AAC5B,GAAA;AACA,EAAA,OAAOA,GAAG,CAAA;AACZ,CAAA;AAEA,IAAIO,cAAc,GAAG,IAAI,CAAA;AACzB,SAASC,YAAYA,GAAG;AACtB,EAAA,IAAID,cAAc,EAAE;AAClB,IAAA,OAAOA,cAAc,CAAA;AACvB,GAAC,MAAM;AACLA,IAAAA,cAAc,GAAG,IAAIhF,IAAI,CAACC,cAAc,EAAE,CAACC,eAAe,EAAE,CAACE,MAAM,CAAA;AACnE,IAAA,OAAO4E,cAAc,CAAA;AACvB,GAAA;AACF,CAAA;AAEA,MAAME,wBAAwB,GAAG,IAAIzE,GAAG,EAAE,CAAA;AAC1C,SAAS0E,2BAA2BA,CAACnB,SAAS,EAAE;AAC9C,EAAA,IAAI1E,IAAI,GAAG4F,wBAAwB,CAACrE,GAAG,CAACmD,SAAS,CAAC,CAAA;EAClD,IAAI1E,IAAI,KAAKwB,SAAS,EAAE;IACtBxB,IAAI,GAAG,IAAIU,IAAI,CAACC,cAAc,CAAC+D,SAAS,CAAC,CAAC9D,eAAe,EAAE,CAAA;AAC3DgF,IAAAA,wBAAwB,CAACjE,GAAG,CAAC+C,SAAS,EAAE1E,IAAI,CAAC,CAAA;AAC/C,GAAA;AACA,EAAA,OAAOA,IAAI,CAAA;AACb,CAAA;AAEA,MAAM8F,aAAa,GAAG,IAAI3E,GAAG,EAAE,CAAA;AAC/B,SAAS4E,iBAAiBA,CAACrB,SAAS,EAAE;AACpC,EAAA,IAAIsB,IAAI,GAAGF,aAAa,CAACvE,GAAG,CAACmD,SAAS,CAAC,CAAA;EACvC,IAAI,CAACsB,IAAI,EAAE;IACT,MAAMlF,MAAM,GAAG,IAAIJ,IAAI,CAACuF,MAAM,CAACvB,SAAS,CAAC,CAAA;AACzC;AACAsB,IAAAA,IAAI,GAAG,aAAa,IAAIlF,MAAM,GAAGA,MAAM,CAACoF,WAAW,EAAE,GAAGpF,MAAM,CAACqF,QAAQ,CAAA;AACvE;AACA,IAAA,IAAI,EAAE,aAAa,IAAIH,IAAI,CAAC,EAAE;AAC5BA,MAAAA,IAAI,GAAG;AAAE,QAAA,GAAGI,oBAAoB;QAAE,GAAGJ,IAAAA;OAAM,CAAA;AAC7C,KAAA;AACAF,IAAAA,aAAa,CAACnE,GAAG,CAAC+C,SAAS,EAAEsB,IAAI,CAAC,CAAA;AACpC,GAAA;AACA,EAAA,OAAOA,IAAI,CAAA;AACb,CAAA;AAEA,SAASK,iBAAiBA,CAACC,SAAS,EAAE;AACpC;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,EAAA,MAAMC,MAAM,GAAGD,SAAS,CAACE,OAAO,CAAC,KAAK,CAAC,CAAA;AACvC,EAAA,IAAID,MAAM,KAAK,CAAC,CAAC,EAAE;IACjBD,SAAS,GAAGA,SAAS,CAACG,SAAS,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAA;AAC5C,GAAA;AAEA,EAAA,MAAMG,MAAM,GAAGJ,SAAS,CAACE,OAAO,CAAC,KAAK,CAAC,CAAA;AACvC,EAAA,IAAIE,MAAM,KAAK,CAAC,CAAC,EAAE;IACjB,OAAO,CAACJ,SAAS,CAAC,CAAA;AACpB,GAAC,MAAM;AACL,IAAA,IAAIK,OAAO,CAAA;AACX,IAAA,IAAIC,WAAW,CAAA;IACf,IAAI;MACFD,OAAO,GAAG3B,YAAY,CAACsB,SAAS,CAAC,CAAC1F,eAAe,EAAE,CAAA;AACnDgG,MAAAA,WAAW,GAAGN,SAAS,CAAA;KACxB,CAAC,OAAO3C,CAAC,EAAE;MACV,MAAMkD,OAAO,GAAGP,SAAS,CAACG,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAA;MAC9CC,OAAO,GAAG3B,YAAY,CAAC6B,OAAO,CAAC,CAACjG,eAAe,EAAE,CAAA;AACjDgG,MAAAA,WAAW,GAAGC,OAAO,CAAA;AACvB,KAAA;IAEA,MAAM;MAAEC,eAAe;AAAEC,MAAAA,QAAAA;AAAS,KAAC,GAAGJ,OAAO,CAAA;AAC7C,IAAA,OAAO,CAACC,WAAW,EAAEE,eAAe,EAAEC,QAAQ,CAAC,CAAA;AACjD,GAAA;AACF,CAAA;AAEA,SAASC,gBAAgBA,CAACV,SAAS,EAAEQ,eAAe,EAAEG,cAAc,EAAE;EACpE,IAAIA,cAAc,IAAIH,eAAe,EAAE;AACrC,IAAA,IAAI,CAACR,SAAS,CAACY,QAAQ,CAAC,KAAK,CAAC,EAAE;AAC9BZ,MAAAA,SAAS,IAAI,IAAI,CAAA;AACnB,KAAA;AAEA,IAAA,IAAIW,cAAc,EAAE;MAClBX,SAAS,IAAK,CAAMW,IAAAA,EAAAA,cAAe,CAAC,CAAA,CAAA;AACtC,KAAA;AAEA,IAAA,IAAIH,eAAe,EAAE;MACnBR,SAAS,IAAK,CAAMQ,IAAAA,EAAAA,eAAgB,CAAC,CAAA,CAAA;AACvC,KAAA;AACA,IAAA,OAAOR,SAAS,CAAA;AAClB,GAAC,MAAM;AACL,IAAA,OAAOA,SAAS,CAAA;AAClB,GAAA;AACF,CAAA;AAEA,SAASa,SAASA,CAACC,CAAC,EAAE;EACpB,MAAMC,EAAE,GAAG,EAAE,CAAA;EACb,KAAK,IAAIxE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,EAAE,EAAEA,CAAC,EAAE,EAAE;IAC5B,MAAMyE,EAAE,GAAGC,QAAQ,CAACC,GAAG,CAAC,IAAI,EAAE3E,CAAC,EAAE,CAAC,CAAC,CAAA;AACnCwE,IAAAA,EAAE,CAACI,IAAI,CAACL,CAAC,CAACE,EAAE,CAAC,CAAC,CAAA;AAChB,GAAA;AACA,EAAA,OAAOD,EAAE,CAAA;AACX,CAAA;AAEA,SAASK,WAAWA,CAACN,CAAC,EAAE;EACtB,MAAMC,EAAE,GAAG,EAAE,CAAA;EACb,KAAK,IAAIxE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;AAC3B,IAAA,MAAMyE,EAAE,GAAGC,QAAQ,CAACC,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,GAAG3E,CAAC,CAAC,CAAA;AACzCwE,IAAAA,EAAE,CAACI,IAAI,CAACL,CAAC,CAACE,EAAE,CAAC,CAAC,CAAA;AAChB,GAAA;AACA,EAAA,OAAOD,EAAE,CAAA;AACX,CAAA;AAEA,SAASM,SAASA,CAACC,GAAG,EAAE9E,MAAM,EAAE+E,SAAS,EAAEC,MAAM,EAAE;AACjD,EAAA,MAAMC,IAAI,GAAGH,GAAG,CAACI,WAAW,EAAE,CAAA;EAE9B,IAAID,IAAI,KAAK,OAAO,EAAE;AACpB,IAAA,OAAO,IAAI,CAAA;AACb,GAAC,MAAM,IAAIA,IAAI,KAAK,IAAI,EAAE;IACxB,OAAOF,SAAS,CAAC/E,MAAM,CAAC,CAAA;AAC1B,GAAC,MAAM;IACL,OAAOgF,MAAM,CAAChF,MAAM,CAAC,CAAA;AACvB,GAAA;AACF,CAAA;AAEA,SAASmF,mBAAmBA,CAACL,GAAG,EAAE;EAChC,IAAIA,GAAG,CAACd,eAAe,IAAIc,GAAG,CAACd,eAAe,KAAK,MAAM,EAAE;AACzD,IAAA,OAAO,KAAK,CAAA;AACd,GAAC,MAAM;AACL,IAAA,OACEc,GAAG,CAACd,eAAe,KAAK,MAAM,IAC9B,CAACc,GAAG,CAAC9G,MAAM,IACX8G,GAAG,CAAC9G,MAAM,CAACoH,UAAU,CAAC,IAAI,CAAC,IAC3BrC,2BAA2B,CAAC+B,GAAG,CAAC9G,MAAM,CAAC,CAACgG,eAAe,KAAK,MAAM,CAAA;AAEtE,GAAA;AACF,CAAA;;AAEA;AACA;AACA;;AAEA,MAAMqB,mBAAmB,CAAC;AACxBtL,EAAAA,WAAWA,CAACuL,IAAI,EAAEC,WAAW,EAAErI,IAAI,EAAE;AACnC,IAAA,IAAI,CAACsI,KAAK,GAAGtI,IAAI,CAACsI,KAAK,IAAI,CAAC,CAAA;AAC5B,IAAA,IAAI,CAACC,KAAK,GAAGvI,IAAI,CAACuI,KAAK,IAAI,KAAK,CAAA;IAEhC,MAAM;MAAED,KAAK;MAAEC,KAAK;MAAE,GAAGC,SAAAA;AAAU,KAAC,GAAGxI,IAAI,CAAA;AAE3C,IAAA,IAAI,CAACqI,WAAW,IAAII,MAAM,CAACC,IAAI,CAACF,SAAS,CAAC,CAAC1F,MAAM,GAAG,CAAC,EAAE;AACrD,MAAA,MAAM6F,QAAQ,GAAG;AAAEC,QAAAA,WAAW,EAAE,KAAK;QAAE,GAAG5I,IAAAA;OAAM,CAAA;AAChD,MAAA,IAAIA,IAAI,CAACsI,KAAK,GAAG,CAAC,EAAEK,QAAQ,CAACE,oBAAoB,GAAG7I,IAAI,CAACsI,KAAK,CAAA;MAC9D,IAAI,CAACnD,GAAG,GAAGD,YAAY,CAACkD,IAAI,EAAEO,QAAQ,CAAC,CAAA;AACzC,KAAA;AACF,GAAA;EAEAzI,MAAMA,CAAC2C,CAAC,EAAE;IACR,IAAI,IAAI,CAACsC,GAAG,EAAE;AACZ,MAAA,MAAM2D,KAAK,GAAG,IAAI,CAACP,KAAK,GAAGvE,IAAI,CAACuE,KAAK,CAAC1F,CAAC,CAAC,GAAGA,CAAC,CAAA;AAC5C,MAAA,OAAO,IAAI,CAACsC,GAAG,CAACjF,MAAM,CAAC4I,KAAK,CAAC,CAAA;AAC/B,KAAC,MAAM;AACL;AACA,MAAA,MAAMA,KAAK,GAAG,IAAI,CAACP,KAAK,GAAGvE,IAAI,CAACuE,KAAK,CAAC1F,CAAC,CAAC,GAAGkG,OAAO,CAAClG,CAAC,EAAE,CAAC,CAAC,CAAA;AACxD,MAAA,OAAOmG,QAAQ,CAACF,KAAK,EAAE,IAAI,CAACR,KAAK,CAAC,CAAA;AACpC,KAAA;AACF,GAAA;AACF,CAAA;;AAEA;AACA;AACA;;AAEA,MAAMW,iBAAiB,CAAC;AACtBpM,EAAAA,WAAWA,CAACyK,EAAE,EAAEc,IAAI,EAAEpI,IAAI,EAAE;IAC1B,IAAI,CAACA,IAAI,GAAGA,IAAI,CAAA;IAChB,IAAI,CAACkJ,YAAY,GAAG1H,SAAS,CAAA;IAE7B,IAAI2H,CAAC,GAAG3H,SAAS,CAAA;AACjB,IAAA,IAAI,IAAI,CAACxB,IAAI,CAACa,QAAQ,EAAE;AACtB;MACA,IAAI,CAACyG,EAAE,GAAGA,EAAE,CAAA;KACb,MAAM,IAAIA,EAAE,CAAChE,IAAI,CAAC5D,IAAI,KAAK,OAAO,EAAE;AACnC;AACA;AACA;AACA;AACA;AACA;MACA,MAAM0J,SAAS,GAAG,CAAC,CAAC,IAAI9B,EAAE,CAACnH,MAAM,GAAG,EAAE,CAAC,CAAA;AACvC,MAAA,MAAMkJ,OAAO,GAAGD,SAAS,IAAI,CAAC,GAAI,CAAUA,QAAAA,EAAAA,SAAU,CAAC,CAAA,GAAI,CAASA,OAAAA,EAAAA,SAAU,CAAC,CAAA,CAAA;AAC/E,MAAA,IAAI9B,EAAE,CAACnH,MAAM,KAAK,CAAC,IAAIiD,QAAQ,CAACC,MAAM,CAACgG,OAAO,CAAC,CAACzF,KAAK,EAAE;AACrDuF,QAAAA,CAAC,GAAGE,OAAO,CAAA;QACX,IAAI,CAAC/B,EAAE,GAAGA,EAAE,CAAA;AACd,OAAC,MAAM;AACL;AACA;AACA6B,QAAAA,CAAC,GAAG,KAAK,CAAA;AACT,QAAA,IAAI,CAAC7B,EAAE,GAAGA,EAAE,CAACnH,MAAM,KAAK,CAAC,GAAGmH,EAAE,GAAGA,EAAE,CAACgC,OAAO,CAAC,KAAK,CAAC,CAACC,IAAI,CAAC;UAAEC,OAAO,EAAElC,EAAE,CAACnH,MAAAA;AAAO,SAAC,CAAC,CAAA;AAC/E,QAAA,IAAI,CAAC+I,YAAY,GAAG5B,EAAE,CAAChE,IAAI,CAAA;AAC7B,OAAA;KACD,MAAM,IAAIgE,EAAE,CAAChE,IAAI,CAAC5D,IAAI,KAAK,QAAQ,EAAE;MACpC,IAAI,CAAC4H,EAAE,GAAGA,EAAE,CAAA;KACb,MAAM,IAAIA,EAAE,CAAChE,IAAI,CAAC5D,IAAI,KAAK,MAAM,EAAE;MAClC,IAAI,CAAC4H,EAAE,GAAGA,EAAE,CAAA;AACZ6B,MAAAA,CAAC,GAAG7B,EAAE,CAAChE,IAAI,CAAC3D,IAAI,CAAA;AAClB,KAAC,MAAM;AACL;AACA;AACAwJ,MAAAA,CAAC,GAAG,KAAK,CAAA;MACT,IAAI,CAAC7B,EAAE,GAAGA,EAAE,CAACgC,OAAO,CAAC,KAAK,CAAC,CAACC,IAAI,CAAC;QAAEC,OAAO,EAAElC,EAAE,CAACnH,MAAAA;AAAO,OAAC,CAAC,CAAA;AACxD,MAAA,IAAI,CAAC+I,YAAY,GAAG5B,EAAE,CAAChE,IAAI,CAAA;AAC7B,KAAA;AAEA,IAAA,MAAMqF,QAAQ,GAAG;AAAE,MAAA,GAAG,IAAI,CAAC3I,IAAAA;KAAM,CAAA;AACjC2I,IAAAA,QAAQ,CAAC9H,QAAQ,GAAG8H,QAAQ,CAAC9H,QAAQ,IAAIsI,CAAC,CAAA;IAC1C,IAAI,CAAC7H,GAAG,GAAG0D,YAAY,CAACoD,IAAI,EAAEO,QAAQ,CAAC,CAAA;AACzC,GAAA;AAEAzI,EAAAA,MAAMA,GAAG;IACP,IAAI,IAAI,CAACgJ,YAAY,EAAE;AACrB;AACA;MACA,OAAO,IAAI,CAACvG,aAAa,EAAE,CACxB8G,GAAG,CAAC,CAAC;AAAE1G,QAAAA,KAAAA;AAAM,OAAC,KAAKA,KAAK,CAAC,CACzB2G,IAAI,CAAC,EAAE,CAAC,CAAA;AACb,KAAA;AACA,IAAA,OAAO,IAAI,CAACpI,GAAG,CAACpB,MAAM,CAAC,IAAI,CAACoH,EAAE,CAACqC,QAAQ,EAAE,CAAC,CAAA;AAC5C,GAAA;AAEAhH,EAAAA,aAAaA,GAAG;AACd,IAAA,MAAMiH,KAAK,GAAG,IAAI,CAACtI,GAAG,CAACqB,aAAa,CAAC,IAAI,CAAC2E,EAAE,CAACqC,QAAQ,EAAE,CAAC,CAAA;IACxD,IAAI,IAAI,CAACT,YAAY,EAAE;AACrB,MAAA,OAAOU,KAAK,CAACH,GAAG,CAAEI,IAAI,IAAK;AACzB,QAAA,IAAIA,IAAI,CAACnK,IAAI,KAAK,cAAc,EAAE;AAChC,UAAA,MAAMI,UAAU,GAAG,IAAI,CAACoJ,YAAY,CAACpJ,UAAU,CAAC,IAAI,CAACwH,EAAE,CAACvH,EAAE,EAAE;AAC1De,YAAAA,MAAM,EAAE,IAAI,CAACwG,EAAE,CAACxG,MAAM;AACtBZ,YAAAA,MAAM,EAAE,IAAI,CAACF,IAAI,CAACvB,YAAAA;AACpB,WAAC,CAAC,CAAA;UACF,OAAO;AACL,YAAA,GAAGoL,IAAI;AACP9G,YAAAA,KAAK,EAAEjD,UAAAA;WACR,CAAA;AACH,SAAC,MAAM;AACL,UAAA,OAAO+J,IAAI,CAAA;AACb,SAAA;AACF,OAAC,CAAC,CAAA;AACJ,KAAA;AACA,IAAA,OAAOD,KAAK,CAAA;AACd,GAAA;AAEAhJ,EAAAA,eAAeA,GAAG;AAChB,IAAA,OAAO,IAAI,CAACU,GAAG,CAACV,eAAe,EAAE,CAAA;AACnC,GAAA;AACF,CAAA;;AAEA;AACA;AACA;AACA,MAAMkJ,gBAAgB,CAAC;AACrBjN,EAAAA,WAAWA,CAACuL,IAAI,EAAE2B,SAAS,EAAE/J,IAAI,EAAE;IACjC,IAAI,CAACA,IAAI,GAAG;AAAEgK,MAAAA,KAAK,EAAE,MAAM;MAAE,GAAGhK,IAAAA;KAAM,CAAA;AACtC,IAAA,IAAI,CAAC+J,SAAS,IAAIE,WAAW,EAAE,EAAE;MAC/B,IAAI,CAACC,GAAG,GAAG5E,YAAY,CAAC8C,IAAI,EAAEpI,IAAI,CAAC,CAAA;AACrC,KAAA;AACF,GAAA;AAEAE,EAAAA,MAAMA,CAACiK,KAAK,EAAE/M,IAAI,EAAE;IAClB,IAAI,IAAI,CAAC8M,GAAG,EAAE;MACZ,OAAO,IAAI,CAACA,GAAG,CAAChK,MAAM,CAACiK,KAAK,EAAE/M,IAAI,CAAC,CAAA;AACrC,KAAC,MAAM;MACL,OAAOgN,kBAA0B,CAAChN,IAAI,EAAE+M,KAAK,EAAE,IAAI,CAACnK,IAAI,CAACqK,OAAO,EAAE,IAAI,CAACrK,IAAI,CAACgK,KAAK,KAAK,MAAM,CAAC,CAAA;AAC/F,KAAA;AACF,GAAA;AAEArH,EAAAA,aAAaA,CAACwH,KAAK,EAAE/M,IAAI,EAAE;IACzB,IAAI,IAAI,CAAC8M,GAAG,EAAE;MACZ,OAAO,IAAI,CAACA,GAAG,CAACvH,aAAa,CAACwH,KAAK,EAAE/M,IAAI,CAAC,CAAA;AAC5C,KAAC,MAAM;AACL,MAAA,OAAO,EAAE,CAAA;AACX,KAAA;AACF,GAAA;AACF,CAAA;AAEA,MAAMgJ,oBAAoB,GAAG;AAC3BkE,EAAAA,QAAQ,EAAE,CAAC;AACXC,EAAAA,WAAW,EAAE,CAAC;AACdC,EAAAA,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAA;AAChB,CAAC,CAAA;;AAED;AACA;AACA;AACe,MAAMvE,MAAM,CAAC;EAC1B,OAAOwE,QAAQA,CAACzK,IAAI,EAAE;IACpB,OAAOiG,MAAM,CAAC5C,MAAM,CAClBrD,IAAI,CAACc,MAAM,EACXd,IAAI,CAAC8G,eAAe,EACpB9G,IAAI,CAACiH,cAAc,EACnBjH,IAAI,CAAC0K,YAAY,EACjB1K,IAAI,CAAC2K,WACP,CAAC,CAAA;AACH,GAAA;AAEA,EAAA,OAAOtH,MAAMA,CAACvC,MAAM,EAAEgG,eAAe,EAAEG,cAAc,EAAEyD,YAAY,EAAEC,WAAW,GAAG,KAAK,EAAE;AACxF,IAAA,MAAMC,eAAe,GAAG9J,MAAM,IAAI+J,QAAQ,CAACC,aAAa,CAAA;AACxD;IACA,MAAMC,OAAO,GAAGH,eAAe,KAAKD,WAAW,GAAG,OAAO,GAAGhF,YAAY,EAAE,CAAC,CAAA;AAC3E,IAAA,MAAMqF,gBAAgB,GAAGlE,eAAe,IAAI+D,QAAQ,CAACI,sBAAsB,CAAA;AAC3E,IAAA,MAAMC,eAAe,GAAGjE,cAAc,IAAI4D,QAAQ,CAACM,qBAAqB,CAAA;IACxE,MAAMC,aAAa,GAAGC,oBAAoB,CAACX,YAAY,CAAC,IAAIG,QAAQ,CAACS,mBAAmB,CAAA;AACxF,IAAA,OAAO,IAAIrF,MAAM,CAAC8E,OAAO,EAAEC,gBAAgB,EAAEE,eAAe,EAAEE,aAAa,EAAER,eAAe,CAAC,CAAA;AAC/F,GAAA;EAEA,OAAOrH,UAAUA,GAAG;AAClBmC,IAAAA,cAAc,GAAG,IAAI,CAAA;IACrBX,WAAW,CAACvB,KAAK,EAAE,CAAA;IACnByB,YAAY,CAACzB,KAAK,EAAE,CAAA;IACpB6B,YAAY,CAAC7B,KAAK,EAAE,CAAA;IACpBoC,wBAAwB,CAACpC,KAAK,EAAE,CAAA;IAChCsC,aAAa,CAACtC,KAAK,EAAE,CAAA;AACvB,GAAA;AAEA,EAAA,OAAO+H,UAAUA,CAAC;IAAEzK,MAAM;IAAEgG,eAAe;IAAEG,cAAc;AAAEyD,IAAAA,YAAAA;GAAc,GAAG,EAAE,EAAE;IAChF,OAAOzE,MAAM,CAAC5C,MAAM,CAACvC,MAAM,EAAEgG,eAAe,EAAEG,cAAc,EAAEyD,YAAY,CAAC,CAAA;AAC7E,GAAA;EAEA7N,WAAWA,CAACiE,MAAM,EAAE0K,SAAS,EAAEvE,cAAc,EAAEyD,YAAY,EAAEE,eAAe,EAAE;IAC5E,MAAM,CAACa,YAAY,EAAEC,qBAAqB,EAAEC,oBAAoB,CAAC,GAAGtF,iBAAiB,CAACvF,MAAM,CAAC,CAAA;IAE7F,IAAI,CAACA,MAAM,GAAG2K,YAAY,CAAA;AAC1B,IAAA,IAAI,CAAC3E,eAAe,GAAG0E,SAAS,IAAIE,qBAAqB,IAAI,IAAI,CAAA;AACjE,IAAA,IAAI,CAACzE,cAAc,GAAGA,cAAc,IAAI0E,oBAAoB,IAAI,IAAI,CAAA;IACpE,IAAI,CAACjB,YAAY,GAAGA,YAAY,CAAA;AAChC,IAAA,IAAI,CAACtC,IAAI,GAAGpB,gBAAgB,CAAC,IAAI,CAAClG,MAAM,EAAE,IAAI,CAACgG,eAAe,EAAE,IAAI,CAACG,cAAc,CAAC,CAAA;IAEpF,IAAI,CAAC2E,aAAa,GAAG;MAAE1L,MAAM,EAAE,EAAE;AAAE2L,MAAAA,UAAU,EAAE,EAAC;KAAG,CAAA;IACnD,IAAI,CAACC,WAAW,GAAG;MAAE5L,MAAM,EAAE,EAAE;AAAE2L,MAAAA,UAAU,EAAE,EAAC;KAAG,CAAA;IACjD,IAAI,CAACE,aAAa,GAAG,IAAI,CAAA;AACzB,IAAA,IAAI,CAACC,QAAQ,GAAG,EAAE,CAAA;IAElB,IAAI,CAACpB,eAAe,GAAGA,eAAe,CAAA;IACtC,IAAI,CAACqB,iBAAiB,GAAG,IAAI,CAAA;AAC/B,GAAA;EAEA,IAAIC,WAAWA,GAAG;AAChB,IAAA,IAAI,IAAI,CAACD,iBAAiB,IAAI,IAAI,EAAE;AAClC,MAAA,IAAI,CAACA,iBAAiB,GAAGhE,mBAAmB,CAAC,IAAI,CAAC,CAAA;AACpD,KAAA;IAEA,OAAO,IAAI,CAACgE,iBAAiB,CAAA;AAC/B,GAAA;AAEAjE,EAAAA,WAAWA,GAAG;AACZ,IAAA,MAAMmE,YAAY,GAAG,IAAI,CAACpC,SAAS,EAAE,CAAA;IACrC,MAAMqC,cAAc,GAClB,CAAC,IAAI,CAACtF,eAAe,KAAK,IAAI,IAAI,IAAI,CAACA,eAAe,KAAK,MAAM,MAChE,IAAI,CAACG,cAAc,KAAK,IAAI,IAAI,IAAI,CAACA,cAAc,KAAK,SAAS,CAAC,CAAA;AACrE,IAAA,OAAOkF,YAAY,IAAIC,cAAc,GAAG,IAAI,GAAG,MAAM,CAAA;AACvD,GAAA;EAEAC,KAAKA,CAACC,IAAI,EAAE;AACV,IAAA,IAAI,CAACA,IAAI,IAAI7D,MAAM,CAAC8D,mBAAmB,CAACD,IAAI,CAAC,CAACxJ,MAAM,KAAK,CAAC,EAAE;AAC1D,MAAA,OAAO,IAAI,CAAA;AACb,KAAC,MAAM;MACL,OAAOmD,MAAM,CAAC5C,MAAM,CAClBiJ,IAAI,CAACxL,MAAM,IAAI,IAAI,CAAC8J,eAAe,EACnC0B,IAAI,CAACxF,eAAe,IAAI,IAAI,CAACA,eAAe,EAC5CwF,IAAI,CAACrF,cAAc,IAAI,IAAI,CAACA,cAAc,EAC1CoE,oBAAoB,CAACiB,IAAI,CAAC5B,YAAY,CAAC,IAAI,IAAI,CAACA,YAAY,EAC5D4B,IAAI,CAAC3B,WAAW,IAAI,KACtB,CAAC,CAAA;AACH,KAAA;AACF,GAAA;AAEA6B,EAAAA,aAAaA,CAACF,IAAI,GAAG,EAAE,EAAE;IACvB,OAAO,IAAI,CAACD,KAAK,CAAC;AAAE,MAAA,GAAGC,IAAI;AAAE3B,MAAAA,WAAW,EAAE,IAAA;AAAK,KAAC,CAAC,CAAA;AACnD,GAAA;AAEA8B,EAAAA,iBAAiBA,CAACH,IAAI,GAAG,EAAE,EAAE;IAC3B,OAAO,IAAI,CAACD,KAAK,CAAC;AAAE,MAAA,GAAGC,IAAI;AAAE3B,MAAAA,WAAW,EAAE,KAAA;AAAM,KAAC,CAAC,CAAA;AACpD,GAAA;AAEA+B,EAAAA,MAAMA,CAAC5J,MAAM,EAAE5C,MAAM,GAAG,KAAK,EAAE;IAC7B,OAAOyH,SAAS,CAAC,IAAI,EAAE7E,MAAM,EAAEsH,MAAc,EAAE,MAAM;AACnD;AACA;AACA;AACA,MAAA,MAAMuC,gBAAgB,GAAG,IAAI,CAACvE,IAAI,KAAK,IAAI,IAAI,IAAI,CAACA,IAAI,CAACF,UAAU,CAAC,KAAK,CAAC,CAAA;MAC1EhI,MAAM,IAAI,CAACyM,gBAAgB,CAAA;MAC3B,MAAMvE,IAAI,GAAGlI,MAAM,GAAG;AAAEtC,UAAAA,KAAK,EAAEkF,MAAM;AAAEjF,UAAAA,GAAG,EAAE,SAAA;AAAU,SAAC,GAAG;AAAED,UAAAA,KAAK,EAAEkF,MAAAA;SAAQ;AACzE8J,QAAAA,SAAS,GAAG1M,MAAM,GAAG,QAAQ,GAAG,YAAY,CAAA;MAC9C,IAAI,CAAC,IAAI,CAAC4L,WAAW,CAACc,SAAS,CAAC,CAAC9J,MAAM,CAAC,EAAE;AACxC,QAAA,MAAM+J,MAAM,GAAG,CAACF,gBAAgB,GAC3BrF,EAAE,IAAK,IAAI,CAACwF,OAAO,CAACxF,EAAE,EAAEc,IAAI,EAAE,OAAO,CAAC,GACtCd,EAAE,IAAK,IAAI,CAACyF,WAAW,CAACzF,EAAE,EAAEc,IAAI,CAAC,CAAClI,MAAM,EAAE,CAAA;AAC/C,QAAA,IAAI,CAAC4L,WAAW,CAACc,SAAS,CAAC,CAAC9J,MAAM,CAAC,GAAGqE,SAAS,CAAC0F,MAAM,CAAC,CAAA;AACzD,OAAA;MACA,OAAO,IAAI,CAACf,WAAW,CAACc,SAAS,CAAC,CAAC9J,MAAM,CAAC,CAAA;AAC5C,KAAC,CAAC,CAAA;AACJ,GAAA;AAEAkK,EAAAA,QAAQA,CAAClK,MAAM,EAAE5C,MAAM,GAAG,KAAK,EAAE;IAC/B,OAAOyH,SAAS,CAAC,IAAI,EAAE7E,MAAM,EAAEsH,QAAgB,EAAE,MAAM;MACrD,MAAMhC,IAAI,GAAGlI,MAAM,GACb;AAAElC,UAAAA,OAAO,EAAE8E,MAAM;AAAEnF,UAAAA,IAAI,EAAE,SAAS;AAAEC,UAAAA,KAAK,EAAE,MAAM;AAAEC,UAAAA,GAAG,EAAE,SAAA;AAAU,SAAC,GACnE;AAAEG,UAAAA,OAAO,EAAE8E,MAAAA;SAAQ;AACvB8J,QAAAA,SAAS,GAAG1M,MAAM,GAAG,QAAQ,GAAG,YAAY,CAAA;MAC9C,IAAI,CAAC,IAAI,CAAC0L,aAAa,CAACgB,SAAS,CAAC,CAAC9J,MAAM,CAAC,EAAE;QAC1C,IAAI,CAAC8I,aAAa,CAACgB,SAAS,CAAC,CAAC9J,MAAM,CAAC,GAAG4E,WAAW,CAAEJ,EAAE,IACrD,IAAI,CAACwF,OAAO,CAACxF,EAAE,EAAEc,IAAI,EAAE,SAAS,CAClC,CAAC,CAAA;AACH,OAAA;MACA,OAAO,IAAI,CAACwD,aAAa,CAACgB,SAAS,CAAC,CAAC9J,MAAM,CAAC,CAAA;AAC9C,KAAC,CAAC,CAAA;AACJ,GAAA;AAEAmK,EAAAA,SAASA,GAAG;IACV,OAAOtF,SAAS,CACd,IAAI,EACJnG,SAAS,EACT,MAAM4I,SAAiB,EACvB,MAAM;AACJ;AACA;AACA,MAAA,IAAI,CAAC,IAAI,CAAC2B,aAAa,EAAE;AACvB,QAAA,MAAM3D,IAAI,GAAG;AAAEhK,UAAAA,IAAI,EAAE,SAAS;AAAEQ,UAAAA,SAAS,EAAE,KAAA;SAAO,CAAA;QAClD,IAAI,CAACmN,aAAa,GAAG,CAACxE,QAAQ,CAACC,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAED,QAAQ,CAACC,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAACiC,GAAG,CACrFnC,EAAE,IAAK,IAAI,CAACwF,OAAO,CAACxF,EAAE,EAAEc,IAAI,EAAE,WAAW,CAC5C,CAAC,CAAA;AACH,OAAA;MAEA,OAAO,IAAI,CAAC2D,aAAa,CAAA;AAC3B,KACF,CAAC,CAAA;AACH,GAAA;EAEAmB,IAAIA,CAACpK,MAAM,EAAE;IACX,OAAO6E,SAAS,CAAC,IAAI,EAAE7E,MAAM,EAAEsH,IAAY,EAAE,MAAM;AACjD,MAAA,MAAMhC,IAAI,GAAG;AAAE1G,QAAAA,GAAG,EAAEoB,MAAAA;OAAQ,CAAA;;AAE5B;AACA;AACA,MAAA,IAAI,CAAC,IAAI,CAACkJ,QAAQ,CAAClJ,MAAM,CAAC,EAAE;QAC1B,IAAI,CAACkJ,QAAQ,CAAClJ,MAAM,CAAC,GAAG,CAACyE,QAAQ,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAED,QAAQ,CAACC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAACiC,GAAG,CAAEnC,EAAE,IACjF,IAAI,CAACwF,OAAO,CAACxF,EAAE,EAAEc,IAAI,EAAE,KAAK,CAC9B,CAAC,CAAA;AACH,OAAA;AAEA,MAAA,OAAO,IAAI,CAAC4D,QAAQ,CAAClJ,MAAM,CAAC,CAAA;AAC9B,KAAC,CAAC,CAAA;AACJ,GAAA;AAEAgK,EAAAA,OAAOA,CAACxF,EAAE,EAAEqB,QAAQ,EAAEwE,KAAK,EAAE;IAC3B,MAAMC,EAAE,GAAG,IAAI,CAACL,WAAW,CAACzF,EAAE,EAAEqB,QAAQ,CAAC;AACvC0E,MAAAA,OAAO,GAAGD,EAAE,CAACzK,aAAa,EAAE;AAC5B2K,MAAAA,QAAQ,GAAGD,OAAO,CAACE,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC9N,IAAI,CAAC+N,WAAW,EAAE,KAAKN,KAAK,CAAC,CAAA;AAChE,IAAA,OAAOG,QAAQ,GAAGA,QAAQ,CAACvK,KAAK,GAAG,IAAI,CAAA;AACzC,GAAA;AAEA2K,EAAAA,eAAeA,CAAC1N,IAAI,GAAG,EAAE,EAAE;AACzB;AACA;AACA,IAAA,OAAO,IAAImI,mBAAmB,CAAC,IAAI,CAACC,IAAI,EAAEpI,IAAI,CAACqI,WAAW,IAAI,IAAI,CAAC6D,WAAW,EAAElM,IAAI,CAAC,CAAA;AACvF,GAAA;AAEA+M,EAAAA,WAAWA,CAACzF,EAAE,EAAEqB,QAAQ,GAAG,EAAE,EAAE;IAC7B,OAAO,IAAIM,iBAAiB,CAAC3B,EAAE,EAAE,IAAI,CAACc,IAAI,EAAEO,QAAQ,CAAC,CAAA;AACvD,GAAA;AAEAgF,EAAAA,YAAYA,CAAC3N,IAAI,GAAG,EAAE,EAAE;AACtB,IAAA,OAAO,IAAI8J,gBAAgB,CAAC,IAAI,CAAC1B,IAAI,EAAE,IAAI,CAAC2B,SAAS,EAAE,EAAE/J,IAAI,CAAC,CAAA;AAChE,GAAA;AAEA4N,EAAAA,aAAaA,CAAC5N,IAAI,GAAG,EAAE,EAAE;AACvB,IAAA,OAAOyE,WAAW,CAAC,IAAI,CAAC2D,IAAI,EAAEpI,IAAI,CAAC,CAAA;AACrC,GAAA;AAEA+J,EAAAA,SAASA,GAAG;AACV,IAAA,OACE,IAAI,CAACjJ,MAAM,KAAK,IAAI,IACpB,IAAI,CAACA,MAAM,CAAC2M,WAAW,EAAE,KAAK,OAAO,IACrC5H,2BAA2B,CAAC,IAAI,CAACuC,IAAI,CAAC,CAACtH,MAAM,CAACoH,UAAU,CAAC,OAAO,CAAC,CAAA;AAErE,GAAA;AAEA2F,EAAAA,eAAeA,GAAG;IAChB,IAAI,IAAI,CAACnD,YAAY,EAAE;MACrB,OAAO,IAAI,CAACA,YAAY,CAAA;AAC1B,KAAC,MAAM,IAAI,CAACoD,iBAAiB,EAAE,EAAE;AAC/B,MAAA,OAAO1H,oBAAoB,CAAA;AAC7B,KAAC,MAAM;AACL,MAAA,OAAOL,iBAAiB,CAAC,IAAI,CAACjF,MAAM,CAAC,CAAA;AACvC,KAAA;AACF,GAAA;AAEAiN,EAAAA,cAAcA,GAAG;AACf,IAAA,OAAO,IAAI,CAACF,eAAe,EAAE,CAACvD,QAAQ,CAAA;AACxC,GAAA;AAEA0D,EAAAA,qBAAqBA,GAAG;AACtB,IAAA,OAAO,IAAI,CAACH,eAAe,EAAE,CAACtD,WAAW,CAAA;AAC3C,GAAA;AAEA0D,EAAAA,cAAcA,GAAG;AACf,IAAA,OAAO,IAAI,CAACJ,eAAe,EAAE,CAACrD,OAAO,CAAA;AACvC,GAAA;EAEApK,MAAMA,CAAC8N,KAAK,EAAE;IACZ,OACE,IAAI,CAACpN,MAAM,KAAKoN,KAAK,CAACpN,MAAM,IAC5B,IAAI,CAACgG,eAAe,KAAKoH,KAAK,CAACpH,eAAe,IAC9C,IAAI,CAACG,cAAc,KAAKiH,KAAK,CAACjH,cAAc,CAAA;AAEhD,GAAA;AAEAkH,EAAAA,QAAQA,GAAG;AACT,IAAA,OAAQ,CAAS,OAAA,EAAA,IAAI,CAACrN,MAAO,CAAI,EAAA,EAAA,IAAI,CAACgG,eAAgB,CAAI,EAAA,EAAA,IAAI,CAACG,cAAe,CAAE,CAAA,CAAA,CAAA;AAClF,GAAA;AACF;;ACrjBA,IAAI1G,SAAS,GAAG,IAAI,CAAA;;AAEpB;AACA;AACA;AACA;AACe,MAAM6N,eAAe,SAAS3O,IAAI,CAAC;AAChD;AACF;AACA;AACA;EACE,WAAW4O,WAAWA,GAAG;IACvB,IAAI9N,SAAS,KAAK,IAAI,EAAE;AACtBA,MAAAA,SAAS,GAAG,IAAI6N,eAAe,CAAC,CAAC,CAAC,CAAA;AACpC,KAAA;AACA,IAAA,OAAO7N,SAAS,CAAA;AAClB,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,OAAOE,QAAQA,CAACN,MAAM,EAAE;AACtB,IAAA,OAAOA,MAAM,KAAK,CAAC,GAAGiO,eAAe,CAACC,WAAW,GAAG,IAAID,eAAe,CAACjO,MAAM,CAAC,CAAA;AACjF,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOmO,cAAcA,CAAC9Q,CAAC,EAAE;AACvB,IAAA,IAAIA,CAAC,EAAE;AACL,MAAA,MAAM+Q,CAAC,GAAG/Q,CAAC,CAACgR,KAAK,CAAC,uCAAuC,CAAC,CAAA;AAC1D,MAAA,IAAID,CAAC,EAAE;AACL,QAAA,OAAO,IAAIH,eAAe,CAACK,YAAY,CAACF,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AACtD,OAAA;AACF,KAAA;AACA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEA1R,WAAWA,CAACsD,MAAM,EAAE;AAClB,IAAA,KAAK,EAAE,CAAA;AACP;IACA,IAAI,CAAC2I,KAAK,GAAG3I,MAAM,CAAA;AACrB,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAIT,IAAIA,GAAG;AACT,IAAA,OAAO,OAAO,CAAA;AAChB,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIC,IAAIA,GAAG;AACT,IAAA,OAAO,IAAI,CAACmJ,KAAK,KAAK,CAAC,GAAG,KAAK,GAAI,CAAK7I,GAAAA,EAAAA,YAAY,CAAC,IAAI,CAAC6I,KAAK,EAAE,QAAQ,CAAE,CAAC,CAAA,CAAA;AAC9E,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIlJ,QAAQA,GAAG;AACb,IAAA,IAAI,IAAI,CAACkJ,KAAK,KAAK,CAAC,EAAE;AACpB,MAAA,OAAO,SAAS,CAAA;AAClB,KAAC,MAAM;MACL,OAAQ,CAAA,OAAA,EAAS7I,YAAY,CAAC,CAAC,IAAI,CAAC6I,KAAK,EAAE,QAAQ,CAAE,CAAC,CAAA,CAAA;AACxD,KAAA;AACF,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACEhJ,EAAAA,UAAUA,GAAG;IACX,OAAO,IAAI,CAACH,IAAI,CAAA;AAClB,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACEM,EAAAA,YAAYA,CAACF,EAAE,EAAEG,MAAM,EAAE;AACvB,IAAA,OAAOD,YAAY,CAAC,IAAI,CAAC6I,KAAK,EAAE5I,MAAM,CAAC,CAAA;AACzC,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIL,WAAWA,GAAG;AAChB,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACEM,EAAAA,MAAMA,GAAG;IACP,OAAO,IAAI,CAAC2I,KAAK,CAAA;AACnB,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE1I,MAAMA,CAACC,SAAS,EAAE;AAChB,IAAA,OAAOA,SAAS,CAACX,IAAI,KAAK,OAAO,IAAIW,SAAS,CAACyI,KAAK,KAAK,IAAI,CAACA,KAAK,CAAA;AACrE,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIxI,OAAOA,GAAG;AACZ,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AACF;;ACnJA;AACA;AACA;AACA;AACe,MAAMoO,WAAW,SAASjP,IAAI,CAAC;EAC5C5C,WAAWA,CAACwE,QAAQ,EAAE;AACpB,IAAA,KAAK,EAAE,CAAA;AACP;IACA,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAAA;AAC1B,GAAA;;AAEA;EACA,IAAI3B,IAAIA,GAAG;AACT,IAAA,OAAO,SAAS,CAAA;AAClB,GAAA;;AAEA;EACA,IAAIC,IAAIA,GAAG;IACT,OAAO,IAAI,CAAC0B,QAAQ,CAAA;AACtB,GAAA;;AAEA;EACA,IAAIxB,WAAWA,GAAG;AAChB,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;;AAEA;AACAC,EAAAA,UAAUA,GAAG;AACX,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACAG,EAAAA,YAAYA,GAAG;AACb,IAAA,OAAO,EAAE,CAAA;AACX,GAAA;;AAEA;AACAE,EAAAA,MAAMA,GAAG;AACP,IAAA,OAAO0D,GAAG,CAAA;AACZ,GAAA;;AAEA;AACAzD,EAAAA,MAAMA,GAAG;AACP,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;;AAEA;EACA,IAAIE,OAAOA,GAAG;AACZ,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;AACF;;ACpDA;AACA;AACA;AAUO,SAASqO,aAAaA,CAACC,KAAK,EAAEC,WAAW,EAAE;EAEhD,IAAI5L,WAAW,CAAC2L,KAAK,CAAC,IAAIA,KAAK,KAAK,IAAI,EAAE;AACxC,IAAA,OAAOC,WAAW,CAAA;AACpB,GAAC,MAAM,IAAID,KAAK,YAAYnP,IAAI,EAAE;AAChC,IAAA,OAAOmP,KAAK,CAAA;AACd,GAAC,MAAM,IAAIE,QAAQ,CAACF,KAAK,CAAC,EAAE;AAC1B,IAAA,MAAMG,OAAO,GAAGH,KAAK,CAACnB,WAAW,EAAE,CAAA;IACnC,IAAIsB,OAAO,KAAK,SAAS,EAAE,OAAOF,WAAW,CAAC,KACzC,IAAIE,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,QAAQ,EAAE,OAAOvO,UAAU,CAACC,QAAQ,CAAC,KAC5E,IAAIsO,OAAO,KAAK,KAAK,IAAIA,OAAO,KAAK,KAAK,EAAE,OAAOX,eAAe,CAACC,WAAW,CAAC,KAC/E,OAAOD,eAAe,CAACE,cAAc,CAACS,OAAO,CAAC,IAAI3L,QAAQ,CAACC,MAAM,CAACuL,KAAK,CAAC,CAAA;AAC/E,GAAC,MAAM,IAAII,QAAQ,CAACJ,KAAK,CAAC,EAAE;AAC1B,IAAA,OAAOR,eAAe,CAAC3N,QAAQ,CAACmO,KAAK,CAAC,CAAA;AACxC,GAAC,MAAM,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,QAAQ,IAAIA,KAAK,IAAI,OAAOA,KAAK,CAACzO,MAAM,KAAK,UAAU,EAAE;AAC/F;AACA;AACA,IAAA,OAAOyO,KAAK,CAAA;AACd,GAAC,MAAM;AACL,IAAA,OAAO,IAAIF,WAAW,CAACE,KAAK,CAAC,CAAA;AAC/B,GAAA;AACF;;ACjCA,MAAMK,gBAAgB,GAAG;AACvBC,EAAAA,IAAI,EAAE,iBAAiB;AACvBC,EAAAA,OAAO,EAAE,iBAAiB;AAC1BC,EAAAA,IAAI,EAAE,iBAAiB;AACvBC,EAAAA,IAAI,EAAE,iBAAiB;AACvBC,EAAAA,IAAI,EAAE,iBAAiB;AACvBC,EAAAA,QAAQ,EAAE,iBAAiB;AAC3BC,EAAAA,IAAI,EAAE,iBAAiB;AACvBC,EAAAA,OAAO,EAAE,uBAAuB;AAChCC,EAAAA,IAAI,EAAE,iBAAiB;AACvBC,EAAAA,IAAI,EAAE,iBAAiB;AACvBC,EAAAA,IAAI,EAAE,iBAAiB;AACvBC,EAAAA,IAAI,EAAE,iBAAiB;AACvBC,EAAAA,IAAI,EAAE,iBAAiB;AACvBC,EAAAA,IAAI,EAAE,iBAAiB;AACvBC,EAAAA,IAAI,EAAE,iBAAiB;AACvBC,EAAAA,IAAI,EAAE,iBAAiB;AACvBC,EAAAA,OAAO,EAAE,iBAAiB;AAC1BC,EAAAA,IAAI,EAAE,iBAAiB;AACvBC,EAAAA,IAAI,EAAE,iBAAiB;AACvBC,EAAAA,IAAI,EAAE,iBAAiB;AACvBC,EAAAA,IAAI,EAAE,KAAA;AACR,CAAC,CAAA;AAED,MAAMC,qBAAqB,GAAG;AAC5BrB,EAAAA,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAClBC,EAAAA,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AACrBC,EAAAA,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAClBC,EAAAA,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAClBC,EAAAA,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAClBC,EAAAA,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;AACxBC,EAAAA,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAClBE,EAAAA,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAClBC,EAAAA,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAClBC,EAAAA,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAClBC,EAAAA,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAClBC,EAAAA,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAClBC,EAAAA,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAClBC,EAAAA,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAClBC,EAAAA,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAClBC,EAAAA,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AACrBC,EAAAA,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAClBC,EAAAA,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAClBC,EAAAA,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAA;AACnB,CAAC,CAAA;AAED,MAAMG,YAAY,GAAGvB,gBAAgB,CAACQ,OAAO,CAACzN,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACyO,KAAK,CAAC,EAAE,CAAC,CAAA;AAExE,SAASC,WAAWA,CAACC,GAAG,EAAE;AAC/B,EAAA,IAAI5N,KAAK,GAAGG,QAAQ,CAACyN,GAAG,EAAE,EAAE,CAAC,CAAA;AAC7B,EAAA,IAAI7M,KAAK,CAACf,KAAK,CAAC,EAAE;AAChBA,IAAAA,KAAK,GAAG,EAAE,CAAA;AACV,IAAA,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8N,GAAG,CAAC7N,MAAM,EAAED,CAAC,EAAE,EAAE;AACnC,MAAA,MAAM+N,IAAI,GAAGD,GAAG,CAACE,UAAU,CAAChO,CAAC,CAAC,CAAA;AAE9B,MAAA,IAAI8N,GAAG,CAAC9N,CAAC,CAAC,CAACiO,MAAM,CAAC7B,gBAAgB,CAACQ,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;QAClD1M,KAAK,IAAIyN,YAAY,CAAChK,OAAO,CAACmK,GAAG,CAAC9N,CAAC,CAAC,CAAC,CAAA;AACvC,OAAC,MAAM;AACL,QAAA,KAAK,MAAM8B,GAAG,IAAI4L,qBAAqB,EAAE;UACvC,MAAM,CAACQ,GAAG,EAAEC,GAAG,CAAC,GAAGT,qBAAqB,CAAC5L,GAAG,CAAC,CAAA;AAC7C,UAAA,IAAIiM,IAAI,IAAIG,GAAG,IAAIH,IAAI,IAAII,GAAG,EAAE;YAC9BjO,KAAK,IAAI6N,IAAI,GAAGG,GAAG,CAAA;AACrB,WAAA;AACF,SAAA;AACF,OAAA;AACF,KAAA;AACA,IAAA,OAAO7N,QAAQ,CAACH,KAAK,EAAE,EAAE,CAAC,CAAA;AAC5B,GAAC,MAAM;AACL,IAAA,OAAOA,KAAK,CAAA;AACd,GAAA;AACF,CAAA;;AAEA;AACA,MAAMkO,eAAe,GAAG,IAAI9P,GAAG,EAAE,CAAA;AAC1B,SAAS+P,oBAAoBA,GAAG;EACrCD,eAAe,CAACzN,KAAK,EAAE,CAAA;AACzB,CAAA;AAEO,SAAS2N,UAAUA,CAAC;AAAErK,EAAAA,eAAAA;AAAgB,CAAC,EAAEsK,MAAM,GAAG,EAAE,EAAE;AAC3D,EAAA,MAAMC,EAAE,GAAGvK,eAAe,IAAI,MAAM,CAAA;AAEpC,EAAA,IAAIwK,WAAW,GAAGL,eAAe,CAAC1P,GAAG,CAAC8P,EAAE,CAAC,CAAA;EACzC,IAAIC,WAAW,KAAK9P,SAAS,EAAE;AAC7B8P,IAAAA,WAAW,GAAG,IAAInQ,GAAG,EAAE,CAAA;AACvB8P,IAAAA,eAAe,CAACtP,GAAG,CAAC0P,EAAE,EAAEC,WAAW,CAAC,CAAA;AACtC,GAAA;AACA,EAAA,IAAIC,KAAK,GAAGD,WAAW,CAAC/P,GAAG,CAAC6P,MAAM,CAAC,CAAA;EACnC,IAAIG,KAAK,KAAK/P,SAAS,EAAE;AACvB+P,IAAAA,KAAK,GAAG,IAAIC,MAAM,CAAE,CAAEvC,EAAAA,gBAAgB,CAACoC,EAAE,CAAE,CAAA,EAAED,MAAO,CAAA,CAAC,CAAC,CAAA;AACtDE,IAAAA,WAAW,CAAC3P,GAAG,CAACyP,MAAM,EAAEG,KAAK,CAAC,CAAA;AAChC,GAAA;AAEA,EAAA,OAAOA,KAAK,CAAA;AACd;;ACpFA,IAAIE,GAAG,GAAGA,MAAMzQ,IAAI,CAACyQ,GAAG,EAAE;AACxB5C,EAAAA,WAAW,GAAG,QAAQ;AACtB/D,EAAAA,aAAa,GAAG,IAAI;AACpBG,EAAAA,sBAAsB,GAAG,IAAI;AAC7BE,EAAAA,qBAAqB,GAAG,IAAI;AAC5BuG,EAAAA,kBAAkB,GAAG,EAAE;EACvBC,cAAc;AACdrG,EAAAA,mBAAmB,GAAG,IAAI,CAAA;;AAE5B;AACA;AACA;AACe,MAAMT,QAAQ,CAAC;AAC5B;AACF;AACA;AACA;EACE,WAAW4G,GAAGA,GAAG;AACf,IAAA,OAAOA,GAAG,CAAA;AACZ,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,WAAWA,GAAGA,CAAClU,CAAC,EAAE;AAChBkU,IAAAA,GAAG,GAAGlU,CAAC,CAAA;AACT,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,WAAWsR,WAAWA,CAACvL,IAAI,EAAE;AAC3BuL,IAAAA,WAAW,GAAGvL,IAAI,CAAA;AACpB,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,WAAWuL,WAAWA,GAAG;AACvB,IAAA,OAAOF,aAAa,CAACE,WAAW,EAAErO,UAAU,CAACC,QAAQ,CAAC,CAAA;AACxD,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWqK,aAAaA,GAAG;AACzB,IAAA,OAAOA,aAAa,CAAA;AACtB,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWA,aAAaA,CAAChK,MAAM,EAAE;AAC/BgK,IAAAA,aAAa,GAAGhK,MAAM,CAAA;AACxB,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWmK,sBAAsBA,GAAG;AAClC,IAAA,OAAOA,sBAAsB,CAAA;AAC/B,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWA,sBAAsBA,CAACnE,eAAe,EAAE;AACjDmE,IAAAA,sBAAsB,GAAGnE,eAAe,CAAA;AAC1C,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWqE,qBAAqBA,GAAG;AACjC,IAAA,OAAOA,qBAAqB,CAAA;AAC9B,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWA,qBAAqBA,CAAClE,cAAc,EAAE;AAC/CkE,IAAAA,qBAAqB,GAAGlE,cAAc,CAAA;AACxC,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;;AAEE;AACF;AACA;EACE,WAAWqE,mBAAmBA,GAAG;AAC/B,IAAA,OAAOA,mBAAmB,CAAA;AAC5B,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,WAAWA,mBAAmBA,CAACZ,YAAY,EAAE;AAC3CY,IAAAA,mBAAmB,GAAGD,oBAAoB,CAACX,YAAY,CAAC,CAAA;AAC1D,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWgH,kBAAkBA,GAAG;AAC9B,IAAA,OAAOA,kBAAkB,CAAA;AAC3B,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,WAAWA,kBAAkBA,CAACE,UAAU,EAAE;IACxCF,kBAAkB,GAAGE,UAAU,GAAG,GAAG,CAAA;AACvC,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWD,cAAcA,GAAG;AAC1B,IAAA,OAAOA,cAAc,CAAA;AACvB,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWA,cAAcA,CAACE,CAAC,EAAE;AAC3BF,IAAAA,cAAc,GAAGE,CAAC,CAAA;AACpB,GAAA;;AAEA;AACF;AACA;AACA;EACE,OAAOC,WAAWA,GAAG;IACnB7L,MAAM,CAAC1C,UAAU,EAAE,CAAA;IACnBH,QAAQ,CAACG,UAAU,EAAE,CAAA;IACrBgE,QAAQ,CAAChE,UAAU,EAAE,CAAA;AACrB2N,IAAAA,oBAAoB,EAAE,CAAA;AACxB,GAAA;AACF;;ACnLe,MAAMa,OAAO,CAAC;AAC3BlV,EAAAA,WAAWA,CAACC,MAAM,EAAEkV,WAAW,EAAE;IAC/B,IAAI,CAAClV,MAAM,GAAGA,MAAM,CAAA;IACpB,IAAI,CAACkV,WAAW,GAAGA,WAAW,CAAA;AAChC,GAAA;AAEAjV,EAAAA,SAASA,GAAG;IACV,IAAI,IAAI,CAACiV,WAAW,EAAE;MACpB,OAAQ,CAAA,EAAE,IAAI,CAAClV,MAAO,KAAI,IAAI,CAACkV,WAAY,CAAC,CAAA,CAAA;AAC9C,KAAC,MAAM;MACL,OAAO,IAAI,CAAClV,MAAM,CAAA;AACpB,KAAA;AACF,GAAA;AACF;;ACAA,MAAMmV,aAAa,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3EC,UAAU,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;AAEtE,SAASC,cAAcA,CAAC/U,IAAI,EAAE2F,KAAK,EAAE;AACnC,EAAA,OAAO,IAAIgP,OAAO,CAChB,mBAAmB,EAClB,CAAA,cAAA,EAAgBhP,KAAM,CAAA,UAAA,EAAY,OAAOA,KAAM,CAAS3F,OAAAA,EAAAA,IAAK,oBAChE,CAAC,CAAA;AACH,CAAA;AAEO,SAASgV,SAASA,CAACzU,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAE;AAC1C,EAAA,MAAMwU,CAAC,GAAG,IAAIrR,IAAI,CAACA,IAAI,CAACsR,GAAG,CAAC3U,IAAI,EAAEC,KAAK,GAAG,CAAC,EAAEC,GAAG,CAAC,CAAC,CAAA;AAElD,EAAA,IAAIF,IAAI,GAAG,GAAG,IAAIA,IAAI,IAAI,CAAC,EAAE;IAC3B0U,CAAC,CAACE,cAAc,CAACF,CAAC,CAACG,cAAc,EAAE,GAAG,IAAI,CAAC,CAAA;AAC7C,GAAA;AAEA,EAAA,MAAMC,EAAE,GAAGJ,CAAC,CAACK,SAAS,EAAE,CAAA;AAExB,EAAA,OAAOD,EAAE,KAAK,CAAC,GAAG,CAAC,GAAGA,EAAE,CAAA;AAC1B,CAAA;AAEA,SAASE,cAAcA,CAAChV,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAE;AACxC,EAAA,OAAOA,GAAG,GAAG,CAAC+U,UAAU,CAACjV,IAAI,CAAC,GAAGuU,UAAU,GAAGD,aAAa,EAAErU,KAAK,GAAG,CAAC,CAAC,CAAA;AACzE,CAAA;AAEA,SAASiV,gBAAgBA,CAAClV,IAAI,EAAEmV,OAAO,EAAE;EACvC,MAAMC,KAAK,GAAGH,UAAU,CAACjV,IAAI,CAAC,GAAGuU,UAAU,GAAGD,aAAa;IACzDe,MAAM,GAAGD,KAAK,CAACE,SAAS,CAAEpQ,CAAC,IAAKA,CAAC,GAAGiQ,OAAO,CAAC;AAC5CjV,IAAAA,GAAG,GAAGiV,OAAO,GAAGC,KAAK,CAACC,MAAM,CAAC,CAAA;EAC/B,OAAO;IAAEpV,KAAK,EAAEoV,MAAM,GAAG,CAAC;AAAEnV,IAAAA,GAAAA;GAAK,CAAA;AACnC,CAAA;AAEO,SAASqV,iBAAiBA,CAACC,UAAU,EAAEC,WAAW,EAAE;EACzD,OAAQ,CAACD,UAAU,GAAGC,WAAW,GAAG,CAAC,IAAI,CAAC,GAAI,CAAC,CAAA;AACjD,CAAA;;AAEA;AACA;AACA;;AAEO,SAASC,eAAeA,CAACC,OAAO,EAAEC,kBAAkB,GAAG,CAAC,EAAEH,WAAW,GAAG,CAAC,EAAE;EAChF,MAAM;MAAEzV,IAAI;MAAEC,KAAK;AAAEC,MAAAA,GAAAA;AAAI,KAAC,GAAGyV,OAAO;IAClCR,OAAO,GAAGH,cAAc,CAAChV,IAAI,EAAEC,KAAK,EAAEC,GAAG,CAAC;AAC1CG,IAAAA,OAAO,GAAGkV,iBAAiB,CAACd,SAAS,CAACzU,IAAI,EAAEC,KAAK,EAAEC,GAAG,CAAC,EAAEuV,WAAW,CAAC,CAAA;AAEvE,EAAA,IAAII,UAAU,GAAGxP,IAAI,CAACuE,KAAK,CAAC,CAACuK,OAAO,GAAG9U,OAAO,GAAG,EAAE,GAAGuV,kBAAkB,IAAI,CAAC,CAAC;IAC5EE,QAAQ,CAAA;EAEV,IAAID,UAAU,GAAG,CAAC,EAAE;IAClBC,QAAQ,GAAG9V,IAAI,GAAG,CAAC,CAAA;IACnB6V,UAAU,GAAGE,eAAe,CAACD,QAAQ,EAAEF,kBAAkB,EAAEH,WAAW,CAAC,CAAA;AACzE,GAAC,MAAM,IAAII,UAAU,GAAGE,eAAe,CAAC/V,IAAI,EAAE4V,kBAAkB,EAAEH,WAAW,CAAC,EAAE;IAC9EK,QAAQ,GAAG9V,IAAI,GAAG,CAAC,CAAA;AACnB6V,IAAAA,UAAU,GAAG,CAAC,CAAA;AAChB,GAAC,MAAM;AACLC,IAAAA,QAAQ,GAAG9V,IAAI,CAAA;AACjB,GAAA;EAEA,OAAO;IAAE8V,QAAQ;IAAED,UAAU;IAAExV,OAAO;IAAE,GAAG2V,UAAU,CAACL,OAAO,CAAA;GAAG,CAAA;AAClE,CAAA;AAEO,SAASM,eAAeA,CAACC,QAAQ,EAAEN,kBAAkB,GAAG,CAAC,EAAEH,WAAW,GAAG,CAAC,EAAE;EACjF,MAAM;MAAEK,QAAQ;MAAED,UAAU;AAAExV,MAAAA,OAAAA;AAAQ,KAAC,GAAG6V,QAAQ;AAChDC,IAAAA,aAAa,GAAGZ,iBAAiB,CAACd,SAAS,CAACqB,QAAQ,EAAE,CAAC,EAAEF,kBAAkB,CAAC,EAAEH,WAAW,CAAC;AAC1FW,IAAAA,UAAU,GAAGC,UAAU,CAACP,QAAQ,CAAC,CAAA;AAEnC,EAAA,IAAIX,OAAO,GAAGU,UAAU,GAAG,CAAC,GAAGxV,OAAO,GAAG8V,aAAa,GAAG,CAAC,GAAGP,kBAAkB;IAC7E5V,IAAI,CAAA;EAEN,IAAImV,OAAO,GAAG,CAAC,EAAE;IACfnV,IAAI,GAAG8V,QAAQ,GAAG,CAAC,CAAA;AACnBX,IAAAA,OAAO,IAAIkB,UAAU,CAACrW,IAAI,CAAC,CAAA;AAC7B,GAAC,MAAM,IAAImV,OAAO,GAAGiB,UAAU,EAAE;IAC/BpW,IAAI,GAAG8V,QAAQ,GAAG,CAAC,CAAA;AACnBX,IAAAA,OAAO,IAAIkB,UAAU,CAACP,QAAQ,CAAC,CAAA;AACjC,GAAC,MAAM;AACL9V,IAAAA,IAAI,GAAG8V,QAAQ,CAAA;AACjB,GAAA;EAEA,MAAM;IAAE7V,KAAK;AAAEC,IAAAA,GAAAA;AAAI,GAAC,GAAGgV,gBAAgB,CAAClV,IAAI,EAAEmV,OAAO,CAAC,CAAA;EACtD,OAAO;IAAEnV,IAAI;IAAEC,KAAK;IAAEC,GAAG;IAAE,GAAG8V,UAAU,CAACE,QAAQ,CAAA;GAAG,CAAA;AACtD,CAAA;AAEO,SAASI,kBAAkBA,CAACC,QAAQ,EAAE;EAC3C,MAAM;IAAEvW,IAAI;IAAEC,KAAK;AAAEC,IAAAA,GAAAA;AAAI,GAAC,GAAGqW,QAAQ,CAAA;EACrC,MAAMpB,OAAO,GAAGH,cAAc,CAAChV,IAAI,EAAEC,KAAK,EAAEC,GAAG,CAAC,CAAA;EAChD,OAAO;IAAEF,IAAI;IAAEmV,OAAO;IAAE,GAAGa,UAAU,CAACO,QAAQ,CAAA;GAAG,CAAA;AACnD,CAAA;AAEO,SAASC,kBAAkBA,CAACC,WAAW,EAAE;EAC9C,MAAM;IAAEzW,IAAI;AAAEmV,IAAAA,OAAAA;AAAQ,GAAC,GAAGsB,WAAW,CAAA;EACrC,MAAM;IAAExW,KAAK;AAAEC,IAAAA,GAAAA;AAAI,GAAC,GAAGgV,gBAAgB,CAAClV,IAAI,EAAEmV,OAAO,CAAC,CAAA;EACtD,OAAO;IAAEnV,IAAI;IAAEC,KAAK;IAAEC,GAAG;IAAE,GAAG8V,UAAU,CAACS,WAAW,CAAA;GAAG,CAAA;AACzD,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,mBAAmBA,CAACC,GAAG,EAAE1M,GAAG,EAAE;EAC5C,MAAM2M,iBAAiB,GACrB,CAACtR,WAAW,CAACqR,GAAG,CAACE,YAAY,CAAC,IAC9B,CAACvR,WAAW,CAACqR,GAAG,CAACG,eAAe,CAAC,IACjC,CAACxR,WAAW,CAACqR,GAAG,CAACI,aAAa,CAAC,CAAA;AACjC,EAAA,IAAIH,iBAAiB,EAAE;IACrB,MAAMI,cAAc,GAClB,CAAC1R,WAAW,CAACqR,GAAG,CAACtW,OAAO,CAAC,IAAI,CAACiF,WAAW,CAACqR,GAAG,CAACd,UAAU,CAAC,IAAI,CAACvQ,WAAW,CAACqR,GAAG,CAACb,QAAQ,CAAC,CAAA;AAEzF,IAAA,IAAIkB,cAAc,EAAE;AAClB,MAAA,MAAM,IAAIzX,6BAA6B,CACrC,gEACF,CAAC,CAAA;AACH,KAAA;AACA,IAAA,IAAI,CAAC+F,WAAW,CAACqR,GAAG,CAACE,YAAY,CAAC,EAAEF,GAAG,CAACtW,OAAO,GAAGsW,GAAG,CAACE,YAAY,CAAA;AAClE,IAAA,IAAI,CAACvR,WAAW,CAACqR,GAAG,CAACG,eAAe,CAAC,EAAEH,GAAG,CAACd,UAAU,GAAGc,GAAG,CAACG,eAAe,CAAA;AAC3E,IAAA,IAAI,CAACxR,WAAW,CAACqR,GAAG,CAACI,aAAa,CAAC,EAAEJ,GAAG,CAACb,QAAQ,GAAGa,GAAG,CAACI,aAAa,CAAA;IACrE,OAAOJ,GAAG,CAACE,YAAY,CAAA;IACvB,OAAOF,GAAG,CAACG,eAAe,CAAA;IAC1B,OAAOH,GAAG,CAACI,aAAa,CAAA;IACxB,OAAO;AACLnB,MAAAA,kBAAkB,EAAE3L,GAAG,CAACoG,qBAAqB,EAAE;AAC/CoF,MAAAA,WAAW,EAAExL,GAAG,CAACmG,cAAc,EAAC;KACjC,CAAA;AACH,GAAC,MAAM;IACL,OAAO;AAAEwF,MAAAA,kBAAkB,EAAE,CAAC;AAAEH,MAAAA,WAAW,EAAE,CAAA;KAAG,CAAA;AAClD,GAAA;AACF,CAAA;AAEO,SAASwB,kBAAkBA,CAACN,GAAG,EAAEf,kBAAkB,GAAG,CAAC,EAAEH,WAAW,GAAG,CAAC,EAAE;AAC/E,EAAA,MAAMyB,SAAS,GAAGC,SAAS,CAACR,GAAG,CAACb,QAAQ,CAAC;AACvCsB,IAAAA,SAAS,GAAGC,cAAc,CACxBV,GAAG,CAACd,UAAU,EACd,CAAC,EACDE,eAAe,CAACY,GAAG,CAACb,QAAQ,EAAEF,kBAAkB,EAAEH,WAAW,CAC/D,CAAC;IACD6B,YAAY,GAAGD,cAAc,CAACV,GAAG,CAACtW,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;EAElD,IAAI,CAAC6W,SAAS,EAAE;AACd,IAAA,OAAO1C,cAAc,CAAC,UAAU,EAAEmC,GAAG,CAACb,QAAQ,CAAC,CAAA;AACjD,GAAC,MAAM,IAAI,CAACsB,SAAS,EAAE;AACrB,IAAA,OAAO5C,cAAc,CAAC,MAAM,EAAEmC,GAAG,CAACd,UAAU,CAAC,CAAA;AAC/C,GAAC,MAAM,IAAI,CAACyB,YAAY,EAAE;AACxB,IAAA,OAAO9C,cAAc,CAAC,SAAS,EAAEmC,GAAG,CAACtW,OAAO,CAAC,CAAA;GAC9C,MAAM,OAAO,KAAK,CAAA;AACrB,CAAA;AAEO,SAASkX,qBAAqBA,CAACZ,GAAG,EAAE;AACzC,EAAA,MAAMO,SAAS,GAAGC,SAAS,CAACR,GAAG,CAAC3W,IAAI,CAAC;AACnCwX,IAAAA,YAAY,GAAGH,cAAc,CAACV,GAAG,CAACxB,OAAO,EAAE,CAAC,EAAEkB,UAAU,CAACM,GAAG,CAAC3W,IAAI,CAAC,CAAC,CAAA;EAErE,IAAI,CAACkX,SAAS,EAAE;AACd,IAAA,OAAO1C,cAAc,CAAC,MAAM,EAAEmC,GAAG,CAAC3W,IAAI,CAAC,CAAA;AACzC,GAAC,MAAM,IAAI,CAACwX,YAAY,EAAE;AACxB,IAAA,OAAOhD,cAAc,CAAC,SAAS,EAAEmC,GAAG,CAACxB,OAAO,CAAC,CAAA;GAC9C,MAAM,OAAO,KAAK,CAAA;AACrB,CAAA;AAEO,SAASsC,uBAAuBA,CAACd,GAAG,EAAE;AAC3C,EAAA,MAAMO,SAAS,GAAGC,SAAS,CAACR,GAAG,CAAC3W,IAAI,CAAC;IACnC0X,UAAU,GAAGL,cAAc,CAACV,GAAG,CAAC1W,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;AAC7C0X,IAAAA,QAAQ,GAAGN,cAAc,CAACV,GAAG,CAACzW,GAAG,EAAE,CAAC,EAAE0X,WAAW,CAACjB,GAAG,CAAC3W,IAAI,EAAE2W,GAAG,CAAC1W,KAAK,CAAC,CAAC,CAAA;EAEzE,IAAI,CAACiX,SAAS,EAAE;AACd,IAAA,OAAO1C,cAAc,CAAC,MAAM,EAAEmC,GAAG,CAAC3W,IAAI,CAAC,CAAA;AACzC,GAAC,MAAM,IAAI,CAAC0X,UAAU,EAAE;AACtB,IAAA,OAAOlD,cAAc,CAAC,OAAO,EAAEmC,GAAG,CAAC1W,KAAK,CAAC,CAAA;AAC3C,GAAC,MAAM,IAAI,CAAC0X,QAAQ,EAAE;AACpB,IAAA,OAAOnD,cAAc,CAAC,KAAK,EAAEmC,GAAG,CAACzW,GAAG,CAAC,CAAA;GACtC,MAAM,OAAO,KAAK,CAAA;AACrB,CAAA;AAEO,SAAS2X,kBAAkBA,CAAClB,GAAG,EAAE;EACtC,MAAM;IAAElW,IAAI;IAAEC,MAAM;IAAEE,MAAM;AAAE8F,IAAAA,WAAAA;AAAY,GAAC,GAAGiQ,GAAG,CAAA;EACjD,MAAMmB,SAAS,GACXT,cAAc,CAAC5W,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,IAC1BA,IAAI,KAAK,EAAE,IAAIC,MAAM,KAAK,CAAC,IAAIE,MAAM,KAAK,CAAC,IAAI8F,WAAW,KAAK,CAAE;IACpEqR,WAAW,GAAGV,cAAc,CAAC3W,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;IAC3CsX,WAAW,GAAGX,cAAc,CAACzW,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;IAC3CqX,gBAAgB,GAAGZ,cAAc,CAAC3Q,WAAW,EAAE,CAAC,EAAE,GAAG,CAAC,CAAA;EAExD,IAAI,CAACoR,SAAS,EAAE;AACd,IAAA,OAAOtD,cAAc,CAAC,MAAM,EAAE/T,IAAI,CAAC,CAAA;AACrC,GAAC,MAAM,IAAI,CAACsX,WAAW,EAAE;AACvB,IAAA,OAAOvD,cAAc,CAAC,QAAQ,EAAE9T,MAAM,CAAC,CAAA;AACzC,GAAC,MAAM,IAAI,CAACsX,WAAW,EAAE;AACvB,IAAA,OAAOxD,cAAc,CAAC,QAAQ,EAAE5T,MAAM,CAAC,CAAA;AACzC,GAAC,MAAM,IAAI,CAACqX,gBAAgB,EAAE;AAC5B,IAAA,OAAOzD,cAAc,CAAC,aAAa,EAAE9N,WAAW,CAAC,CAAA;GAClD,MAAM,OAAO,KAAK,CAAA;AACrB;;AC7MA;AACA;AACA;AACA;AACA;;AAMA;AACA;AACA;;AAEA;;AAEO,SAASpB,WAAWA,CAAC4S,CAAC,EAAE;EAC7B,OAAO,OAAOA,CAAC,KAAK,WAAW,CAAA;AACjC,CAAA;AAEO,SAAS7G,QAAQA,CAAC6G,CAAC,EAAE;EAC1B,OAAO,OAAOA,CAAC,KAAK,QAAQ,CAAA;AAC9B,CAAA;AAEO,SAASf,SAASA,CAACe,CAAC,EAAE;EAC3B,OAAO,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;AAC7C,CAAA;AAEO,SAAS/G,QAAQA,CAAC+G,CAAC,EAAE;EAC1B,OAAO,OAAOA,CAAC,KAAK,QAAQ,CAAA;AAC9B,CAAA;AAEO,SAASC,MAAMA,CAACD,CAAC,EAAE;EACxB,OAAOpN,MAAM,CAACsN,SAAS,CAAC5H,QAAQ,CAAC6H,IAAI,CAACH,CAAC,CAAC,KAAK,eAAe,CAAA;AAC9D,CAAA;;AAEA;;AAEO,SAAS5L,WAAWA,GAAG;EAC5B,IAAI;IACF,OAAO,OAAOvJ,IAAI,KAAK,WAAW,IAAI,CAAC,CAACA,IAAI,CAAC+E,kBAAkB,CAAA;GAChE,CAAC,OAAO9B,CAAC,EAAE;AACV,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;AACF,CAAA;AAEO,SAASmK,iBAAiBA,GAAG;EAClC,IAAI;IACF,OACE,OAAOpN,IAAI,KAAK,WAAW,IAC3B,CAAC,CAACA,IAAI,CAACuF,MAAM,KACZ,UAAU,IAAIvF,IAAI,CAACuF,MAAM,CAAC8P,SAAS,IAAI,aAAa,IAAIrV,IAAI,CAACuF,MAAM,CAAC8P,SAAS,CAAC,CAAA;GAElF,CAAC,OAAOpS,CAAC,EAAE;AACV,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;AACF,CAAA;;AAEA;;AAEO,SAASsS,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAOC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC,CAAA;AAC/C,CAAA;AAEO,SAASG,MAAMA,CAACC,GAAG,EAAEC,EAAE,EAAEC,OAAO,EAAE;AACvC,EAAA,IAAIF,GAAG,CAACxT,MAAM,KAAK,CAAC,EAAE;AACpB,IAAA,OAAOtB,SAAS,CAAA;AAClB,GAAA;EACA,OAAO8U,GAAG,CAACG,MAAM,CAAC,CAACC,IAAI,EAAEC,IAAI,KAAK;IAChC,MAAMC,IAAI,GAAG,CAACL,EAAE,CAACI,IAAI,CAAC,EAAEA,IAAI,CAAC,CAAA;IAC7B,IAAI,CAACD,IAAI,EAAE;AACT,MAAA,OAAOE,IAAI,CAAA;AACb,KAAC,MAAM,IAAIJ,OAAO,CAACE,IAAI,CAAC,CAAC,CAAC,EAAEE,IAAI,CAAC,CAAC,CAAC,CAAC,KAAKF,IAAI,CAAC,CAAC,CAAC,EAAE;AAChD,MAAA,OAAOA,IAAI,CAAA;AACb,KAAC,MAAM;AACL,MAAA,OAAOE,IAAI,CAAA;AACb,KAAA;AACF,GAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;AACb,CAAA;AAEO,SAASC,IAAIA,CAACvC,GAAG,EAAE5L,IAAI,EAAE;EAC9B,OAAOA,IAAI,CAAC+N,MAAM,CAAC,CAACK,CAAC,EAAEC,CAAC,KAAK;AAC3BD,IAAAA,CAAC,CAACC,CAAC,CAAC,GAAGzC,GAAG,CAACyC,CAAC,CAAC,CAAA;AACb,IAAA,OAAOD,CAAC,CAAA;GACT,EAAE,EAAE,CAAC,CAAA;AACR,CAAA;AAEO,SAASE,cAAcA,CAAC1C,GAAG,EAAE2C,IAAI,EAAE;EACxC,OAAOxO,MAAM,CAACsN,SAAS,CAACiB,cAAc,CAAChB,IAAI,CAAC1B,GAAG,EAAE2C,IAAI,CAAC,CAAA;AACxD,CAAA;AAEO,SAAS5L,oBAAoBA,CAAC6L,QAAQ,EAAE;EAC7C,IAAIA,QAAQ,IAAI,IAAI,EAAE;AACpB,IAAA,OAAO,IAAI,CAAA;AACb,GAAC,MAAM,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;AACvC,IAAA,MAAM,IAAI7Z,oBAAoB,CAAC,iCAAiC,CAAC,CAAA;AACnE,GAAC,MAAM;IACL,IACE,CAAC2X,cAAc,CAACkC,QAAQ,CAAC5M,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,IACxC,CAAC0K,cAAc,CAACkC,QAAQ,CAAC3M,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,IAC3C,CAAC4L,KAAK,CAACC,OAAO,CAACc,QAAQ,CAAC1M,OAAO,CAAC,IAChC0M,QAAQ,CAAC1M,OAAO,CAAC2M,IAAI,CAAEC,CAAC,IAAK,CAACpC,cAAc,CAACoC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACtD;AACA,MAAA,MAAM,IAAI/Z,oBAAoB,CAAC,uBAAuB,CAAC,CAAA;AACzD,KAAA;IACA,OAAO;MACLiN,QAAQ,EAAE4M,QAAQ,CAAC5M,QAAQ;MAC3BC,WAAW,EAAE2M,QAAQ,CAAC3M,WAAW;AACjCC,MAAAA,OAAO,EAAE2L,KAAK,CAACkB,IAAI,CAACH,QAAQ,CAAC1M,OAAO,CAAA;KACrC,CAAA;AACH,GAAA;AACF,CAAA;;AAEA;;AAEO,SAASwK,cAAcA,CAACkB,KAAK,EAAEoB,MAAM,EAAEC,GAAG,EAAE;EACjD,OAAOzC,SAAS,CAACoB,KAAK,CAAC,IAAIA,KAAK,IAAIoB,MAAM,IAAIpB,KAAK,IAAIqB,GAAG,CAAA;AAC5D,CAAA;;AAEA;AACO,SAASC,QAAQA,CAACC,CAAC,EAAEla,CAAC,EAAE;EAC7B,OAAOka,CAAC,GAAGla,CAAC,GAAGyG,IAAI,CAACuE,KAAK,CAACkP,CAAC,GAAGla,CAAC,CAAC,CAAA;AAClC,CAAA;AAEO,SAASyL,QAAQA,CAAC4F,KAAK,EAAErR,CAAC,GAAG,CAAC,EAAE;AACrC,EAAA,MAAMma,KAAK,GAAG9I,KAAK,GAAG,CAAC,CAAA;AACvB,EAAA,IAAI+I,MAAM,CAAA;AACV,EAAA,IAAID,KAAK,EAAE;AACTC,IAAAA,MAAM,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC/I,KAAK,EAAE5F,QAAQ,CAACzL,CAAC,EAAE,GAAG,CAAC,CAAA;AAC/C,GAAC,MAAM;IACLoa,MAAM,GAAG,CAAC,EAAE,GAAG/I,KAAK,EAAE5F,QAAQ,CAACzL,CAAC,EAAE,GAAG,CAAC,CAAA;AACxC,GAAA;AACA,EAAA,OAAOoa,MAAM,CAAA;AACf,CAAA;AAEO,SAASC,YAAYA,CAACC,MAAM,EAAE;AACnC,EAAA,IAAI5U,WAAW,CAAC4U,MAAM,CAAC,IAAIA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,EAAE,EAAE;AAC3D,IAAA,OAAOrW,SAAS,CAAA;AAClB,GAAC,MAAM;AACL,IAAA,OAAO0B,QAAQ,CAAC2U,MAAM,EAAE,EAAE,CAAC,CAAA;AAC7B,GAAA;AACF,CAAA;AAEO,SAASC,aAAaA,CAACD,MAAM,EAAE;AACpC,EAAA,IAAI5U,WAAW,CAAC4U,MAAM,CAAC,IAAIA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,EAAE,EAAE;AAC3D,IAAA,OAAOrW,SAAS,CAAA;AAClB,GAAC,MAAM;IACL,OAAOuW,UAAU,CAACF,MAAM,CAAC,CAAA;AAC3B,GAAA;AACF,CAAA;AAEO,SAASG,WAAWA,CAACC,QAAQ,EAAE;AACpC;AACA,EAAA,IAAIhV,WAAW,CAACgV,QAAQ,CAAC,IAAIA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,EAAE,EAAE;AACjE,IAAA,OAAOzW,SAAS,CAAA;AAClB,GAAC,MAAM;IACL,MAAM4F,CAAC,GAAG2Q,UAAU,CAAC,IAAI,GAAGE,QAAQ,CAAC,GAAG,IAAI,CAAA;AAC5C,IAAA,OAAOjU,IAAI,CAACuE,KAAK,CAACnB,CAAC,CAAC,CAAA;AACtB,GAAA;AACF,CAAA;AAEO,SAAS2B,OAAOA,CAACmP,MAAM,EAAEC,MAAM,EAAEC,QAAQ,GAAG,OAAO,EAAE;AAC1D,EAAA,MAAMC,MAAM,GAAG,EAAE,IAAIF,MAAM,CAAA;AAC3B,EAAA,QAAQC,QAAQ;AACd,IAAA,KAAK,QAAQ;MACX,OAAOF,MAAM,GAAG,CAAC,GACblU,IAAI,CAACsU,IAAI,CAACJ,MAAM,GAAGG,MAAM,CAAC,GAAGA,MAAM,GACnCrU,IAAI,CAACuE,KAAK,CAAC2P,MAAM,GAAGG,MAAM,CAAC,GAAGA,MAAM,CAAA;AAC1C,IAAA,KAAK,OAAO;MACV,OAAOrU,IAAI,CAACuU,KAAK,CAACL,MAAM,GAAGG,MAAM,CAAC,GAAGA,MAAM,CAAA;AAC7C,IAAA,KAAK,OAAO;MACV,OAAOrU,IAAI,CAACwU,KAAK,CAACN,MAAM,GAAGG,MAAM,CAAC,GAAGA,MAAM,CAAA;AAC7C,IAAA,KAAK,OAAO;MACV,OAAOrU,IAAI,CAACuE,KAAK,CAAC2P,MAAM,GAAGG,MAAM,CAAC,GAAGA,MAAM,CAAA;AAC7C,IAAA,KAAK,MAAM;MACT,OAAOrU,IAAI,CAACsU,IAAI,CAACJ,MAAM,GAAGG,MAAM,CAAC,GAAGA,MAAM,CAAA;AAC5C,IAAA;AACE,MAAA,MAAM,IAAII,UAAU,CAAE,CAAiBL,eAAAA,EAAAA,QAAS,kBAAiB,CAAC,CAAA;AACtE,GAAA;AACF,CAAA;;AAEA;;AAEO,SAASxF,UAAUA,CAACjV,IAAI,EAAE;AAC/B,EAAA,OAAOA,IAAI,GAAG,CAAC,KAAK,CAAC,KAAKA,IAAI,GAAG,GAAG,KAAK,CAAC,IAAIA,IAAI,GAAG,GAAG,KAAK,CAAC,CAAC,CAAA;AACjE,CAAA;AAEO,SAASqW,UAAUA,CAACrW,IAAI,EAAE;AAC/B,EAAA,OAAOiV,UAAU,CAACjV,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAA;AACrC,CAAA;AAEO,SAAS4X,WAAWA,CAAC5X,IAAI,EAAEC,KAAK,EAAE;EACvC,MAAM8a,QAAQ,GAAGlB,QAAQ,CAAC5Z,KAAK,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;IAC1C+a,OAAO,GAAGhb,IAAI,GAAG,CAACC,KAAK,GAAG8a,QAAQ,IAAI,EAAE,CAAA;EAE1C,IAAIA,QAAQ,KAAK,CAAC,EAAE;AAClB,IAAA,OAAO9F,UAAU,CAAC+F,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,CAAA;AACtC,GAAC,MAAM;AACL,IAAA,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAACD,QAAQ,GAAG,CAAC,CAAC,CAAA;AACzE,GAAA;AACF,CAAA;;AAEA;AACO,SAAStU,YAAYA,CAACkQ,GAAG,EAAE;AAChC,EAAA,IAAIjC,CAAC,GAAGrR,IAAI,CAACsR,GAAG,CACdgC,GAAG,CAAC3W,IAAI,EACR2W,GAAG,CAAC1W,KAAK,GAAG,CAAC,EACb0W,GAAG,CAACzW,GAAG,EACPyW,GAAG,CAAClW,IAAI,EACRkW,GAAG,CAACjW,MAAM,EACViW,GAAG,CAAC/V,MAAM,EACV+V,GAAG,CAACjQ,WACN,CAAC,CAAA;;AAED;EACA,IAAIiQ,GAAG,CAAC3W,IAAI,GAAG,GAAG,IAAI2W,GAAG,CAAC3W,IAAI,IAAI,CAAC,EAAE;AACnC0U,IAAAA,CAAC,GAAG,IAAIrR,IAAI,CAACqR,CAAC,CAAC,CAAA;AACf;AACA;AACA;AACAA,IAAAA,CAAC,CAACE,cAAc,CAAC+B,GAAG,CAAC3W,IAAI,EAAE2W,GAAG,CAAC1W,KAAK,GAAG,CAAC,EAAE0W,GAAG,CAACzW,GAAG,CAAC,CAAA;AACpD,GAAA;AACA,EAAA,OAAO,CAACwU,CAAC,CAAA;AACX,CAAA;;AAEA;AACA,SAASuG,eAAeA,CAACjb,IAAI,EAAE4V,kBAAkB,EAAEH,WAAW,EAAE;AAC9D,EAAA,MAAMyF,KAAK,GAAG3F,iBAAiB,CAACd,SAAS,CAACzU,IAAI,EAAE,CAAC,EAAE4V,kBAAkB,CAAC,EAAEH,WAAW,CAAC,CAAA;AACpF,EAAA,OAAO,CAACyF,KAAK,GAAGtF,kBAAkB,GAAG,CAAC,CAAA;AACxC,CAAA;AAEO,SAASG,eAAeA,CAACD,QAAQ,EAAEF,kBAAkB,GAAG,CAAC,EAAEH,WAAW,GAAG,CAAC,EAAE;EACjF,MAAM0F,UAAU,GAAGF,eAAe,CAACnF,QAAQ,EAAEF,kBAAkB,EAAEH,WAAW,CAAC,CAAA;EAC7E,MAAM2F,cAAc,GAAGH,eAAe,CAACnF,QAAQ,GAAG,CAAC,EAAEF,kBAAkB,EAAEH,WAAW,CAAC,CAAA;EACrF,OAAO,CAACY,UAAU,CAACP,QAAQ,CAAC,GAAGqF,UAAU,GAAGC,cAAc,IAAI,CAAC,CAAA;AACjE,CAAA;AAEO,SAASC,cAAcA,CAACrb,IAAI,EAAE;EACnC,IAAIA,IAAI,GAAG,EAAE,EAAE;AACb,IAAA,OAAOA,IAAI,CAAA;AACb,GAAC,MAAM,OAAOA,IAAI,GAAGkN,QAAQ,CAAC6G,kBAAkB,GAAG,IAAI,GAAG/T,IAAI,GAAG,IAAI,GAAGA,IAAI,CAAA;AAC9E,CAAA;;AAEA;;AAEO,SAASoD,aAAaA,CAAChB,EAAE,EAAEkZ,YAAY,EAAEnY,MAAM,EAAED,QAAQ,GAAG,IAAI,EAAE;AACvE,EAAA,MAAMiB,IAAI,GAAG,IAAId,IAAI,CAACjB,EAAE,CAAC;AACvB4I,IAAAA,QAAQ,GAAG;AACT/J,MAAAA,SAAS,EAAE,KAAK;AAChBjB,MAAAA,IAAI,EAAE,SAAS;AACfC,MAAAA,KAAK,EAAE,SAAS;AAChBC,MAAAA,GAAG,EAAE,SAAS;AACdO,MAAAA,IAAI,EAAE,SAAS;AACfC,MAAAA,MAAM,EAAE,SAAA;KACT,CAAA;AAEH,EAAA,IAAIwC,QAAQ,EAAE;IACZ8H,QAAQ,CAAC9H,QAAQ,GAAGA,QAAQ,CAAA;AAC9B,GAAA;AAEA,EAAA,MAAMqY,QAAQ,GAAG;AAAEza,IAAAA,YAAY,EAAEwa,YAAY;IAAE,GAAGtQ,QAAAA;GAAU,CAAA;AAE5D,EAAA,MAAM1G,MAAM,GAAG,IAAIvB,IAAI,CAACC,cAAc,CAACG,MAAM,EAAEoY,QAAQ,CAAC,CACrDvW,aAAa,CAACb,IAAI,CAAC,CACnByL,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC9N,IAAI,CAAC+N,WAAW,EAAE,KAAK,cAAc,CAAC,CAAA;AACvD,EAAA,OAAOxL,MAAM,GAAGA,MAAM,CAACc,KAAK,GAAG,IAAI,CAAA;AACrC,CAAA;;AAEA;AACO,SAAS0L,YAAYA,CAAC0K,UAAU,EAAEC,YAAY,EAAE;AACrD,EAAA,IAAIC,OAAO,GAAGnW,QAAQ,CAACiW,UAAU,EAAE,EAAE,CAAC,CAAA;;AAEtC;AACA,EAAA,IAAIG,MAAM,CAACxV,KAAK,CAACuV,OAAO,CAAC,EAAE;AACzBA,IAAAA,OAAO,GAAG,CAAC,CAAA;AACb,GAAA;EAEA,MAAME,MAAM,GAAGrW,QAAQ,CAACkW,YAAY,EAAE,EAAE,CAAC,IAAI,CAAC;AAC5CI,IAAAA,YAAY,GAAGH,OAAO,GAAG,CAAC,IAAI5Q,MAAM,CAACgR,EAAE,CAACJ,OAAO,EAAE,CAAC,CAAC,CAAC,GAAG,CAACE,MAAM,GAAGA,MAAM,CAAA;AACzE,EAAA,OAAOF,OAAO,GAAG,EAAE,GAAGG,YAAY,CAAA;AACpC,CAAA;;AAEA;;AAEO,SAASE,QAAQA,CAAC3W,KAAK,EAAE;AAC9B,EAAA,MAAM4W,YAAY,GAAGL,MAAM,CAACvW,KAAK,CAAC,CAAA;EAClC,IAAI,OAAOA,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,EAAE,IAAI,CAACuW,MAAM,CAACM,QAAQ,CAACD,YAAY,CAAC,EAC9E,MAAM,IAAItc,oBAAoB,CAAE,CAAA,mBAAA,EAAqB0F,KAAM,CAAA,CAAC,CAAC,CAAA;AAC/D,EAAA,OAAO4W,YAAY,CAAA;AACrB,CAAA;AAEO,SAASE,eAAeA,CAACvF,GAAG,EAAEwF,UAAU,EAAE;EAC/C,MAAMC,UAAU,GAAG,EAAE,CAAA;AACrB,EAAA,KAAK,MAAMC,CAAC,IAAI1F,GAAG,EAAE;AACnB,IAAA,IAAI0C,cAAc,CAAC1C,GAAG,EAAE0F,CAAC,CAAC,EAAE;AAC1B,MAAA,MAAM5C,CAAC,GAAG9C,GAAG,CAAC0F,CAAC,CAAC,CAAA;AAChB,MAAA,IAAI5C,CAAC,KAAK5V,SAAS,IAAI4V,CAAC,KAAK,IAAI,EAAE,SAAA;MACnC2C,UAAU,CAACD,UAAU,CAACE,CAAC,CAAC,CAAC,GAAGN,QAAQ,CAACtC,CAAC,CAAC,CAAA;AACzC,KAAA;AACF,GAAA;AACA,EAAA,OAAO2C,UAAU,CAAA;AACnB,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS9Z,YAAYA,CAACE,MAAM,EAAED,MAAM,EAAE;AAC3C,EAAA,MAAM+Z,KAAK,GAAGjW,IAAI,CAACuU,KAAK,CAACvU,IAAI,CAACC,GAAG,CAAC9D,MAAM,GAAG,EAAE,CAAC,CAAC;AAC7CqJ,IAAAA,OAAO,GAAGxF,IAAI,CAACuU,KAAK,CAACvU,IAAI,CAACC,GAAG,CAAC9D,MAAM,GAAG,EAAE,CAAC,CAAC;AAC3C+Z,IAAAA,IAAI,GAAG/Z,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAA;AAEhC,EAAA,QAAQD,MAAM;AACZ,IAAA,KAAK,OAAO;AACV,MAAA,OAAQ,GAAEga,IAAK,CAAA,EAAElR,QAAQ,CAACiR,KAAK,EAAE,CAAC,CAAE,CAAA,CAAA,EAAGjR,QAAQ,CAACQ,OAAO,EAAE,CAAC,CAAE,CAAC,CAAA,CAAA;AAC/D,IAAA,KAAK,QAAQ;AACX,MAAA,OAAQ,CAAE0Q,EAAAA,IAAK,CAAED,EAAAA,KAAM,GAAEzQ,OAAO,GAAG,CAAC,GAAI,CAAGA,CAAAA,EAAAA,OAAQ,CAAC,CAAA,GAAG,EAAG,CAAC,CAAA,CAAA;AAC7D,IAAA,KAAK,QAAQ;AACX,MAAA,OAAQ,GAAE0Q,IAAK,CAAA,EAAElR,QAAQ,CAACiR,KAAK,EAAE,CAAC,CAAE,CAAA,EAAEjR,QAAQ,CAACQ,OAAO,EAAE,CAAC,CAAE,CAAC,CAAA,CAAA;AAC9D,IAAA;AACE,MAAA,MAAM,IAAIiP,UAAU,CAAE,CAAevY,aAAAA,EAAAA,MAAO,sCAAqC,CAAC,CAAA;AACtF,GAAA;AACF,CAAA;AAEO,SAASyT,UAAUA,CAACW,GAAG,EAAE;AAC9B,EAAA,OAAOuC,IAAI,CAACvC,GAAG,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAA;AAC/D;;AClUA;AACA;AACA;;AAEO,MAAM6F,UAAU,GAAG,CACxB,SAAS,EACT,UAAU,EACV,OAAO,EACP,OAAO,EACP,KAAK,EACL,MAAM,EACN,MAAM,EACN,QAAQ,EACR,WAAW,EACX,SAAS,EACT,UAAU,EACV,UAAU,CACX,CAAA;AAEM,MAAMC,WAAW,GAAG,CACzB,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN,CAAA;AAEM,MAAMC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;AAEjF,SAAS3N,MAAMA,CAAC5J,MAAM,EAAE;AAC7B,EAAA,QAAQA,MAAM;AACZ,IAAA,KAAK,QAAQ;MACX,OAAO,CAAC,GAAGuX,YAAY,CAAC,CAAA;AAC1B,IAAA,KAAK,OAAO;MACV,OAAO,CAAC,GAAGD,WAAW,CAAC,CAAA;AACzB,IAAA,KAAK,MAAM;MACT,OAAO,CAAC,GAAGD,UAAU,CAAC,CAAA;AACxB,IAAA,KAAK,SAAS;MACZ,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;AACxE,IAAA,KAAK,SAAS;MACZ,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;AACjF,IAAA;AACE,MAAA,OAAO,IAAI,CAAA;AACf,GAAA;AACF,CAAA;AAEO,MAAMG,YAAY,GAAG,CAC1B,QAAQ,EACR,SAAS,EACT,WAAW,EACX,UAAU,EACV,QAAQ,EACR,UAAU,EACV,QAAQ,CACT,CAAA;AAEM,MAAMC,aAAa,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;AAEvE,MAAMC,cAAc,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;AAE1D,SAASxN,QAAQA,CAAClK,MAAM,EAAE;AAC/B,EAAA,QAAQA,MAAM;AACZ,IAAA,KAAK,QAAQ;MACX,OAAO,CAAC,GAAG0X,cAAc,CAAC,CAAA;AAC5B,IAAA,KAAK,OAAO;MACV,OAAO,CAAC,GAAGD,aAAa,CAAC,CAAA;AAC3B,IAAA,KAAK,MAAM;MACT,OAAO,CAAC,GAAGD,YAAY,CAAC,CAAA;AAC1B,IAAA,KAAK,SAAS;AACZ,MAAA,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;AAC5C,IAAA;AACE,MAAA,OAAO,IAAI,CAAA;AACf,GAAA;AACF,CAAA;AAEO,MAAMrN,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;AAE9B,MAAMwN,QAAQ,GAAG,CAAC,eAAe,EAAE,aAAa,CAAC,CAAA;AAEjD,MAAMC,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;AAE9B,MAAMC,UAAU,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;AAE7B,SAASzN,IAAIA,CAACpK,MAAM,EAAE;AAC3B,EAAA,QAAQA,MAAM;AACZ,IAAA,KAAK,QAAQ;MACX,OAAO,CAAC,GAAG6X,UAAU,CAAC,CAAA;AACxB,IAAA,KAAK,OAAO;MACV,OAAO,CAAC,GAAGD,SAAS,CAAC,CAAA;AACvB,IAAA,KAAK,MAAM;MACT,OAAO,CAAC,GAAGD,QAAQ,CAAC,CAAA;AACtB,IAAA;AACE,MAAA,OAAO,IAAI,CAAA;AACf,GAAA;AACF,CAAA;AAEO,SAASG,mBAAmBA,CAACtT,EAAE,EAAE;EACtC,OAAO2F,SAAS,CAAC3F,EAAE,CAAClJ,IAAI,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;AACxC,CAAA;AAEO,SAASyc,kBAAkBA,CAACvT,EAAE,EAAExE,MAAM,EAAE;EAC7C,OAAOkK,QAAQ,CAAClK,MAAM,CAAC,CAACwE,EAAE,CAACtJ,OAAO,GAAG,CAAC,CAAC,CAAA;AACzC,CAAA;AAEO,SAAS8c,gBAAgBA,CAACxT,EAAE,EAAExE,MAAM,EAAE;EAC3C,OAAO4J,MAAM,CAAC5J,MAAM,CAAC,CAACwE,EAAE,CAAC1J,KAAK,GAAG,CAAC,CAAC,CAAA;AACrC,CAAA;AAEO,SAASmd,cAAcA,CAACzT,EAAE,EAAExE,MAAM,EAAE;AACzC,EAAA,OAAOoK,IAAI,CAACpK,MAAM,CAAC,CAACwE,EAAE,CAAC3J,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;AAC1C,CAAA;AAEO,SAASqd,kBAAkBA,CAAC5d,IAAI,EAAE+M,KAAK,EAAEE,OAAO,GAAG,QAAQ,EAAE4Q,MAAM,GAAG,KAAK,EAAE;AAClF,EAAA,MAAMC,KAAK,GAAG;AACZC,IAAAA,KAAK,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;AACtBC,IAAAA,QAAQ,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;AAC7B1O,IAAAA,MAAM,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC;AACxB2O,IAAAA,KAAK,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;AACtBC,IAAAA,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;AAC5BrB,IAAAA,KAAK,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;AACtBzQ,IAAAA,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;AAC3B+R,IAAAA,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAA;GAC3B,CAAA;AAED,EAAA,MAAMC,QAAQ,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAChV,OAAO,CAACpJ,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA;AAErE,EAAA,IAAIiN,OAAO,KAAK,MAAM,IAAImR,QAAQ,EAAE;AAClC,IAAA,MAAMC,KAAK,GAAGre,IAAI,KAAK,MAAM,CAAA;AAC7B,IAAA,QAAQ+M,KAAK;AACX,MAAA,KAAK,CAAC;AACJ,QAAA,OAAOsR,KAAK,GAAG,UAAU,GAAI,CAAOP,KAAAA,EAAAA,KAAK,CAAC9d,IAAI,CAAC,CAAC,CAAC,CAAE,CAAC,CAAA,CAAA;AACtD,MAAA,KAAK,CAAC,CAAC;AACL,QAAA,OAAOqe,KAAK,GAAG,WAAW,GAAI,CAAOP,KAAAA,EAAAA,KAAK,CAAC9d,IAAI,CAAC,CAAC,CAAC,CAAE,CAAC,CAAA,CAAA;AACvD,MAAA,KAAK,CAAC;AACJ,QAAA,OAAOqe,KAAK,GAAG,OAAO,GAAI,CAAOP,KAAAA,EAAAA,KAAK,CAAC9d,IAAI,CAAC,CAAC,CAAC,CAAE,CAAC,CAAA,CAAA;AAErD,KAAA;AACF,GAAA;;AAEA,EAAA,MAAMse,QAAQ,GAAGjT,MAAM,CAACgR,EAAE,CAACtP,KAAK,EAAE,CAAC,CAAC,CAAC,IAAIA,KAAK,GAAG,CAAC;AAChDwR,IAAAA,QAAQ,GAAG3X,IAAI,CAACC,GAAG,CAACkG,KAAK,CAAC;IAC1ByR,QAAQ,GAAGD,QAAQ,KAAK,CAAC;AACzBE,IAAAA,QAAQ,GAAGX,KAAK,CAAC9d,IAAI,CAAC;AACtB0e,IAAAA,OAAO,GAAGb,MAAM,GACZW,QAAQ,GACNC,QAAQ,CAAC,CAAC,CAAC,GACXA,QAAQ,CAAC,CAAC,CAAC,IAAIA,QAAQ,CAAC,CAAC,CAAC,GAC5BD,QAAQ,GACRV,KAAK,CAAC9d,IAAI,CAAC,CAAC,CAAC,CAAC,GACdA,IAAI,CAAA;AACV,EAAA,OAAOse,QAAQ,GAAI,CAAEC,EAAAA,QAAS,CAAGG,CAAAA,EAAAA,OAAQ,CAAK,IAAA,CAAA,GAAI,CAAKH,GAAAA,EAAAA,QAAS,CAAGG,CAAAA,EAAAA,OAAQ,CAAC,CAAA,CAAA;AAC9E;;ACjKA,SAASC,eAAeA,CAACC,MAAM,EAAEC,aAAa,EAAE;EAC9C,IAAIze,CAAC,GAAG,EAAE,CAAA;AACV,EAAA,KAAK,MAAM0e,KAAK,IAAIF,MAAM,EAAE;IAC1B,IAAIE,KAAK,CAACC,OAAO,EAAE;MACjB3e,CAAC,IAAI0e,KAAK,CAACE,GAAG,CAAA;AAChB,KAAC,MAAM;AACL5e,MAAAA,CAAC,IAAIye,aAAa,CAACC,KAAK,CAACE,GAAG,CAAC,CAAA;AAC/B,KAAA;AACF,GAAA;AACA,EAAA,OAAO5e,CAAC,CAAA;AACV,CAAA;AAEA,MAAM6e,sBAAsB,GAAG;EAC7BC,CAAC,EAAEC,UAAkB;EACrBC,EAAE,EAAED,QAAgB;EACpBE,GAAG,EAAEF,SAAiB;EACtBG,IAAI,EAAEH,SAAiB;EACvB1K,CAAC,EAAE0K,WAAmB;EACtBI,EAAE,EAAEJ,iBAAyB;EAC7BK,GAAG,EAAEL,sBAA8B;EACnCM,IAAI,EAAEN,qBAA6B;EACnCO,CAAC,EAAEP,cAAsB;EACzBQ,EAAE,EAAER,oBAA4B;EAChCS,GAAG,EAAET,yBAAiC;EACtCU,IAAI,EAAEV,wBAAgC;EACtCnV,CAAC,EAAEmV,cAAsB;EACzBW,EAAE,EAAEX,YAAoB;EACxBY,GAAG,EAAEZ,aAAqB;EAC1Ba,IAAI,EAAEb,aAAqB;EAC3Bc,CAAC,EAAEd,2BAAmC;EACtCe,EAAE,EAAEf,yBAAiC;EACrCgB,GAAG,EAAEhB,0BAAkC;EACvCiB,IAAI,EAAEjB,0BAAQ/c;AAChB,CAAC,CAAA;;AAED;AACA;AACA;;AAEe,MAAMie,SAAS,CAAC;EAC7B,OAAOpa,MAAMA,CAACvC,MAAM,EAAEd,IAAI,GAAG,EAAE,EAAE;AAC/B,IAAA,OAAO,IAAIyd,SAAS,CAAC3c,MAAM,EAAEd,IAAI,CAAC,CAAA;AACpC,GAAA;EAEA,OAAO0d,WAAWA,CAACC,GAAG,EAAE;AACtB;AACA;;IAEA,IAAIC,OAAO,GAAG,IAAI;AAChBC,MAAAA,WAAW,GAAG,EAAE;AAChBC,MAAAA,SAAS,GAAG,KAAK,CAAA;IACnB,MAAM9B,MAAM,GAAG,EAAE,CAAA;AACjB,IAAA,KAAK,IAAInZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8a,GAAG,CAAC7a,MAAM,EAAED,CAAC,EAAE,EAAE;AACnC,MAAA,MAAMkb,CAAC,GAAGJ,GAAG,CAACK,MAAM,CAACnb,CAAC,CAAC,CAAA;MACvB,IAAIkb,CAAC,KAAK,GAAG,EAAE;AACb;AACA,QAAA,IAAIF,WAAW,CAAC/a,MAAM,GAAG,CAAC,IAAIgb,SAAS,EAAE;UACvC9B,MAAM,CAACvU,IAAI,CAAC;YACV0U,OAAO,EAAE2B,SAAS,IAAI,OAAO,CAACG,IAAI,CAACJ,WAAW,CAAC;AAC/CzB,YAAAA,GAAG,EAAEyB,WAAW,KAAK,EAAE,GAAG,GAAG,GAAGA,WAAAA;AAClC,WAAC,CAAC,CAAA;AACJ,SAAA;AACAD,QAAAA,OAAO,GAAG,IAAI,CAAA;AACdC,QAAAA,WAAW,GAAG,EAAE,CAAA;QAChBC,SAAS,GAAG,CAACA,SAAS,CAAA;OACvB,MAAM,IAAIA,SAAS,EAAE;AACpBD,QAAAA,WAAW,IAAIE,CAAC,CAAA;AAClB,OAAC,MAAM,IAAIA,CAAC,KAAKH,OAAO,EAAE;AACxBC,QAAAA,WAAW,IAAIE,CAAC,CAAA;AAClB,OAAC,MAAM;AACL,QAAA,IAAIF,WAAW,CAAC/a,MAAM,GAAG,CAAC,EAAE;UAC1BkZ,MAAM,CAACvU,IAAI,CAAC;AAAE0U,YAAAA,OAAO,EAAE,OAAO,CAAC8B,IAAI,CAACJ,WAAW,CAAC;AAAEzB,YAAAA,GAAG,EAAEyB,WAAAA;AAAY,WAAC,CAAC,CAAA;AACvE,SAAA;AACAA,QAAAA,WAAW,GAAGE,CAAC,CAAA;AACfH,QAAAA,OAAO,GAAGG,CAAC,CAAA;AACb,OAAA;AACF,KAAA;AAEA,IAAA,IAAIF,WAAW,CAAC/a,MAAM,GAAG,CAAC,EAAE;MAC1BkZ,MAAM,CAACvU,IAAI,CAAC;QAAE0U,OAAO,EAAE2B,SAAS,IAAI,OAAO,CAACG,IAAI,CAACJ,WAAW,CAAC;AAAEzB,QAAAA,GAAG,EAAEyB,WAAAA;AAAY,OAAC,CAAC,CAAA;AACpF,KAAA;AAEA,IAAA,OAAO7B,MAAM,CAAA;AACf,GAAA;EAEA,OAAOK,sBAAsBA,CAACH,KAAK,EAAE;IACnC,OAAOG,sBAAsB,CAACH,KAAK,CAAC,CAAA;AACtC,GAAA;AAEArf,EAAAA,WAAWA,CAACiE,MAAM,EAAEod,UAAU,EAAE;IAC9B,IAAI,CAACle,IAAI,GAAGke,UAAU,CAAA;IACtB,IAAI,CAACtW,GAAG,GAAG9G,MAAM,CAAA;IACjB,IAAI,CAACqd,SAAS,GAAG,IAAI,CAAA;AACvB,GAAA;AAEAC,EAAAA,uBAAuBA,CAAC9W,EAAE,EAAEtH,IAAI,EAAE;AAChC,IAAA,IAAI,IAAI,CAACme,SAAS,KAAK,IAAI,EAAE;MAC3B,IAAI,CAACA,SAAS,GAAG,IAAI,CAACvW,GAAG,CAAC6E,iBAAiB,EAAE,CAAA;AAC/C,KAAA;IACA,MAAMW,EAAE,GAAG,IAAI,CAAC+Q,SAAS,CAACpR,WAAW,CAACzF,EAAE,EAAE;MAAE,GAAG,IAAI,CAACtH,IAAI;MAAE,GAAGA,IAAAA;AAAK,KAAC,CAAC,CAAA;AACpE,IAAA,OAAOoN,EAAE,CAAClN,MAAM,EAAE,CAAA;AACpB,GAAA;AAEA6M,EAAAA,WAAWA,CAACzF,EAAE,EAAEtH,IAAI,GAAG,EAAE,EAAE;AACzB,IAAA,OAAO,IAAI,CAAC4H,GAAG,CAACmF,WAAW,CAACzF,EAAE,EAAE;MAAE,GAAG,IAAI,CAACtH,IAAI;MAAE,GAAGA,IAAAA;AAAK,KAAC,CAAC,CAAA;AAC5D,GAAA;AAEAqe,EAAAA,cAAcA,CAAC/W,EAAE,EAAEtH,IAAI,EAAE;IACvB,OAAO,IAAI,CAAC+M,WAAW,CAACzF,EAAE,EAAEtH,IAAI,CAAC,CAACE,MAAM,EAAE,CAAA;AAC5C,GAAA;AAEAoe,EAAAA,mBAAmBA,CAAChX,EAAE,EAAEtH,IAAI,EAAE;IAC5B,OAAO,IAAI,CAAC+M,WAAW,CAACzF,EAAE,EAAEtH,IAAI,CAAC,CAAC2C,aAAa,EAAE,CAAA;AACnD,GAAA;AAEA4b,EAAAA,cAAcA,CAACC,QAAQ,EAAExe,IAAI,EAAE;IAC7B,MAAMoN,EAAE,GAAG,IAAI,CAACL,WAAW,CAACyR,QAAQ,CAACC,KAAK,EAAEze,IAAI,CAAC,CAAA;IACjD,OAAOoN,EAAE,CAAC9L,GAAG,CAACod,WAAW,CAACF,QAAQ,CAACC,KAAK,CAAC9U,QAAQ,EAAE,EAAE6U,QAAQ,CAACG,GAAG,CAAChV,QAAQ,EAAE,CAAC,CAAA;AAC/E,GAAA;AAEA/I,EAAAA,eAAeA,CAAC0G,EAAE,EAAEtH,IAAI,EAAE;IACxB,OAAO,IAAI,CAAC+M,WAAW,CAACzF,EAAE,EAAEtH,IAAI,CAAC,CAACY,eAAe,EAAE,CAAA;AACrD,GAAA;EAEAge,GAAGA,CAACrhB,CAAC,EAAEshB,CAAC,GAAG,CAAC,EAAEC,WAAW,GAAGtd,SAAS,EAAE;AACrC;AACA,IAAA,IAAI,IAAI,CAACxB,IAAI,CAACqI,WAAW,EAAE;AACzB,MAAA,OAAOW,QAAQ,CAACzL,CAAC,EAAEshB,CAAC,CAAC,CAAA;AACvB,KAAA;AAEA,IAAA,MAAM7e,IAAI,GAAG;AAAE,MAAA,GAAG,IAAI,CAACA,IAAAA;KAAM,CAAA;IAE7B,IAAI6e,CAAC,GAAG,CAAC,EAAE;MACT7e,IAAI,CAACsI,KAAK,GAAGuW,CAAC,CAAA;AAChB,KAAA;AACA,IAAA,IAAIC,WAAW,EAAE;MACf9e,IAAI,CAAC8e,WAAW,GAAGA,WAAW,CAAA;AAChC,KAAA;AAEA,IAAA,OAAO,IAAI,CAAClX,GAAG,CAAC8F,eAAe,CAAC1N,IAAI,CAAC,CAACE,MAAM,CAAC3C,CAAC,CAAC,CAAA;AACjD,GAAA;AAEAwhB,EAAAA,wBAAwBA,CAACzX,EAAE,EAAEqW,GAAG,EAAE;IAChC,MAAMqB,YAAY,GAAG,IAAI,CAACpX,GAAG,CAACI,WAAW,EAAE,KAAK,IAAI;AAClDiX,MAAAA,oBAAoB,GAAG,IAAI,CAACrX,GAAG,CAACX,cAAc,IAAI,IAAI,CAACW,GAAG,CAACX,cAAc,KAAK,SAAS;AACvF4Q,MAAAA,MAAM,GAAGA,CAAC7X,IAAI,EAAE8M,OAAO,KAAK,IAAI,CAAClF,GAAG,CAACkF,OAAO,CAACxF,EAAE,EAAEtH,IAAI,EAAE8M,OAAO,CAAC;MAC/D7M,YAAY,GAAID,IAAI,IAAK;AACvB,QAAA,IAAIsH,EAAE,CAAC4X,aAAa,IAAI5X,EAAE,CAACnH,MAAM,KAAK,CAAC,IAAIH,IAAI,CAACmf,MAAM,EAAE;AACtD,UAAA,OAAO,GAAG,CAAA;AACZ,SAAA;AAEA,QAAA,OAAO7X,EAAE,CAAChH,OAAO,GAAGgH,EAAE,CAAChE,IAAI,CAACrD,YAAY,CAACqH,EAAE,CAACvH,EAAE,EAAEC,IAAI,CAACE,MAAM,CAAC,GAAG,EAAE,CAAA;OAClE;AACDkf,MAAAA,QAAQ,GAAGA,MACTJ,YAAY,GACR5U,mBAA2B,CAAC9C,EAAE,CAAC,GAC/BuQ,MAAM,CAAC;AAAEzZ,QAAAA,IAAI,EAAE,SAAS;AAAEQ,QAAAA,SAAS,EAAE,KAAA;OAAO,EAAE,WAAW,CAAC;MAChEhB,KAAK,GAAGA,CAACkF,MAAM,EAAE+I,UAAU,KACzBmT,YAAY,GACR5U,gBAAwB,CAAC9C,EAAE,EAAExE,MAAM,CAAC,GACpC+U,MAAM,CAAChM,UAAU,GAAG;AAAEjO,QAAAA,KAAK,EAAEkF,MAAAA;AAAO,OAAC,GAAG;AAAElF,QAAAA,KAAK,EAAEkF,MAAM;AAAEjF,QAAAA,GAAG,EAAE,SAAA;OAAW,EAAE,OAAO,CAAC;MACzFG,OAAO,GAAGA,CAAC8E,MAAM,EAAE+I,UAAU,KAC3BmT,YAAY,GACR5U,kBAA0B,CAAC9C,EAAE,EAAExE,MAAM,CAAC,GACtC+U,MAAM,CACJhM,UAAU,GAAG;AAAE7N,QAAAA,OAAO,EAAE8E,MAAAA;AAAO,OAAC,GAAG;AAAE9E,QAAAA,OAAO,EAAE8E,MAAM;AAAElF,QAAAA,KAAK,EAAE,MAAM;AAAEC,QAAAA,GAAG,EAAE,SAAA;OAAW,EACrF,SACF,CAAC;MACPwhB,UAAU,GAAInD,KAAK,IAAK;AACtB,QAAA,MAAMgC,UAAU,GAAGT,SAAS,CAACpB,sBAAsB,CAACH,KAAK,CAAC,CAAA;AAC1D,QAAA,IAAIgC,UAAU,EAAE;AACd,UAAA,OAAO,IAAI,CAACE,uBAAuB,CAAC9W,EAAE,EAAE4W,UAAU,CAAC,CAAA;AACrD,SAAC,MAAM;AACL,UAAA,OAAOhC,KAAK,CAAA;AACd,SAAA;OACD;AACDxa,MAAAA,GAAG,GAAIoB,MAAM,IACXkc,YAAY,GAAG5U,cAAsB,CAAC9C,EAAE,EAAExE,MAAM,CAAC,GAAG+U,MAAM,CAAC;AAAEnW,QAAAA,GAAG,EAAEoB,MAAAA;OAAQ,EAAE,KAAK,CAAC;MACpFmZ,aAAa,GAAIC,KAAK,IAAK;AACzB;AACA,QAAA,QAAQA,KAAK;AACX;AACA,UAAA,KAAK,GAAG;AACN,YAAA,OAAO,IAAI,CAAC0C,GAAG,CAACtX,EAAE,CAACjD,WAAW,CAAC,CAAA;AACjC,UAAA,KAAK,GAAG,CAAA;AACR;AACA,UAAA,KAAK,KAAK;YACR,OAAO,IAAI,CAACua,GAAG,CAACtX,EAAE,CAACjD,WAAW,EAAE,CAAC,CAAC,CAAA;AACpC;AACA,UAAA,KAAK,GAAG;AACN,YAAA,OAAO,IAAI,CAACua,GAAG,CAACtX,EAAE,CAAC/I,MAAM,CAAC,CAAA;AAC5B,UAAA,KAAK,IAAI;YACP,OAAO,IAAI,CAACqgB,GAAG,CAACtX,EAAE,CAAC/I,MAAM,EAAE,CAAC,CAAC,CAAA;AAC/B;AACA,UAAA,KAAK,IAAI;AACP,YAAA,OAAO,IAAI,CAACqgB,GAAG,CAAC5a,IAAI,CAACuE,KAAK,CAACjB,EAAE,CAACjD,WAAW,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;AACrD,UAAA,KAAK,KAAK;AACR,YAAA,OAAO,IAAI,CAACua,GAAG,CAAC5a,IAAI,CAACuE,KAAK,CAACjB,EAAE,CAACjD,WAAW,GAAG,GAAG,CAAC,CAAC,CAAA;AACnD;AACA,UAAA,KAAK,GAAG;AACN,YAAA,OAAO,IAAI,CAACua,GAAG,CAACtX,EAAE,CAACjJ,MAAM,CAAC,CAAA;AAC5B,UAAA,KAAK,IAAI;YACP,OAAO,IAAI,CAACugB,GAAG,CAACtX,EAAE,CAACjJ,MAAM,EAAE,CAAC,CAAC,CAAA;AAC/B;AACA,UAAA,KAAK,GAAG;AACN,YAAA,OAAO,IAAI,CAACugB,GAAG,CAACtX,EAAE,CAAClJ,IAAI,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAGkJ,EAAE,CAAClJ,IAAI,GAAG,EAAE,CAAC,CAAA;AACzD,UAAA,KAAK,IAAI;YACP,OAAO,IAAI,CAACwgB,GAAG,CAACtX,EAAE,CAAClJ,IAAI,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAGkJ,EAAE,CAAClJ,IAAI,GAAG,EAAE,EAAE,CAAC,CAAC,CAAA;AAC5D,UAAA,KAAK,GAAG;AACN,YAAA,OAAO,IAAI,CAACwgB,GAAG,CAACtX,EAAE,CAAClJ,IAAI,CAAC,CAAA;AAC1B,UAAA,KAAK,IAAI;YACP,OAAO,IAAI,CAACwgB,GAAG,CAACtX,EAAE,CAAClJ,IAAI,EAAE,CAAC,CAAC,CAAA;AAC7B;AACA,UAAA,KAAK,GAAG;AACN;AACA,YAAA,OAAO6B,YAAY,CAAC;AAAEC,cAAAA,MAAM,EAAE,QAAQ;AAAEif,cAAAA,MAAM,EAAE,IAAI,CAACnf,IAAI,CAACmf,MAAAA;AAAO,aAAC,CAAC,CAAA;AACrE,UAAA,KAAK,IAAI;AACP;AACA,YAAA,OAAOlf,YAAY,CAAC;AAAEC,cAAAA,MAAM,EAAE,OAAO;AAAEif,cAAAA,MAAM,EAAE,IAAI,CAACnf,IAAI,CAACmf,MAAAA;AAAO,aAAC,CAAC,CAAA;AACpE,UAAA,KAAK,KAAK;AACR;AACA,YAAA,OAAOlf,YAAY,CAAC;AAAEC,cAAAA,MAAM,EAAE,QAAQ;AAAEif,cAAAA,MAAM,EAAE,IAAI,CAACnf,IAAI,CAACmf,MAAAA;AAAO,aAAC,CAAC,CAAA;AACrE,UAAA,KAAK,MAAM;AACT;YACA,OAAO7X,EAAE,CAAChE,IAAI,CAACxD,UAAU,CAACwH,EAAE,CAACvH,EAAE,EAAE;AAAEG,cAAAA,MAAM,EAAE,OAAO;AAAEY,cAAAA,MAAM,EAAE,IAAI,CAAC8G,GAAG,CAAC9G,MAAAA;AAAO,aAAC,CAAC,CAAA;AAChF,UAAA,KAAK,OAAO;AACV;YACA,OAAOwG,EAAE,CAAChE,IAAI,CAACxD,UAAU,CAACwH,EAAE,CAACvH,EAAE,EAAE;AAAEG,cAAAA,MAAM,EAAE,MAAM;AAAEY,cAAAA,MAAM,EAAE,IAAI,CAAC8G,GAAG,CAAC9G,MAAAA;AAAO,aAAC,CAAC,CAAA;AAC/E;AACA,UAAA,KAAK,GAAG;AACN;YACA,OAAOwG,EAAE,CAACjG,QAAQ,CAAA;AACpB;AACA,UAAA,KAAK,GAAG;YACN,OAAO+d,QAAQ,EAAE,CAAA;AACnB;AACA,UAAA,KAAK,GAAG;YACN,OAAOH,oBAAoB,GAAGpH,MAAM,CAAC;AAAEha,cAAAA,GAAG,EAAE,SAAA;aAAW,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC+gB,GAAG,CAACtX,EAAE,CAACzJ,GAAG,CAAC,CAAA;AACpF,UAAA,KAAK,IAAI;YACP,OAAOohB,oBAAoB,GAAGpH,MAAM,CAAC;AAAEha,cAAAA,GAAG,EAAE,SAAA;AAAU,aAAC,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC+gB,GAAG,CAACtX,EAAE,CAACzJ,GAAG,EAAE,CAAC,CAAC,CAAA;AACvF;AACA,UAAA,KAAK,GAAG;AACN;AACA,YAAA,OAAO,IAAI,CAAC+gB,GAAG,CAACtX,EAAE,CAACtJ,OAAO,CAAC,CAAA;AAC7B,UAAA,KAAK,KAAK;AACR;AACA,YAAA,OAAOA,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;AAC/B,UAAA,KAAK,MAAM;AACT;AACA,YAAA,OAAOA,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;AAC9B,UAAA,KAAK,OAAO;AACV;AACA,YAAA,OAAOA,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;AAChC;AACA,UAAA,KAAK,GAAG;AACN;AACA,YAAA,OAAO,IAAI,CAAC4gB,GAAG,CAACtX,EAAE,CAACtJ,OAAO,CAAC,CAAA;AAC7B,UAAA,KAAK,KAAK;AACR;AACA,YAAA,OAAOA,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;AAChC,UAAA,KAAK,MAAM;AACT;AACA,YAAA,OAAOA,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;AAC/B,UAAA,KAAK,OAAO;AACV;AACA,YAAA,OAAOA,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAA;AACjC;AACA,UAAA,KAAK,GAAG;AACN;YACA,OAAOihB,oBAAoB,GACvBpH,MAAM,CAAC;AAAEja,cAAAA,KAAK,EAAE,SAAS;AAAEC,cAAAA,GAAG,EAAE,SAAA;aAAW,EAAE,OAAO,CAAC,GACrD,IAAI,CAAC+gB,GAAG,CAACtX,EAAE,CAAC1J,KAAK,CAAC,CAAA;AACxB,UAAA,KAAK,IAAI;AACP;YACA,OAAOqhB,oBAAoB,GACvBpH,MAAM,CAAC;AAAEja,cAAAA,KAAK,EAAE,SAAS;AAAEC,cAAAA,GAAG,EAAE,SAAA;AAAU,aAAC,EAAE,OAAO,CAAC,GACrD,IAAI,CAAC+gB,GAAG,CAACtX,EAAE,CAAC1J,KAAK,EAAE,CAAC,CAAC,CAAA;AAC3B,UAAA,KAAK,KAAK;AACR;AACA,YAAA,OAAOA,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;AAC7B,UAAA,KAAK,MAAM;AACT;AACA,YAAA,OAAOA,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;AAC5B,UAAA,KAAK,OAAO;AACV;AACA,YAAA,OAAOA,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;AAC9B;AACA,UAAA,KAAK,GAAG;AACN;YACA,OAAOqhB,oBAAoB,GACvBpH,MAAM,CAAC;AAAEja,cAAAA,KAAK,EAAE,SAAA;aAAW,EAAE,OAAO,CAAC,GACrC,IAAI,CAACghB,GAAG,CAACtX,EAAE,CAAC1J,KAAK,CAAC,CAAA;AACxB,UAAA,KAAK,IAAI;AACP;YACA,OAAOqhB,oBAAoB,GACvBpH,MAAM,CAAC;AAAEja,cAAAA,KAAK,EAAE,SAAA;AAAU,aAAC,EAAE,OAAO,CAAC,GACrC,IAAI,CAACghB,GAAG,CAACtX,EAAE,CAAC1J,KAAK,EAAE,CAAC,CAAC,CAAA;AAC3B,UAAA,KAAK,KAAK;AACR;AACA,YAAA,OAAOA,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;AAC9B,UAAA,KAAK,MAAM;AACT;AACA,YAAA,OAAOA,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;AAC7B,UAAA,KAAK,OAAO;AACV;AACA,YAAA,OAAOA,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAA;AAC/B;AACA,UAAA,KAAK,GAAG;AACN;YACA,OAAOqhB,oBAAoB,GAAGpH,MAAM,CAAC;AAAEla,cAAAA,IAAI,EAAE,SAAA;aAAW,EAAE,MAAM,CAAC,GAAG,IAAI,CAACihB,GAAG,CAACtX,EAAE,CAAC3J,IAAI,CAAC,CAAA;AACvF,UAAA,KAAK,IAAI;AACP;YACA,OAAOshB,oBAAoB,GACvBpH,MAAM,CAAC;AAAEla,cAAAA,IAAI,EAAE,SAAA;aAAW,EAAE,MAAM,CAAC,GACnC,IAAI,CAACihB,GAAG,CAACtX,EAAE,CAAC3J,IAAI,CAACwQ,QAAQ,EAAE,CAACmR,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AAC/C,UAAA,KAAK,MAAM;AACT;YACA,OAAOL,oBAAoB,GACvBpH,MAAM,CAAC;AAAEla,cAAAA,IAAI,EAAE,SAAA;AAAU,aAAC,EAAE,MAAM,CAAC,GACnC,IAAI,CAACihB,GAAG,CAACtX,EAAE,CAAC3J,IAAI,EAAE,CAAC,CAAC,CAAA;AAC1B,UAAA,KAAK,QAAQ;AACX;YACA,OAAOshB,oBAAoB,GACvBpH,MAAM,CAAC;AAAEla,cAAAA,IAAI,EAAE,SAAA;AAAU,aAAC,EAAE,MAAM,CAAC,GACnC,IAAI,CAACihB,GAAG,CAACtX,EAAE,CAAC3J,IAAI,EAAE,CAAC,CAAC,CAAA;AAC1B;AACA,UAAA,KAAK,GAAG;AACN;YACA,OAAO+D,GAAG,CAAC,OAAO,CAAC,CAAA;AACrB,UAAA,KAAK,IAAI;AACP;YACA,OAAOA,GAAG,CAAC,MAAM,CAAC,CAAA;AACpB,UAAA,KAAK,OAAO;YACV,OAAOA,GAAG,CAAC,QAAQ,CAAC,CAAA;AACtB,UAAA,KAAK,IAAI;AACP,YAAA,OAAO,IAAI,CAACkd,GAAG,CAACtX,EAAE,CAACmM,QAAQ,CAACtF,QAAQ,EAAE,CAACmR,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AACtD,UAAA,KAAK,MAAM;YACT,OAAO,IAAI,CAACV,GAAG,CAACtX,EAAE,CAACmM,QAAQ,EAAE,CAAC,CAAC,CAAA;AACjC,UAAA,KAAK,GAAG;AACN,YAAA,OAAO,IAAI,CAACmL,GAAG,CAACtX,EAAE,CAACkM,UAAU,CAAC,CAAA;AAChC,UAAA,KAAK,IAAI;YACP,OAAO,IAAI,CAACoL,GAAG,CAACtX,EAAE,CAACkM,UAAU,EAAE,CAAC,CAAC,CAAA;AACnC,UAAA,KAAK,GAAG;AACN,YAAA,OAAO,IAAI,CAACoL,GAAG,CAACtX,EAAE,CAACmN,eAAe,CAAC,CAAA;AACrC,UAAA,KAAK,IAAI;YACP,OAAO,IAAI,CAACmK,GAAG,CAACtX,EAAE,CAACmN,eAAe,EAAE,CAAC,CAAC,CAAA;AACxC,UAAA,KAAK,IAAI;AACP,YAAA,OAAO,IAAI,CAACmK,GAAG,CAACtX,EAAE,CAACoN,aAAa,CAACvG,QAAQ,EAAE,CAACmR,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AAC3D,UAAA,KAAK,MAAM;YACT,OAAO,IAAI,CAACV,GAAG,CAACtX,EAAE,CAACoN,aAAa,EAAE,CAAC,CAAC,CAAA;AACtC,UAAA,KAAK,GAAG;AACN,YAAA,OAAO,IAAI,CAACkK,GAAG,CAACtX,EAAE,CAACwL,OAAO,CAAC,CAAA;AAC7B,UAAA,KAAK,KAAK;YACR,OAAO,IAAI,CAAC8L,GAAG,CAACtX,EAAE,CAACwL,OAAO,EAAE,CAAC,CAAC,CAAA;AAChC,UAAA,KAAK,GAAG;AACN;AACA,YAAA,OAAO,IAAI,CAAC8L,GAAG,CAACtX,EAAE,CAACiY,OAAO,CAAC,CAAA;AAC7B,UAAA,KAAK,IAAI;AACP;YACA,OAAO,IAAI,CAACX,GAAG,CAACtX,EAAE,CAACiY,OAAO,EAAE,CAAC,CAAC,CAAA;AAChC,UAAA,KAAK,GAAG;AACN,YAAA,OAAO,IAAI,CAACX,GAAG,CAAC5a,IAAI,CAACuE,KAAK,CAACjB,EAAE,CAACvH,EAAE,GAAG,IAAI,CAAC,CAAC,CAAA;AAC3C,UAAA,KAAK,GAAG;AACN,YAAA,OAAO,IAAI,CAAC6e,GAAG,CAACtX,EAAE,CAACvH,EAAE,CAAC,CAAA;AACxB,UAAA;YACE,OAAOsf,UAAU,CAACnD,KAAK,CAAC,CAAA;AAC5B,SAAA;OACD,CAAA;IAEH,OAAOH,eAAe,CAAC0B,SAAS,CAACC,WAAW,CAACC,GAAG,CAAC,EAAE1B,aAAa,CAAC,CAAA;AACnE,GAAA;AAEAuD,EAAAA,wBAAwBA,CAACC,GAAG,EAAE9B,GAAG,EAAE;AACjC,IAAA,MAAM+B,aAAa,GAAG,IAAI,CAAC1f,IAAI,CAAC2f,QAAQ,KAAK,qBAAqB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;IAC3E,MAAMC,YAAY,GAAI1D,KAAK,IAAK;QAC5B,QAAQA,KAAK,CAAC,CAAC,CAAC;AACd,UAAA,KAAK,GAAG;AACN,YAAA,OAAO,cAAc,CAAA;AACvB,UAAA,KAAK,GAAG;AACN,YAAA,OAAO,SAAS,CAAA;AAClB,UAAA,KAAK,GAAG;AACN,YAAA,OAAO,SAAS,CAAA;AAClB,UAAA,KAAK,GAAG;AACN,YAAA,OAAO,OAAO,CAAA;AAChB,UAAA,KAAK,GAAG;AACN,YAAA,OAAO,MAAM,CAAA;AACf,UAAA,KAAK,GAAG;AACN,YAAA,OAAO,OAAO,CAAA;AAChB,UAAA,KAAK,GAAG;AACN,YAAA,OAAO,QAAQ,CAAA;AACjB,UAAA,KAAK,GAAG;AACN,YAAA,OAAO,OAAO,CAAA;AAChB,UAAA;AACE,YAAA,OAAO,IAAI,CAAA;AACf,SAAA;OACD;AACDD,MAAAA,aAAa,GAAGA,CAAC4D,MAAM,EAAEC,IAAI,KAAM5D,KAAK,IAAK;AAC3C,QAAA,MAAM6D,MAAM,GAAGH,YAAY,CAAC1D,KAAK,CAAC,CAAA;AAClC,QAAA,IAAI6D,MAAM,EAAE;AACV,UAAA,MAAMC,eAAe,GACnBF,IAAI,CAACG,kBAAkB,IAAIF,MAAM,KAAKD,IAAI,CAACI,WAAW,GAAGR,aAAa,GAAG,CAAC,CAAA;AAC5E,UAAA,IAAIZ,WAAW,CAAA;AACf,UAAA,IAAI,IAAI,CAAC9e,IAAI,CAAC2f,QAAQ,KAAK,qBAAqB,IAAII,MAAM,KAAKD,IAAI,CAACI,WAAW,EAAE;AAC/EpB,YAAAA,WAAW,GAAG,OAAO,CAAA;WACtB,MAAM,IAAI,IAAI,CAAC9e,IAAI,CAAC2f,QAAQ,KAAK,KAAK,EAAE;AACvCb,YAAAA,WAAW,GAAG,QAAQ,CAAA;AACxB,WAAC,MAAM;AACL;AACAA,YAAAA,WAAW,GAAG,MAAM,CAAA;AACtB,WAAA;AACA,UAAA,OAAO,IAAI,CAACF,GAAG,CAACiB,MAAM,CAACte,GAAG,CAACwe,MAAM,CAAC,GAAGC,eAAe,EAAE9D,KAAK,CAACpZ,MAAM,EAAEgc,WAAW,CAAC,CAAA;AAClF,SAAC,MAAM;AACL,UAAA,OAAO5C,KAAK,CAAA;AACd,SAAA;OACD;AACDiE,MAAAA,MAAM,GAAG1C,SAAS,CAACC,WAAW,CAACC,GAAG,CAAC;AACnCyC,MAAAA,UAAU,GAAGD,MAAM,CAAC1J,MAAM,CACxB,CAAC4J,KAAK,EAAE;QAAElE,OAAO;AAAEC,QAAAA,GAAAA;AAAI,OAAC,KAAMD,OAAO,GAAGkE,KAAK,GAAGA,KAAK,CAACC,MAAM,CAAClE,GAAG,CAAE,EAClE,EACF,CAAC;AACDmE,MAAAA,SAAS,GAAGd,GAAG,CAACe,OAAO,CAAC,GAAGJ,UAAU,CAAC3W,GAAG,CAACmW,YAAY,CAAC,CAACa,MAAM,CAAE5O,CAAC,IAAKA,CAAC,CAAC,CAAC;AACzE6O,MAAAA,YAAY,GAAG;QACbT,kBAAkB,EAAEM,SAAS,GAAG,CAAC;AACjC;AACA;QACAL,WAAW,EAAEzX,MAAM,CAACC,IAAI,CAAC6X,SAAS,CAACI,MAAM,CAAC,CAAC,CAAC,CAAA;OAC7C,CAAA;IACH,OAAO5E,eAAe,CAACoE,MAAM,EAAElE,aAAa,CAACsE,SAAS,EAAEG,YAAY,CAAC,CAAC,CAAA;AACxE,GAAA;AACF;;ACraA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAME,SAAS,GAAG,8EAA8E,CAAA;AAEhG,SAASC,cAAcA,CAAC,GAAGC,OAAO,EAAE;AAClC,EAAA,MAAMC,IAAI,GAAGD,OAAO,CAACrK,MAAM,CAAC,CAACrP,CAAC,EAAEmH,CAAC,KAAKnH,CAAC,GAAGmH,CAAC,CAACyS,MAAM,EAAE,EAAE,CAAC,CAAA;AACvD,EAAA,OAAOxP,MAAM,CAAE,CAAGuP,CAAAA,EAAAA,IAAK,GAAE,CAAC,CAAA;AAC5B,CAAA;AAEA,SAASE,iBAAiBA,CAAC,GAAGC,UAAU,EAAE;AACxC,EAAA,OAAQ1T,CAAC,IACP0T,UAAU,CACPzK,MAAM,CACL,CAAC,CAAC0K,UAAU,EAAEC,UAAU,EAAEC,MAAM,CAAC,EAAEC,EAAE,KAAK;AACxC,IAAA,MAAM,CAAClF,GAAG,EAAE9Y,IAAI,EAAEqT,IAAI,CAAC,GAAG2K,EAAE,CAAC9T,CAAC,EAAE6T,MAAM,CAAC,CAAA;AACvC,IAAA,OAAO,CAAC;AAAE,MAAA,GAAGF,UAAU;MAAE,GAAG/E,GAAAA;AAAI,KAAC,EAAE9Y,IAAI,IAAI8d,UAAU,EAAEzK,IAAI,CAAC,CAAA;AAC9D,GAAC,EACD,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,CACd,CAAC,CACA2I,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AAClB,CAAA;AAEA,SAASiC,KAAKA,CAAC/jB,CAAC,EAAE,GAAGgkB,QAAQ,EAAE;EAC7B,IAAIhkB,CAAC,IAAI,IAAI,EAAE;AACb,IAAA,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;AACrB,GAAA;EAEA,KAAK,MAAM,CAAC+T,KAAK,EAAEkQ,SAAS,CAAC,IAAID,QAAQ,EAAE;AACzC,IAAA,MAAMhU,CAAC,GAAG+D,KAAK,CAACrP,IAAI,CAAC1E,CAAC,CAAC,CAAA;AACvB,IAAA,IAAIgQ,CAAC,EAAE;MACL,OAAOiU,SAAS,CAACjU,CAAC,CAAC,CAAA;AACrB,KAAA;AACF,GAAA;AACA,EAAA,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;AACrB,CAAA;AAEA,SAASkU,WAAWA,CAAC,GAAGhZ,IAAI,EAAE;AAC5B,EAAA,OAAO,CAAC8F,KAAK,EAAE6S,MAAM,KAAK;IACxB,MAAMM,GAAG,GAAG,EAAE,CAAA;AACd,IAAA,IAAI9e,CAAC,CAAA;AAEL,IAAA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6F,IAAI,CAAC5F,MAAM,EAAED,CAAC,EAAE,EAAE;AAChC8e,MAAAA,GAAG,CAACjZ,IAAI,CAAC7F,CAAC,CAAC,CAAC,GAAG+U,YAAY,CAACpJ,KAAK,CAAC6S,MAAM,GAAGxe,CAAC,CAAC,CAAC,CAAA;AAChD,KAAA;IACA,OAAO,CAAC8e,GAAG,EAAE,IAAI,EAAEN,MAAM,GAAGxe,CAAC,CAAC,CAAA;GAC/B,CAAA;AACH,CAAA;;AAEA;AACA,MAAM+e,WAAW,GAAG,oCAAoC,CAAA;AACxD,MAAMC,eAAe,GAAI,CAAA,GAAA,EAAKD,WAAW,CAACZ,MAAO,CAAUJ,QAAAA,EAAAA,SAAS,CAACI,MAAO,CAAS,QAAA,CAAA,CAAA;AACrF,MAAMc,gBAAgB,GAAG,qDAAqD,CAAA;AAC9E,MAAMC,YAAY,GAAGvQ,MAAM,CAAE,CAAA,EAAEsQ,gBAAgB,CAACd,MAAO,CAAA,EAAEa,eAAgB,CAAA,CAAC,CAAC,CAAA;AAC3E,MAAMG,qBAAqB,GAAGxQ,MAAM,CAAE,UAASuQ,YAAY,CAACf,MAAO,CAAA,EAAA,CAAG,CAAC,CAAA;AACvE,MAAMiB,WAAW,GAAG,6CAA6C,CAAA;AACjE,MAAMC,YAAY,GAAG,6BAA6B,CAAA;AAClD,MAAMC,eAAe,GAAG,kBAAkB,CAAA;AAC1C,MAAMC,kBAAkB,GAAGV,WAAW,CAAC,UAAU,EAAE,YAAY,EAAE,SAAS,CAAC,CAAA;AAC3E,MAAMW,qBAAqB,GAAGX,WAAW,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;AAC5D,MAAMY,WAAW,GAAG,uBAAuB,CAAC;AAC5C,MAAMC,YAAY,GAAG/Q,MAAM,CACxB,CAAA,EAAEsQ,gBAAgB,CAACd,MAAO,CAAOY,KAAAA,EAAAA,WAAW,CAACZ,MAAO,CAAA,EAAA,EAAIJ,SAAS,CAACI,MAAO,KAC5E,CAAC,CAAA;AACD,MAAMwB,qBAAqB,GAAGhR,MAAM,CAAE,OAAM+Q,YAAY,CAACvB,MAAO,CAAA,EAAA,CAAG,CAAC,CAAA;AAEpE,SAASyB,GAAGA,CAACjU,KAAK,EAAExL,GAAG,EAAE0f,QAAQ,EAAE;AACjC,EAAA,MAAMlV,CAAC,GAAGgB,KAAK,CAACxL,GAAG,CAAC,CAAA;EACpB,OAAOC,WAAW,CAACuK,CAAC,CAAC,GAAGkV,QAAQ,GAAG9K,YAAY,CAACpK,CAAC,CAAC,CAAA;AACpD,CAAA;AAEA,SAASmV,aAAaA,CAACnU,KAAK,EAAE6S,MAAM,EAAE;AACpC,EAAA,MAAMuB,IAAI,GAAG;AACXjlB,IAAAA,IAAI,EAAE8kB,GAAG,CAACjU,KAAK,EAAE6S,MAAM,CAAC;IACxBzjB,KAAK,EAAE6kB,GAAG,CAACjU,KAAK,EAAE6S,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;IAChCxjB,GAAG,EAAE4kB,GAAG,CAACjU,KAAK,EAAE6S,MAAM,GAAG,CAAC,EAAE,CAAC,CAAA;GAC9B,CAAA;EAED,OAAO,CAACuB,IAAI,EAAE,IAAI,EAAEvB,MAAM,GAAG,CAAC,CAAC,CAAA;AACjC,CAAA;AAEA,SAASwB,cAAcA,CAACrU,KAAK,EAAE6S,MAAM,EAAE;AACrC,EAAA,MAAMuB,IAAI,GAAG;IACX3I,KAAK,EAAEwI,GAAG,CAACjU,KAAK,EAAE6S,MAAM,EAAE,CAAC,CAAC;IAC5B7X,OAAO,EAAEiZ,GAAG,CAACjU,KAAK,EAAE6S,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;IAClC9F,OAAO,EAAEkH,GAAG,CAACjU,KAAK,EAAE6S,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;IAClCyB,YAAY,EAAE9K,WAAW,CAACxJ,KAAK,CAAC6S,MAAM,GAAG,CAAC,CAAC,CAAA;GAC5C,CAAA;EAED,OAAO,CAACuB,IAAI,EAAE,IAAI,EAAEvB,MAAM,GAAG,CAAC,CAAC,CAAA;AACjC,CAAA;AAEA,SAAS0B,gBAAgBA,CAACvU,KAAK,EAAE6S,MAAM,EAAE;AACvC,EAAA,MAAM2B,KAAK,GAAG,CAACxU,KAAK,CAAC6S,MAAM,CAAC,IAAI,CAAC7S,KAAK,CAAC6S,MAAM,GAAG,CAAC,CAAC;AAChD4B,IAAAA,UAAU,GAAGxU,YAAY,CAACD,KAAK,CAAC6S,MAAM,GAAG,CAAC,CAAC,EAAE7S,KAAK,CAAC6S,MAAM,GAAG,CAAC,CAAC,CAAC;IAC/D/d,IAAI,GAAG0f,KAAK,GAAG,IAAI,GAAG5U,eAAe,CAAC3N,QAAQ,CAACwiB,UAAU,CAAC,CAAA;EAC5D,OAAO,CAAC,EAAE,EAAE3f,IAAI,EAAE+d,MAAM,GAAG,CAAC,CAAC,CAAA;AAC/B,CAAA;AAEA,SAAS6B,eAAeA,CAAC1U,KAAK,EAAE6S,MAAM,EAAE;AACtC,EAAA,MAAM/d,IAAI,GAAGkL,KAAK,CAAC6S,MAAM,CAAC,GAAGje,QAAQ,CAACC,MAAM,CAACmL,KAAK,CAAC6S,MAAM,CAAC,CAAC,GAAG,IAAI,CAAA;EAClE,OAAO,CAAC,EAAE,EAAE/d,IAAI,EAAE+d,MAAM,GAAG,CAAC,CAAC,CAAA;AAC/B,CAAA;;AAEA;;AAEA,MAAM8B,WAAW,GAAG3R,MAAM,CAAE,MAAKsQ,gBAAgB,CAACd,MAAO,CAAA,CAAA,CAAE,CAAC,CAAA;;AAE5D;;AAEA,MAAMoC,WAAW,GACf,8PAA8P,CAAA;AAEhQ,SAASC,kBAAkBA,CAAC7U,KAAK,EAAE;EACjC,MAAM,CAAChR,CAAC,EAAE8lB,OAAO,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAEC,eAAe,CAAC,GAC3FrV,KAAK,CAAA;AAEP,EAAA,MAAMsV,iBAAiB,GAAGtmB,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAA;EACtC,MAAMumB,eAAe,GAAGH,SAAS,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,CAAA;EAEzD,MAAMI,WAAW,GAAGA,CAACpF,GAAG,EAAEqF,KAAK,GAAG,KAAK,KACrCrF,GAAG,KAAKpd,SAAS,KAAKyiB,KAAK,IAAKrF,GAAG,IAAIkF,iBAAkB,CAAC,GAAG,CAAClF,GAAG,GAAGA,GAAG,CAAA;AAEzE,EAAA,OAAO,CACL;AACEzD,IAAAA,KAAK,EAAE6I,WAAW,CAAClM,aAAa,CAACwL,OAAO,CAAC,CAAC;AAC1C5W,IAAAA,MAAM,EAAEsX,WAAW,CAAClM,aAAa,CAACyL,QAAQ,CAAC,CAAC;AAC5ClI,IAAAA,KAAK,EAAE2I,WAAW,CAAClM,aAAa,CAAC0L,OAAO,CAAC,CAAC;AAC1ClI,IAAAA,IAAI,EAAE0I,WAAW,CAAClM,aAAa,CAAC2L,MAAM,CAAC,CAAC;AACxCxJ,IAAAA,KAAK,EAAE+J,WAAW,CAAClM,aAAa,CAAC4L,OAAO,CAAC,CAAC;AAC1Cla,IAAAA,OAAO,EAAEwa,WAAW,CAAClM,aAAa,CAAC6L,SAAS,CAAC,CAAC;IAC9CpI,OAAO,EAAEyI,WAAW,CAAClM,aAAa,CAAC8L,SAAS,CAAC,EAAEA,SAAS,KAAK,IAAI,CAAC;IAClEd,YAAY,EAAEkB,WAAW,CAAChM,WAAW,CAAC6L,eAAe,CAAC,EAAEE,eAAe,CAAA;AACzE,GAAC,CACF,CAAA;AACH,CAAA;;AAEA;AACA;AACA;AACA,MAAMG,UAAU,GAAG;AACjBC,EAAAA,GAAG,EAAE,CAAC;AACNC,EAAAA,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;AACZC,EAAAA,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;AACZC,EAAAA,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;AACZC,EAAAA,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;AACZC,EAAAA,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;AACZC,EAAAA,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;AACZC,EAAAA,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;EACZC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAA;AACZ,CAAC,CAAA;AAED,SAASC,WAAWA,CAACC,UAAU,EAAEvB,OAAO,EAAEC,QAAQ,EAAEE,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAE;AACzF,EAAA,MAAMkB,MAAM,GAAG;AACbnnB,IAAAA,IAAI,EAAE2lB,OAAO,CAACxgB,MAAM,KAAK,CAAC,GAAGkW,cAAc,CAACpB,YAAY,CAAC0L,OAAO,CAAC,CAAC,GAAG1L,YAAY,CAAC0L,OAAO,CAAC;IAC1F1lB,KAAK,EAAEwM,WAAmB,CAAC5D,OAAO,CAAC+c,QAAQ,CAAC,GAAG,CAAC;AAChD1lB,IAAAA,GAAG,EAAE+Z,YAAY,CAAC6L,MAAM,CAAC;AACzBrlB,IAAAA,IAAI,EAAEwZ,YAAY,CAAC8L,OAAO,CAAC;IAC3BrlB,MAAM,EAAEuZ,YAAY,CAAC+L,SAAS,CAAA;GAC/B,CAAA;EAED,IAAIC,SAAS,EAAEkB,MAAM,CAACvmB,MAAM,GAAGqZ,YAAY,CAACgM,SAAS,CAAC,CAAA;AACtD,EAAA,IAAIiB,UAAU,EAAE;AACdC,IAAAA,MAAM,CAAC9mB,OAAO,GACZ6mB,UAAU,CAAC/hB,MAAM,GAAG,CAAC,GACjBsH,YAAoB,CAAC5D,OAAO,CAACqe,UAAU,CAAC,GAAG,CAAC,GAC5Cza,aAAqB,CAAC5D,OAAO,CAACqe,UAAU,CAAC,GAAG,CAAC,CAAA;AACrD,GAAA;AAEA,EAAA,OAAOC,MAAM,CAAA;AACf,CAAA;;AAEA;AACA,MAAMC,OAAO,GACX,iMAAiM,CAAA;AAEnM,SAASC,cAAcA,CAACxW,KAAK,EAAE;EAC7B,MAAM,GAEFqW,UAAU,EACVpB,MAAM,EACNF,QAAQ,EACRD,OAAO,EACPI,OAAO,EACPC,SAAS,EACTC,SAAS,EACTqB,SAAS,EACTC,SAAS,EACT/L,UAAU,EACVC,YAAY,CACb,GAAG5K,KAAK;AACTsW,IAAAA,MAAM,GAAGF,WAAW,CAACC,UAAU,EAAEvB,OAAO,EAAEC,QAAQ,EAAEE,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAEC,SAAS,CAAC,CAAA;AAE5F,EAAA,IAAIzjB,MAAM,CAAA;AACV,EAAA,IAAI8kB,SAAS,EAAE;AACb9kB,IAAAA,MAAM,GAAG+jB,UAAU,CAACe,SAAS,CAAC,CAAA;GAC/B,MAAM,IAAIC,SAAS,EAAE;AACpB/kB,IAAAA,MAAM,GAAG,CAAC,CAAA;AACZ,GAAC,MAAM;AACLA,IAAAA,MAAM,GAAGsO,YAAY,CAAC0K,UAAU,EAAEC,YAAY,CAAC,CAAA;AACjD,GAAA;EAEA,OAAO,CAAC0L,MAAM,EAAE,IAAI1W,eAAe,CAACjO,MAAM,CAAC,CAAC,CAAA;AAC9C,CAAA;AAEA,SAASglB,iBAAiBA,CAAC3nB,CAAC,EAAE;AAC5B;AACA,EAAA,OAAOA,CAAC,CACLwE,OAAO,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAClCA,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CACxBojB,IAAI,EAAE,CAAA;AACX,CAAA;;AAEA;;AAEA,MAAMC,OAAO,GACT,4HAA4H;AAC9HC,EAAAA,MAAM,GACJ,wJAAwJ;AAC1JC,EAAAA,KAAK,GACH,2HAA2H,CAAA;AAE/H,SAASC,mBAAmBA,CAAChX,KAAK,EAAE;AAClC,EAAA,MAAM,GAAGqW,UAAU,EAAEpB,MAAM,EAAEF,QAAQ,EAAED,OAAO,EAAEI,OAAO,EAAEC,SAAS,EAAEC,SAAS,CAAC,GAAGpV,KAAK;AACpFsW,IAAAA,MAAM,GAAGF,WAAW,CAACC,UAAU,EAAEvB,OAAO,EAAEC,QAAQ,EAAEE,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAEC,SAAS,CAAC,CAAA;AAC5F,EAAA,OAAO,CAACkB,MAAM,EAAE1W,eAAe,CAACC,WAAW,CAAC,CAAA;AAC9C,CAAA;AAEA,SAASoX,YAAYA,CAACjX,KAAK,EAAE;AAC3B,EAAA,MAAM,GAAGqW,UAAU,EAAEtB,QAAQ,EAAEE,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAEN,OAAO,CAAC,GAAG9U,KAAK;AACpFsW,IAAAA,MAAM,GAAGF,WAAW,CAACC,UAAU,EAAEvB,OAAO,EAAEC,QAAQ,EAAEE,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAEC,SAAS,CAAC,CAAA;AAC5F,EAAA,OAAO,CAACkB,MAAM,EAAE1W,eAAe,CAACC,WAAW,CAAC,CAAA;AAC9C,CAAA;AAEA,MAAMqX,4BAA4B,GAAG7E,cAAc,CAACoB,WAAW,EAAED,qBAAqB,CAAC,CAAA;AACvF,MAAM2D,6BAA6B,GAAG9E,cAAc,CAACqB,YAAY,EAAEF,qBAAqB,CAAC,CAAA;AACzF,MAAM4D,gCAAgC,GAAG/E,cAAc,CAACsB,eAAe,EAAEH,qBAAqB,CAAC,CAAA;AAC/F,MAAM6D,oBAAoB,GAAGhF,cAAc,CAACkB,YAAY,CAAC,CAAA;AAEzD,MAAM+D,0BAA0B,GAAG7E,iBAAiB,CAClD0B,aAAa,EACbE,cAAc,EACdE,gBAAgB,EAChBG,eACF,CAAC,CAAA;AACD,MAAM6C,2BAA2B,GAAG9E,iBAAiB,CACnDmB,kBAAkB,EAClBS,cAAc,EACdE,gBAAgB,EAChBG,eACF,CAAC,CAAA;AACD,MAAM8C,4BAA4B,GAAG/E,iBAAiB,CACpDoB,qBAAqB,EACrBQ,cAAc,EACdE,gBAAgB,EAChBG,eACF,CAAC,CAAA;AACD,MAAM+C,uBAAuB,GAAGhF,iBAAiB,CAC/C4B,cAAc,EACdE,gBAAgB,EAChBG,eACF,CAAC,CAAA;;AAED;AACA;AACA;;AAEO,SAASgD,YAAYA,CAAC1oB,CAAC,EAAE;EAC9B,OAAO+jB,KAAK,CACV/jB,CAAC,EACD,CAACkoB,4BAA4B,EAAEI,0BAA0B,CAAC,EAC1D,CAACH,6BAA6B,EAAEI,2BAA2B,CAAC,EAC5D,CAACH,gCAAgC,EAAEI,4BAA4B,CAAC,EAChE,CAACH,oBAAoB,EAAEI,uBAAuB,CAChD,CAAC,CAAA;AACH,CAAA;AAEO,SAASE,gBAAgBA,CAAC3oB,CAAC,EAAE;AAClC,EAAA,OAAO+jB,KAAK,CAAC4D,iBAAiB,CAAC3nB,CAAC,CAAC,EAAE,CAACunB,OAAO,EAAEC,cAAc,CAAC,CAAC,CAAA;AAC/D,CAAA;AAEO,SAASoB,aAAaA,CAAC5oB,CAAC,EAAE;EAC/B,OAAO+jB,KAAK,CACV/jB,CAAC,EACD,CAAC6nB,OAAO,EAAEG,mBAAmB,CAAC,EAC9B,CAACF,MAAM,EAAEE,mBAAmB,CAAC,EAC7B,CAACD,KAAK,EAAEE,YAAY,CACtB,CAAC,CAAA;AACH,CAAA;AAEO,SAASY,gBAAgBA,CAAC7oB,CAAC,EAAE;EAClC,OAAO+jB,KAAK,CAAC/jB,CAAC,EAAE,CAAC4lB,WAAW,EAAEC,kBAAkB,CAAC,CAAC,CAAA;AACpD,CAAA;AAEA,MAAMiD,kBAAkB,GAAGrF,iBAAiB,CAAC4B,cAAc,CAAC,CAAA;AAErD,SAAS0D,gBAAgBA,CAAC/oB,CAAC,EAAE;EAClC,OAAO+jB,KAAK,CAAC/jB,CAAC,EAAE,CAAC2lB,WAAW,EAAEmD,kBAAkB,CAAC,CAAC,CAAA;AACpD,CAAA;AAEA,MAAME,4BAA4B,GAAG3F,cAAc,CAACyB,WAAW,EAAEE,qBAAqB,CAAC,CAAA;AACvF,MAAMiE,oBAAoB,GAAG5F,cAAc,CAAC0B,YAAY,CAAC,CAAA;AAEzD,MAAMmE,+BAA+B,GAAGzF,iBAAiB,CACvD4B,cAAc,EACdE,gBAAgB,EAChBG,eACF,CAAC,CAAA;AAEM,SAASyD,QAAQA,CAACnpB,CAAC,EAAE;AAC1B,EAAA,OAAO+jB,KAAK,CACV/jB,CAAC,EACD,CAACgpB,4BAA4B,EAAEV,0BAA0B,CAAC,EAC1D,CAACW,oBAAoB,EAAEC,+BAA+B,CACxD,CAAC,CAAA;AACH;;AC9TA,MAAME,SAAO,GAAG,kBAAkB,CAAA;;AAElC;AACO,MAAMC,cAAc,GAAG;AAC1BxL,IAAAA,KAAK,EAAE;AACLC,MAAAA,IAAI,EAAE,CAAC;MACPrB,KAAK,EAAE,CAAC,GAAG,EAAE;AACbzQ,MAAAA,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE;AACpB+R,MAAAA,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;MACzBuH,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAA;KAClC;AACDxH,IAAAA,IAAI,EAAE;AACJrB,MAAAA,KAAK,EAAE,EAAE;MACTzQ,OAAO,EAAE,EAAE,GAAG,EAAE;AAChB+R,MAAAA,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AACrBuH,MAAAA,YAAY,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAA;KAC9B;AACD7I,IAAAA,KAAK,EAAE;AAAEzQ,MAAAA,OAAO,EAAE,EAAE;MAAE+R,OAAO,EAAE,EAAE,GAAG,EAAE;AAAEuH,MAAAA,YAAY,EAAE,EAAE,GAAG,EAAE,GAAG,IAAA;KAAM;AACtEtZ,IAAAA,OAAO,EAAE;AAAE+R,MAAAA,OAAO,EAAE,EAAE;MAAEuH,YAAY,EAAE,EAAE,GAAG,IAAA;KAAM;AACjDvH,IAAAA,OAAO,EAAE;AAAEuH,MAAAA,YAAY,EAAE,IAAA;AAAK,KAAA;GAC/B;AACDgE,EAAAA,YAAY,GAAG;AACb3L,IAAAA,KAAK,EAAE;AACLC,MAAAA,QAAQ,EAAE,CAAC;AACX1O,MAAAA,MAAM,EAAE,EAAE;AACV2O,MAAAA,KAAK,EAAE,EAAE;AACTC,MAAAA,IAAI,EAAE,GAAG;MACTrB,KAAK,EAAE,GAAG,GAAG,EAAE;AACfzQ,MAAAA,OAAO,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE;AACtB+R,MAAAA,OAAO,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;MAC3BuH,YAAY,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAA;KACpC;AACD1H,IAAAA,QAAQ,EAAE;AACR1O,MAAAA,MAAM,EAAE,CAAC;AACT2O,MAAAA,KAAK,EAAE,EAAE;AACTC,MAAAA,IAAI,EAAE,EAAE;MACRrB,KAAK,EAAE,EAAE,GAAG,EAAE;AACdzQ,MAAAA,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AACrB+R,MAAAA,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;MAC1BuH,YAAY,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAA;KACnC;AACDpW,IAAAA,MAAM,EAAE;AACN2O,MAAAA,KAAK,EAAE,CAAC;AACRC,MAAAA,IAAI,EAAE,EAAE;MACRrB,KAAK,EAAE,EAAE,GAAG,EAAE;AACdzQ,MAAAA,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AACrB+R,MAAAA,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;MAC1BuH,YAAY,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAA;KACnC;IAED,GAAG+D,cAAAA;GACJ;EACDE,kBAAkB,GAAG,QAAQ,GAAG,GAAG;EACnCC,mBAAmB,GAAG,QAAQ,GAAG,IAAI;AACrCC,EAAAA,cAAc,GAAG;AACf9L,IAAAA,KAAK,EAAE;AACLC,MAAAA,QAAQ,EAAE,CAAC;AACX1O,MAAAA,MAAM,EAAE,EAAE;MACV2O,KAAK,EAAE0L,kBAAkB,GAAG,CAAC;AAC7BzL,MAAAA,IAAI,EAAEyL,kBAAkB;MACxB9M,KAAK,EAAE8M,kBAAkB,GAAG,EAAE;AAC9Bvd,MAAAA,OAAO,EAAEud,kBAAkB,GAAG,EAAE,GAAG,EAAE;AACrCxL,MAAAA,OAAO,EAAEwL,kBAAkB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;MAC1CjE,YAAY,EAAEiE,kBAAkB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAA;KACnD;AACD3L,IAAAA,QAAQ,EAAE;AACR1O,MAAAA,MAAM,EAAE,CAAC;MACT2O,KAAK,EAAE0L,kBAAkB,GAAG,EAAE;MAC9BzL,IAAI,EAAEyL,kBAAkB,GAAG,CAAC;AAC5B9M,MAAAA,KAAK,EAAG8M,kBAAkB,GAAG,EAAE,GAAI,CAAC;AACpCvd,MAAAA,OAAO,EAAGud,kBAAkB,GAAG,EAAE,GAAG,EAAE,GAAI,CAAC;MAC3CxL,OAAO,EAAGwL,kBAAkB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAI,CAAC;MAChDjE,YAAY,EAAGiE,kBAAkB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,GAAI,CAAA;KAC5D;AACDra,IAAAA,MAAM,EAAE;MACN2O,KAAK,EAAE2L,mBAAmB,GAAG,CAAC;AAC9B1L,MAAAA,IAAI,EAAE0L,mBAAmB;MACzB/M,KAAK,EAAE+M,mBAAmB,GAAG,EAAE;AAC/Bxd,MAAAA,OAAO,EAAEwd,mBAAmB,GAAG,EAAE,GAAG,EAAE;AACtCzL,MAAAA,OAAO,EAAEyL,mBAAmB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;MAC3ClE,YAAY,EAAEkE,mBAAmB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAA;KACpD;IACD,GAAGH,cAAAA;GACJ,CAAA;;AAEH;AACA,MAAMK,cAAY,GAAG,CACnB,OAAO,EACP,UAAU,EACV,QAAQ,EACR,OAAO,EACP,MAAM,EACN,OAAO,EACP,SAAS,EACT,SAAS,EACT,cAAc,CACf,CAAA;AAED,MAAMC,YAAY,GAAGD,cAAY,CAAC5H,KAAK,CAAC,CAAC,CAAC,CAAC8H,OAAO,EAAE,CAAA;;AAEpD;AACA,SAAS/a,OAAKA,CAACoT,GAAG,EAAEnT,IAAI,EAAE9I,KAAK,GAAG,KAAK,EAAE;AACvC;AACA,EAAA,MAAM6jB,IAAI,GAAG;AACX1G,IAAAA,MAAM,EAAEnd,KAAK,GAAG8I,IAAI,CAACqU,MAAM,GAAG;MAAE,GAAGlB,GAAG,CAACkB,MAAM;AAAE,MAAA,IAAIrU,IAAI,CAACqU,MAAM,IAAI,EAAE,CAAA;KAAG;IACvE/Y,GAAG,EAAE6X,GAAG,CAAC7X,GAAG,CAACyE,KAAK,CAACC,IAAI,CAAC1E,GAAG,CAAC;AAC5B0f,IAAAA,kBAAkB,EAAEhb,IAAI,CAACgb,kBAAkB,IAAI7H,GAAG,CAAC6H,kBAAkB;AACrEC,IAAAA,MAAM,EAAEjb,IAAI,CAACib,MAAM,IAAI9H,GAAG,CAAC8H,MAAAA;GAC5B,CAAA;AACD,EAAA,OAAO,IAAIC,QAAQ,CAACH,IAAI,CAAC,CAAA;AAC3B,CAAA;AAEA,SAASI,gBAAgBA,CAACF,MAAM,EAAEG,IAAI,EAAE;AAAA,EAAA,IAAAC,kBAAA,CAAA;EACtC,IAAIC,GAAG,GAAAD,CAAAA,kBAAA,GAAGD,IAAI,CAAC5E,YAAY,KAAA,IAAA,GAAA6E,kBAAA,GAAI,CAAC,CAAA;EAChC,KAAK,MAAMvqB,IAAI,IAAI+pB,YAAY,CAAC7H,KAAK,CAAC,CAAC,CAAC,EAAE;AACxC,IAAA,IAAIoI,IAAI,CAACtqB,IAAI,CAAC,EAAE;AACdwqB,MAAAA,GAAG,IAAIF,IAAI,CAACtqB,IAAI,CAAC,GAAGmqB,MAAM,CAACnqB,IAAI,CAAC,CAAC,cAAc,CAAC,CAAA;AAClD,KAAA;AACF,GAAA;AACA,EAAA,OAAOwqB,GAAG,CAAA;AACZ,CAAA;;AAEA;AACA,SAASC,eAAeA,CAACN,MAAM,EAAEG,IAAI,EAAE;AACrC;AACA;AACA,EAAA,MAAMrP,MAAM,GAAGoP,gBAAgB,CAACF,MAAM,EAAEG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;AAE1DR,EAAAA,cAAY,CAACY,WAAW,CAAC,CAACC,QAAQ,EAAEnK,OAAO,KAAK;IAC9C,IAAI,CAAC3a,WAAW,CAACykB,IAAI,CAAC9J,OAAO,CAAC,CAAC,EAAE;AAC/B,MAAA,IAAImK,QAAQ,EAAE;AACZ,QAAA,MAAMC,WAAW,GAAGN,IAAI,CAACK,QAAQ,CAAC,GAAG1P,MAAM,CAAA;QAC3C,MAAM4P,IAAI,GAAGV,MAAM,CAAC3J,OAAO,CAAC,CAACmK,QAAQ,CAAC,CAAA;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACA,MAAMG,MAAM,GAAGlkB,IAAI,CAACuE,KAAK,CAACyf,WAAW,GAAGC,IAAI,CAAC,CAAA;AAC7CP,QAAAA,IAAI,CAAC9J,OAAO,CAAC,IAAIsK,MAAM,GAAG7P,MAAM,CAAA;QAChCqP,IAAI,CAACK,QAAQ,CAAC,IAAIG,MAAM,GAAGD,IAAI,GAAG5P,MAAM,CAAA;AAC1C,OAAA;AACA,MAAA,OAAOuF,OAAO,CAAA;AAChB,KAAC,MAAM;AACL,MAAA,OAAOmK,QAAQ,CAAA;AACjB,KAAA;GACD,EAAE,IAAI,CAAC,CAAA;;AAER;AACA;AACAb,EAAAA,cAAY,CAACzQ,MAAM,CAAC,CAACsR,QAAQ,EAAEnK,OAAO,KAAK;IACzC,IAAI,CAAC3a,WAAW,CAACykB,IAAI,CAAC9J,OAAO,CAAC,CAAC,EAAE;AAC/B,MAAA,IAAImK,QAAQ,EAAE;AACZ,QAAA,MAAM9P,QAAQ,GAAGyP,IAAI,CAACK,QAAQ,CAAC,GAAG,CAAC,CAAA;AACnCL,QAAAA,IAAI,CAACK,QAAQ,CAAC,IAAI9P,QAAQ,CAAA;AAC1ByP,QAAAA,IAAI,CAAC9J,OAAO,CAAC,IAAI3F,QAAQ,GAAGsP,MAAM,CAACQ,QAAQ,CAAC,CAACnK,OAAO,CAAC,CAAA;AACvD,OAAA;AACA,MAAA,OAAOA,OAAO,CAAA;AAChB,KAAC,MAAM;AACL,MAAA,OAAOmK,QAAQ,CAAA;AACjB,KAAA;GACD,EAAE,IAAI,CAAC,CAAA;AACV,CAAA;;AAEA;AACA,SAASI,YAAYA,CAACT,IAAI,EAAE;EAC1B,MAAMU,OAAO,GAAG,EAAE,CAAA;AAClB,EAAA,KAAK,MAAM,CAACzjB,GAAG,EAAE5B,KAAK,CAAC,IAAI0F,MAAM,CAAC4f,OAAO,CAACX,IAAI,CAAC,EAAE;IAC/C,IAAI3kB,KAAK,KAAK,CAAC,EAAE;AACfqlB,MAAAA,OAAO,CAACzjB,GAAG,CAAC,GAAG5B,KAAK,CAAA;AACtB,KAAA;AACF,GAAA;AACA,EAAA,OAAOqlB,OAAO,CAAA;AAChB,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,MAAMZ,QAAQ,CAAC;AAC5B;AACF;AACA;EACE3qB,WAAWA,CAACyrB,MAAM,EAAE;IAClB,MAAMC,QAAQ,GAAGD,MAAM,CAAChB,kBAAkB,KAAK,UAAU,IAAI,KAAK,CAAA;AAClE,IAAA,IAAIC,MAAM,GAAGgB,QAAQ,GAAGtB,cAAc,GAAGH,YAAY,CAAA;IAErD,IAAIwB,MAAM,CAACf,MAAM,EAAE;MACjBA,MAAM,GAAGe,MAAM,CAACf,MAAM,CAAA;AACxB,KAAA;;AAEA;AACJ;AACA;AACI,IAAA,IAAI,CAAC5G,MAAM,GAAG2H,MAAM,CAAC3H,MAAM,CAAA;AAC3B;AACJ;AACA;IACI,IAAI,CAAC/Y,GAAG,GAAG0gB,MAAM,CAAC1gB,GAAG,IAAI3B,MAAM,CAAC5C,MAAM,EAAE,CAAA;AACxC;AACJ;AACA;AACI,IAAA,IAAI,CAACikB,kBAAkB,GAAGiB,QAAQ,GAAG,UAAU,GAAG,QAAQ,CAAA;AAC1D;AACJ;AACA;AACI,IAAA,IAAI,CAACC,OAAO,GAAGF,MAAM,CAACE,OAAO,IAAI,IAAI,CAAA;AACrC;AACJ;AACA;IACI,IAAI,CAACjB,MAAM,GAAGA,MAAM,CAAA;AACpB;AACJ;AACA;IACI,IAAI,CAACkB,eAAe,GAAG,IAAI,CAAA;AAC7B,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,OAAOC,UAAUA,CAACve,KAAK,EAAEnK,IAAI,EAAE;IAC7B,OAAOwnB,QAAQ,CAACjc,UAAU,CAAC;AAAEuX,MAAAA,YAAY,EAAE3Y,KAAAA;KAAO,EAAEnK,IAAI,CAAC,CAAA;AAC3D,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOuL,UAAUA,CAAC+I,GAAG,EAAEtU,IAAI,GAAG,EAAE,EAAE;IAChC,IAAIsU,GAAG,IAAI,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;AAC1C,MAAA,MAAM,IAAIjX,oBAAoB,CAC3B,CAAA,4DAAA,EACCiX,GAAG,KAAK,IAAI,GAAG,MAAM,GAAG,OAAOA,GAChC,EACH,CAAC,CAAA;AACH,KAAA;IAEA,OAAO,IAAIkT,QAAQ,CAAC;MAClB7G,MAAM,EAAE9G,eAAe,CAACvF,GAAG,EAAEkT,QAAQ,CAACmB,aAAa,CAAC;AACpD/gB,MAAAA,GAAG,EAAE3B,MAAM,CAACsF,UAAU,CAACvL,IAAI,CAAC;MAC5BsnB,kBAAkB,EAAEtnB,IAAI,CAACsnB,kBAAkB;MAC3CC,MAAM,EAAEvnB,IAAI,CAACunB,MAAAA;AACf,KAAC,CAAC,CAAA;AACJ,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOqB,gBAAgBA,CAACC,YAAY,EAAE;AACpC,IAAA,IAAI7Z,QAAQ,CAAC6Z,YAAY,CAAC,EAAE;AAC1B,MAAA,OAAOrB,QAAQ,CAACkB,UAAU,CAACG,YAAY,CAAC,CAAA;KACzC,MAAM,IAAIrB,QAAQ,CAACsB,UAAU,CAACD,YAAY,CAAC,EAAE;AAC5C,MAAA,OAAOA,YAAY,CAAA;AACrB,KAAC,MAAM,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;AAC3C,MAAA,OAAOrB,QAAQ,CAACjc,UAAU,CAACsd,YAAY,CAAC,CAAA;AAC1C,KAAC,MAAM;MACL,MAAM,IAAIxrB,oBAAoB,CAC3B,CAAA,0BAAA,EAA4BwrB,YAAa,CAAW,SAAA,EAAA,OAAOA,YAAa,CAAA,CAC3E,CAAC,CAAA;AACH,KAAA;AACF,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,OAAOE,OAAOA,CAACC,IAAI,EAAEhpB,IAAI,EAAE;AACzB,IAAA,MAAM,CAACiC,MAAM,CAAC,GAAGokB,gBAAgB,CAAC2C,IAAI,CAAC,CAAA;AACvC,IAAA,IAAI/mB,MAAM,EAAE;AACV,MAAA,OAAOulB,QAAQ,CAACjc,UAAU,CAACtJ,MAAM,EAAEjC,IAAI,CAAC,CAAA;AAC1C,KAAC,MAAM;MACL,OAAOwnB,QAAQ,CAACgB,OAAO,CAAC,YAAY,EAAG,CAAA,WAAA,EAAaQ,IAAK,CAAA,6BAAA,CAA8B,CAAC,CAAA;AAC1F,KAAA;AACF,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,OAAOC,WAAWA,CAACD,IAAI,EAAEhpB,IAAI,EAAE;AAC7B,IAAA,MAAM,CAACiC,MAAM,CAAC,GAAGskB,gBAAgB,CAACyC,IAAI,CAAC,CAAA;AACvC,IAAA,IAAI/mB,MAAM,EAAE;AACV,MAAA,OAAOulB,QAAQ,CAACjc,UAAU,CAACtJ,MAAM,EAAEjC,IAAI,CAAC,CAAA;AAC1C,KAAC,MAAM;MACL,OAAOwnB,QAAQ,CAACgB,OAAO,CAAC,YAAY,EAAG,CAAA,WAAA,EAAaQ,IAAK,CAAA,6BAAA,CAA8B,CAAC,CAAA;AAC1F,KAAA;AACF,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACE,EAAA,OAAOR,OAAOA,CAAC1rB,MAAM,EAAEkV,WAAW,GAAG,IAAI,EAAE;IACzC,IAAI,CAAClV,MAAM,EAAE;AACX,MAAA,MAAM,IAAIO,oBAAoB,CAAC,kDAAkD,CAAC,CAAA;AACpF,KAAA;AAEA,IAAA,MAAMmrB,OAAO,GAAG1rB,MAAM,YAAYiV,OAAO,GAAGjV,MAAM,GAAG,IAAIiV,OAAO,CAACjV,MAAM,EAAEkV,WAAW,CAAC,CAAA;IAErF,IAAInH,QAAQ,CAAC8G,cAAc,EAAE;AAC3B,MAAA,MAAM,IAAI1U,oBAAoB,CAACurB,OAAO,CAAC,CAAA;AACzC,KAAC,MAAM;MACL,OAAO,IAAIhB,QAAQ,CAAC;AAAEgB,QAAAA,OAAAA;AAAQ,OAAC,CAAC,CAAA;AAClC,KAAA;AACF,GAAA;;AAEA;AACF;AACA;EACE,OAAOG,aAAaA,CAACvrB,IAAI,EAAE;AACzB,IAAA,MAAM2c,UAAU,GAAG;AACjBpc,MAAAA,IAAI,EAAE,OAAO;AACbwd,MAAAA,KAAK,EAAE,OAAO;AACdoE,MAAAA,OAAO,EAAE,UAAU;AACnBnE,MAAAA,QAAQ,EAAE,UAAU;AACpBxd,MAAAA,KAAK,EAAE,QAAQ;AACf8O,MAAAA,MAAM,EAAE,QAAQ;AAChBwc,MAAAA,IAAI,EAAE,OAAO;AACb7N,MAAAA,KAAK,EAAE,OAAO;AACdxd,MAAAA,GAAG,EAAE,MAAM;AACXyd,MAAAA,IAAI,EAAE,MAAM;AACZld,MAAAA,IAAI,EAAE,OAAO;AACb6b,MAAAA,KAAK,EAAE,OAAO;AACd5b,MAAAA,MAAM,EAAE,SAAS;AACjBmL,MAAAA,OAAO,EAAE,SAAS;AAClBjL,MAAAA,MAAM,EAAE,SAAS;AACjBgd,MAAAA,OAAO,EAAE,SAAS;AAClBlX,MAAAA,WAAW,EAAE,cAAc;AAC3Bye,MAAAA,YAAY,EAAE,cAAA;KACf,CAAC1lB,IAAI,GAAGA,IAAI,CAACqQ,WAAW,EAAE,GAAGrQ,IAAI,CAAC,CAAA;IAEnC,IAAI,CAAC2c,UAAU,EAAE,MAAM,IAAI5c,gBAAgB,CAACC,IAAI,CAAC,CAAA;AAEjD,IAAA,OAAO2c,UAAU,CAAA;AACnB,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,OAAO+O,UAAUA,CAACjT,CAAC,EAAE;AACnB,IAAA,OAAQA,CAAC,IAAIA,CAAC,CAAC4S,eAAe,IAAK,KAAK,CAAA;AAC1C,GAAA;;AAEA;AACF;AACA;AACA;EACE,IAAI3nB,MAAMA,GAAG;IACX,OAAO,IAAI,CAACR,OAAO,GAAG,IAAI,CAACsH,GAAG,CAAC9G,MAAM,GAAG,IAAI,CAAA;AAC9C,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAIgG,eAAeA,GAAG;IACpB,OAAO,IAAI,CAACxG,OAAO,GAAG,IAAI,CAACsH,GAAG,CAACd,eAAe,GAAG,IAAI,CAAA;AACvD,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEqiB,EAAAA,QAAQA,CAACxL,GAAG,EAAE3d,IAAI,GAAG,EAAE,EAAE;AACvB;AACA,IAAA,MAAMopB,OAAO,GAAG;AACd,MAAA,GAAGppB,IAAI;MACPuI,KAAK,EAAEvI,IAAI,CAACwY,KAAK,KAAK,KAAK,IAAIxY,IAAI,CAACuI,KAAK,KAAK,KAAA;KAC/C,CAAA;IACD,OAAO,IAAI,CAACjI,OAAO,GACfmd,SAAS,CAACpa,MAAM,CAAC,IAAI,CAACuE,GAAG,EAAEwhB,OAAO,CAAC,CAAC5J,wBAAwB,CAAC,IAAI,EAAE7B,GAAG,CAAC,GACvEiJ,SAAO,CAAA;AACb,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEyC,EAAAA,OAAOA,CAACrpB,IAAI,GAAG,EAAE,EAAE;AACjB,IAAA,IAAI,CAAC,IAAI,CAACM,OAAO,EAAE,OAAOsmB,SAAO,CAAA;AAEjC,IAAA,MAAM0C,SAAS,GAAGtpB,IAAI,CAACspB,SAAS,KAAK,KAAK,CAAA;AAE1C,IAAA,MAAM7rB,CAAC,GAAGypB,cAAY,CACnBzd,GAAG,CAAErM,IAAI,IAAK;AACb,MAAA,MAAMgf,GAAG,GAAG,IAAI,CAACuE,MAAM,CAACvjB,IAAI,CAAC,CAAA;MAC7B,IAAI6F,WAAW,CAACmZ,GAAG,CAAC,IAAKA,GAAG,KAAK,CAAC,IAAI,CAACkN,SAAU,EAAE;AACjD,QAAA,OAAO,IAAI,CAAA;AACb,OAAA;AACA,MAAA,OAAO,IAAI,CAAC1hB,GAAG,CACZ8F,eAAe,CAAC;AAAE1D,QAAAA,KAAK,EAAE,MAAM;AAAEuf,QAAAA,WAAW,EAAE,MAAM;AAAE,QAAA,GAAGvpB,IAAI;QAAE5C,IAAI,EAAEA,IAAI,CAACkiB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AAAE,OAAC,CAAC,CACzFpf,MAAM,CAACkc,GAAG,CAAC,CAAA;AAChB,KAAC,CAAC,CACDqE,MAAM,CAAEljB,CAAC,IAAKA,CAAC,CAAC,CAAA;AAEnB,IAAA,OAAO,IAAI,CAACqK,GAAG,CACZgG,aAAa,CAAC;AAAElO,MAAAA,IAAI,EAAE,aAAa;AAAEsK,MAAAA,KAAK,EAAEhK,IAAI,CAACwpB,SAAS,IAAI,QAAQ;MAAE,GAAGxpB,IAAAA;AAAK,KAAC,CAAC,CAClFE,MAAM,CAACzC,CAAC,CAAC,CAAA;AACd,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACEgsB,EAAAA,QAAQA,GAAG;AACT,IAAA,IAAI,CAAC,IAAI,CAACnpB,OAAO,EAAE,OAAO,EAAE,CAAA;IAC5B,OAAO;AAAE,MAAA,GAAG,IAAI,CAACqgB,MAAAA;KAAQ,CAAA;AAC3B,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE+I,EAAAA,KAAKA,GAAG;AACN;AACA,IAAA,IAAI,CAAC,IAAI,CAACppB,OAAO,EAAE,OAAO,IAAI,CAAA;IAE9B,IAAI9C,CAAC,GAAG,GAAG,CAAA;AACX,IAAA,IAAI,IAAI,CAAC2d,KAAK,KAAK,CAAC,EAAE3d,CAAC,IAAI,IAAI,CAAC2d,KAAK,GAAG,GAAG,CAAA;IAC3C,IAAI,IAAI,CAACzO,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC0O,QAAQ,KAAK,CAAC,EAAE5d,CAAC,IAAI,IAAI,CAACkP,MAAM,GAAG,IAAI,CAAC0O,QAAQ,GAAG,CAAC,GAAG,GAAG,CAAA;AACxF,IAAA,IAAI,IAAI,CAACC,KAAK,KAAK,CAAC,EAAE7d,CAAC,IAAI,IAAI,CAAC6d,KAAK,GAAG,GAAG,CAAA;AAC3C,IAAA,IAAI,IAAI,CAACC,IAAI,KAAK,CAAC,EAAE9d,CAAC,IAAI,IAAI,CAAC8d,IAAI,GAAG,GAAG,CAAA;IACzC,IAAI,IAAI,CAACrB,KAAK,KAAK,CAAC,IAAI,IAAI,CAACzQ,OAAO,KAAK,CAAC,IAAI,IAAI,CAAC+R,OAAO,KAAK,CAAC,IAAI,IAAI,CAACuH,YAAY,KAAK,CAAC,EACzFtlB,CAAC,IAAI,GAAG,CAAA;AACV,IAAA,IAAI,IAAI,CAACyc,KAAK,KAAK,CAAC,EAAEzc,CAAC,IAAI,IAAI,CAACyc,KAAK,GAAG,GAAG,CAAA;AAC3C,IAAA,IAAI,IAAI,CAACzQ,OAAO,KAAK,CAAC,EAAEhM,CAAC,IAAI,IAAI,CAACgM,OAAO,GAAG,GAAG,CAAA;IAC/C,IAAI,IAAI,CAAC+R,OAAO,KAAK,CAAC,IAAI,IAAI,CAACuH,YAAY,KAAK,CAAC;AAC/C;AACA;AACAtlB,MAAAA,CAAC,IAAIuL,OAAO,CAAC,IAAI,CAACwS,OAAO,GAAG,IAAI,CAACuH,YAAY,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAA;AAChE,IAAA,IAAItlB,CAAC,KAAK,GAAG,EAAEA,CAAC,IAAI,KAAK,CAAA;AACzB,IAAA,OAAOA,CAAC,CAAA;AACV,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEmsB,EAAAA,SAASA,CAAC3pB,IAAI,GAAG,EAAE,EAAE;AACnB,IAAA,IAAI,CAAC,IAAI,CAACM,OAAO,EAAE,OAAO,IAAI,CAAA;AAE9B,IAAA,MAAMspB,MAAM,GAAG,IAAI,CAACC,QAAQ,EAAE,CAAA;IAC9B,IAAID,MAAM,GAAG,CAAC,IAAIA,MAAM,IAAI,QAAQ,EAAE,OAAO,IAAI,CAAA;AAEjD5pB,IAAAA,IAAI,GAAG;AACL8pB,MAAAA,oBAAoB,EAAE,KAAK;AAC3BC,MAAAA,eAAe,EAAE,KAAK;AACtBC,MAAAA,aAAa,EAAE,KAAK;AACpB9pB,MAAAA,MAAM,EAAE,UAAU;AAClB,MAAA,GAAGF,IAAI;AACPiqB,MAAAA,aAAa,EAAE,KAAA;KAChB,CAAA;AAED,IAAA,MAAMC,QAAQ,GAAG3iB,QAAQ,CAACmhB,UAAU,CAACkB,MAAM,EAAE;AAAEtmB,MAAAA,IAAI,EAAE,KAAA;AAAM,KAAC,CAAC,CAAA;AAC7D,IAAA,OAAO4mB,QAAQ,CAACP,SAAS,CAAC3pB,IAAI,CAAC,CAAA;AACjC,GAAA;;AAEA;AACF;AACA;AACA;AACEmqB,EAAAA,MAAMA,GAAG;AACP,IAAA,OAAO,IAAI,CAACT,KAAK,EAAE,CAAA;AACrB,GAAA;;AAEA;AACF;AACA;AACA;AACEvb,EAAAA,QAAQA,GAAG;AACT,IAAA,OAAO,IAAI,CAACub,KAAK,EAAE,CAAA;AACrB,GAAA;;AAEA;AACF;AACA;AACA;AACE,EAAA,CAACU,MAAM,CAACC,GAAG,CAAC,4BAA4B,CAAC,CAAI,GAAA;IAC3C,IAAI,IAAI,CAAC/pB,OAAO,EAAE;MAChB,OAAQ,CAAA,mBAAA,EAAqBsE,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC8b,MAAM,CAAE,CAAG,EAAA,CAAA,CAAA;AAC9D,KAAC,MAAM;AACL,MAAA,OAAQ,CAA8B,4BAAA,EAAA,IAAI,CAAC2J,aAAc,CAAG,EAAA,CAAA,CAAA;AAC9D,KAAA;AACF,GAAA;;AAEA;AACF;AACA;AACA;AACET,EAAAA,QAAQA,GAAG;AACT,IAAA,IAAI,CAAC,IAAI,CAACvpB,OAAO,EAAE,OAAOuD,GAAG,CAAA;IAE7B,OAAO4jB,gBAAgB,CAAC,IAAI,CAACF,MAAM,EAAE,IAAI,CAAC5G,MAAM,CAAC,CAAA;AACnD,GAAA;;AAEA;AACF;AACA;AACA;AACE4J,EAAAA,OAAOA,GAAG;AACR,IAAA,OAAO,IAAI,CAACV,QAAQ,EAAE,CAAA;AACxB,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACEtgB,IAAIA,CAACihB,QAAQ,EAAE;AACb,IAAA,IAAI,CAAC,IAAI,CAAClqB,OAAO,EAAE,OAAO,IAAI,CAAA;AAE9B,IAAA,MAAMmf,GAAG,GAAG+H,QAAQ,CAACoB,gBAAgB,CAAC4B,QAAQ,CAAC;MAC7C1F,MAAM,GAAG,EAAE,CAAA;AAEb,IAAA,KAAK,MAAM/N,CAAC,IAAImQ,cAAY,EAAE;AAC5B,MAAA,IAAIlQ,cAAc,CAACyI,GAAG,CAACkB,MAAM,EAAE5J,CAAC,CAAC,IAAIC,cAAc,CAAC,IAAI,CAAC2J,MAAM,EAAE5J,CAAC,CAAC,EAAE;AACnE+N,QAAAA,MAAM,CAAC/N,CAAC,CAAC,GAAG0I,GAAG,CAACle,GAAG,CAACwV,CAAC,CAAC,GAAG,IAAI,CAACxV,GAAG,CAACwV,CAAC,CAAC,CAAA;AACtC,OAAA;AACF,KAAA;IAEA,OAAO1K,OAAK,CAAC,IAAI,EAAE;AAAEsU,MAAAA,MAAM,EAAEmE,MAAAA;KAAQ,EAAE,IAAI,CAAC,CAAA;AAC9C,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE2F,KAAKA,CAACD,QAAQ,EAAE;AACd,IAAA,IAAI,CAAC,IAAI,CAAClqB,OAAO,EAAE,OAAO,IAAI,CAAA;AAE9B,IAAA,MAAMmf,GAAG,GAAG+H,QAAQ,CAACoB,gBAAgB,CAAC4B,QAAQ,CAAC,CAAA;IAC/C,OAAO,IAAI,CAACjhB,IAAI,CAACkW,GAAG,CAACiL,MAAM,EAAE,CAAC,CAAA;AAChC,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,QAAQA,CAACC,EAAE,EAAE;AACX,IAAA,IAAI,CAAC,IAAI,CAACtqB,OAAO,EAAE,OAAO,IAAI,CAAA;IAC9B,MAAMwkB,MAAM,GAAG,EAAE,CAAA;IACjB,KAAK,MAAM/N,CAAC,IAAItO,MAAM,CAACC,IAAI,CAAC,IAAI,CAACiY,MAAM,CAAC,EAAE;AACxCmE,MAAAA,MAAM,CAAC/N,CAAC,CAAC,GAAG2C,QAAQ,CAACkR,EAAE,CAAC,IAAI,CAACjK,MAAM,CAAC5J,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAA;AAC7C,KAAA;IACA,OAAO1K,OAAK,CAAC,IAAI,EAAE;AAAEsU,MAAAA,MAAM,EAAEmE,MAAAA;KAAQ,EAAE,IAAI,CAAC,CAAA;AAC9C,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEvjB,GAAGA,CAACnE,IAAI,EAAE;IACR,OAAO,IAAI,CAACoqB,QAAQ,CAACmB,aAAa,CAACvrB,IAAI,CAAC,CAAC,CAAA;AAC3C,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEuE,GAAGA,CAACgf,MAAM,EAAE;AACV,IAAA,IAAI,CAAC,IAAI,CAACrgB,OAAO,EAAE,OAAO,IAAI,CAAA;AAE9B,IAAA,MAAMuqB,KAAK,GAAG;MAAE,GAAG,IAAI,CAAClK,MAAM;AAAE,MAAA,GAAG9G,eAAe,CAAC8G,MAAM,EAAE6G,QAAQ,CAACmB,aAAa,CAAA;KAAG,CAAA;IACpF,OAAOtc,OAAK,CAAC,IAAI,EAAE;AAAEsU,MAAAA,MAAM,EAAEkK,KAAAA;AAAM,KAAC,CAAC,CAAA;AACvC,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACEC,EAAAA,WAAWA,CAAC;IAAEhqB,MAAM;IAAEgG,eAAe;IAAEwgB,kBAAkB;AAAEC,IAAAA,MAAAA;GAAQ,GAAG,EAAE,EAAE;AACxE,IAAA,MAAM3f,GAAG,GAAG,IAAI,CAACA,GAAG,CAACyE,KAAK,CAAC;MAAEvL,MAAM;AAAEgG,MAAAA,eAAAA;AAAgB,KAAC,CAAC,CAAA;AACvD,IAAA,MAAM9G,IAAI,GAAG;MAAE4H,GAAG;MAAE2f,MAAM;AAAED,MAAAA,kBAAAA;KAAoB,CAAA;AAChD,IAAA,OAAOjb,OAAK,CAAC,IAAI,EAAErM,IAAI,CAAC,CAAA;AAC1B,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE+qB,EAAEA,CAAC3tB,IAAI,EAAE;AACP,IAAA,OAAO,IAAI,CAACkD,OAAO,GAAG,IAAI,CAACkgB,OAAO,CAACpjB,IAAI,CAAC,CAACmE,GAAG,CAACnE,IAAI,CAAC,GAAGyG,GAAG,CAAA;AAC1D,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEmnB,EAAAA,SAASA,GAAG;AACV,IAAA,IAAI,CAAC,IAAI,CAAC1qB,OAAO,EAAE,OAAO,IAAI,CAAA;AAC9B,IAAA,MAAMonB,IAAI,GAAG,IAAI,CAAC+B,QAAQ,EAAE,CAAA;AAC5B5B,IAAAA,eAAe,CAAC,IAAI,CAACN,MAAM,EAAEG,IAAI,CAAC,CAAA;IAClC,OAAOrb,OAAK,CAAC,IAAI,EAAE;AAAEsU,MAAAA,MAAM,EAAE+G,IAAAA;KAAM,EAAE,IAAI,CAAC,CAAA;AAC5C,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACEuD,EAAAA,OAAOA,GAAG;AACR,IAAA,IAAI,CAAC,IAAI,CAAC3qB,OAAO,EAAE,OAAO,IAAI,CAAA;AAC9B,IAAA,MAAMonB,IAAI,GAAGS,YAAY,CAAC,IAAI,CAAC6C,SAAS,EAAE,CAACE,UAAU,EAAE,CAACzB,QAAQ,EAAE,CAAC,CAAA;IACnE,OAAOpd,OAAK,CAAC,IAAI,EAAE;AAAEsU,MAAAA,MAAM,EAAE+G,IAAAA;KAAM,EAAE,IAAI,CAAC,CAAA;AAC5C,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACElH,OAAOA,CAAC,GAAGtF,KAAK,EAAE;AAChB,IAAA,IAAI,CAAC,IAAI,CAAC5a,OAAO,EAAE,OAAO,IAAI,CAAA;AAE9B,IAAA,IAAI4a,KAAK,CAACpY,MAAM,KAAK,CAAC,EAAE;AACtB,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AAEAoY,IAAAA,KAAK,GAAGA,KAAK,CAACzR,GAAG,CAAEuQ,CAAC,IAAKwN,QAAQ,CAACmB,aAAa,CAAC3O,CAAC,CAAC,CAAC,CAAA;IAEnD,MAAMmR,KAAK,GAAG,EAAE;MACdC,WAAW,GAAG,EAAE;AAChB1D,MAAAA,IAAI,GAAG,IAAI,CAAC+B,QAAQ,EAAE,CAAA;AACxB,IAAA,IAAI4B,QAAQ,CAAA;AAEZ,IAAA,KAAK,MAAMtU,CAAC,IAAImQ,cAAY,EAAE;MAC5B,IAAIhM,KAAK,CAAC1U,OAAO,CAACuQ,CAAC,CAAC,IAAI,CAAC,EAAE;AACzBsU,QAAAA,QAAQ,GAAGtU,CAAC,CAAA;QAEZ,IAAIuU,GAAG,GAAG,CAAC,CAAA;;AAEX;AACA,QAAA,KAAK,MAAMC,EAAE,IAAIH,WAAW,EAAE;AAC5BE,UAAAA,GAAG,IAAI,IAAI,CAAC/D,MAAM,CAACgE,EAAE,CAAC,CAACxU,CAAC,CAAC,GAAGqU,WAAW,CAACG,EAAE,CAAC,CAAA;AAC3CH,UAAAA,WAAW,CAACG,EAAE,CAAC,GAAG,CAAC,CAAA;AACrB,SAAA;;AAEA;AACA,QAAA,IAAIvc,QAAQ,CAAC0Y,IAAI,CAAC3Q,CAAC,CAAC,CAAC,EAAE;AACrBuU,UAAAA,GAAG,IAAI5D,IAAI,CAAC3Q,CAAC,CAAC,CAAA;AAChB,SAAA;;AAEA;AACA;AACA,QAAA,MAAMlU,CAAC,GAAGmB,IAAI,CAACuU,KAAK,CAAC+S,GAAG,CAAC,CAAA;AACzBH,QAAAA,KAAK,CAACpU,CAAC,CAAC,GAAGlU,CAAC,CAAA;AACZuoB,QAAAA,WAAW,CAACrU,CAAC,CAAC,GAAG,CAACuU,GAAG,GAAG,IAAI,GAAGzoB,CAAC,GAAG,IAAI,IAAI,IAAI,CAAA;;AAE/C;OACD,MAAM,IAAImM,QAAQ,CAAC0Y,IAAI,CAAC3Q,CAAC,CAAC,CAAC,EAAE;AAC5BqU,QAAAA,WAAW,CAACrU,CAAC,CAAC,GAAG2Q,IAAI,CAAC3Q,CAAC,CAAC,CAAA;AAC1B,OAAA;AACF,KAAA;;AAEA;AACA;AACA,IAAA,KAAK,MAAMpS,GAAG,IAAIymB,WAAW,EAAE;AAC7B,MAAA,IAAIA,WAAW,CAACzmB,GAAG,CAAC,KAAK,CAAC,EAAE;QAC1BwmB,KAAK,CAACE,QAAQ,CAAC,IACb1mB,GAAG,KAAK0mB,QAAQ,GAAGD,WAAW,CAACzmB,GAAG,CAAC,GAAGymB,WAAW,CAACzmB,GAAG,CAAC,GAAG,IAAI,CAAC4iB,MAAM,CAAC8D,QAAQ,CAAC,CAAC1mB,GAAG,CAAC,CAAA;AACvF,OAAA;AACF,KAAA;AAEAkjB,IAAAA,eAAe,CAAC,IAAI,CAACN,MAAM,EAAE4D,KAAK,CAAC,CAAA;IACnC,OAAO9e,OAAK,CAAC,IAAI,EAAE;AAAEsU,MAAAA,MAAM,EAAEwK,KAAAA;KAAO,EAAE,IAAI,CAAC,CAAA;AAC7C,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACED,EAAAA,UAAUA,GAAG;AACX,IAAA,IAAI,CAAC,IAAI,CAAC5qB,OAAO,EAAE,OAAO,IAAI,CAAA;AAC9B,IAAA,OAAO,IAAI,CAACkgB,OAAO,CACjB,OAAO,EACP,QAAQ,EACR,OAAO,EACP,MAAM,EACN,OAAO,EACP,SAAS,EACT,SAAS,EACT,cACF,CAAC,CAAA;AACH,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACEkK,EAAAA,MAAMA,GAAG;AACP,IAAA,IAAI,CAAC,IAAI,CAACpqB,OAAO,EAAE,OAAO,IAAI,CAAA;IAC9B,MAAMkrB,OAAO,GAAG,EAAE,CAAA;IAClB,KAAK,MAAMzU,CAAC,IAAItO,MAAM,CAACC,IAAI,CAAC,IAAI,CAACiY,MAAM,CAAC,EAAE;MACxC6K,OAAO,CAACzU,CAAC,CAAC,GAAG,IAAI,CAAC4J,MAAM,CAAC5J,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC4J,MAAM,CAAC5J,CAAC,CAAC,CAAA;AACzD,KAAA;IACA,OAAO1K,OAAK,CAAC,IAAI,EAAE;AAAEsU,MAAAA,MAAM,EAAE6K,OAAAA;KAAS,EAAE,IAAI,CAAC,CAAA;AAC/C,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACEC,EAAAA,WAAWA,GAAG;AACZ,IAAA,IAAI,CAAC,IAAI,CAACnrB,OAAO,EAAE,OAAO,IAAI,CAAA;AAC9B,IAAA,MAAMonB,IAAI,GAAGS,YAAY,CAAC,IAAI,CAACxH,MAAM,CAAC,CAAA;IACtC,OAAOtU,OAAK,CAAC,IAAI,EAAE;AAAEsU,MAAAA,MAAM,EAAE+G,IAAAA;KAAM,EAAE,IAAI,CAAC,CAAA;AAC5C,GAAA;;AAEA;AACF;AACA;AACA;EACE,IAAIvM,KAAKA,GAAG;AACV,IAAA,OAAO,IAAI,CAAC7a,OAAO,GAAG,IAAI,CAACqgB,MAAM,CAACxF,KAAK,IAAI,CAAC,GAAGtX,GAAG,CAAA;AACpD,GAAA;;AAEA;AACF;AACA;AACA;EACE,IAAIuX,QAAQA,GAAG;AACb,IAAA,OAAO,IAAI,CAAC9a,OAAO,GAAG,IAAI,CAACqgB,MAAM,CAACvF,QAAQ,IAAI,CAAC,GAAGvX,GAAG,CAAA;AACvD,GAAA;;AAEA;AACF;AACA;AACA;EACE,IAAI6I,MAAMA,GAAG;AACX,IAAA,OAAO,IAAI,CAACpM,OAAO,GAAG,IAAI,CAACqgB,MAAM,CAACjU,MAAM,IAAI,CAAC,GAAG7I,GAAG,CAAA;AACrD,GAAA;;AAEA;AACF;AACA;AACA;EACE,IAAIwX,KAAKA,GAAG;AACV,IAAA,OAAO,IAAI,CAAC/a,OAAO,GAAG,IAAI,CAACqgB,MAAM,CAACtF,KAAK,IAAI,CAAC,GAAGxX,GAAG,CAAA;AACpD,GAAA;;AAEA;AACF;AACA;AACA;EACE,IAAIyX,IAAIA,GAAG;AACT,IAAA,OAAO,IAAI,CAAChb,OAAO,GAAG,IAAI,CAACqgB,MAAM,CAACrF,IAAI,IAAI,CAAC,GAAGzX,GAAG,CAAA;AACnD,GAAA;;AAEA;AACF;AACA;AACA;EACE,IAAIoW,KAAKA,GAAG;AACV,IAAA,OAAO,IAAI,CAAC3Z,OAAO,GAAG,IAAI,CAACqgB,MAAM,CAAC1G,KAAK,IAAI,CAAC,GAAGpW,GAAG,CAAA;AACpD,GAAA;;AAEA;AACF;AACA;AACA;EACE,IAAI2F,OAAOA,GAAG;AACZ,IAAA,OAAO,IAAI,CAAClJ,OAAO,GAAG,IAAI,CAACqgB,MAAM,CAACnX,OAAO,IAAI,CAAC,GAAG3F,GAAG,CAAA;AACtD,GAAA;;AAEA;AACF;AACA;AACA;EACE,IAAI0X,OAAOA,GAAG;AACZ,IAAA,OAAO,IAAI,CAACjb,OAAO,GAAG,IAAI,CAACqgB,MAAM,CAACpF,OAAO,IAAI,CAAC,GAAG1X,GAAG,CAAA;AACtD,GAAA;;AAEA;AACF;AACA;AACA;EACE,IAAIif,YAAYA,GAAG;AACjB,IAAA,OAAO,IAAI,CAACxiB,OAAO,GAAG,IAAI,CAACqgB,MAAM,CAACmC,YAAY,IAAI,CAAC,GAAGjf,GAAG,CAAA;AAC3D,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAIvD,OAAOA,GAAG;AACZ,IAAA,OAAO,IAAI,CAACkoB,OAAO,KAAK,IAAI,CAAA;AAC9B,GAAA;;AAEA;AACF;AACA;AACA;EACE,IAAI8B,aAAaA,GAAG;IAClB,OAAO,IAAI,CAAC9B,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC1rB,MAAM,GAAG,IAAI,CAAA;AAClD,GAAA;;AAEA;AACF;AACA;AACA;EACE,IAAI4uB,kBAAkBA,GAAG;IACvB,OAAO,IAAI,CAAClD,OAAO,GAAG,IAAI,CAACA,OAAO,CAACxW,WAAW,GAAG,IAAI,CAAA;AACvD,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE5R,MAAMA,CAAC8N,KAAK,EAAE;IACZ,IAAI,CAAC,IAAI,CAAC5N,OAAO,IAAI,CAAC4N,KAAK,CAAC5N,OAAO,EAAE;AACnC,MAAA,OAAO,KAAK,CAAA;AACd,KAAA;IAEA,IAAI,CAAC,IAAI,CAACsH,GAAG,CAACxH,MAAM,CAAC8N,KAAK,CAACtG,GAAG,CAAC,EAAE;AAC/B,MAAA,OAAO,KAAK,CAAA;AACd,KAAA;AAEA,IAAA,SAAS+jB,EAAEA,CAACC,EAAE,EAAEC,EAAE,EAAE;AAClB;AACA,MAAA,IAAID,EAAE,KAAKpqB,SAAS,IAAIoqB,EAAE,KAAK,CAAC,EAAE,OAAOC,EAAE,KAAKrqB,SAAS,IAAIqqB,EAAE,KAAK,CAAC,CAAA;MACrE,OAAOD,EAAE,KAAKC,EAAE,CAAA;AAClB,KAAA;AAEA,IAAA,KAAK,MAAM7R,CAAC,IAAIkN,cAAY,EAAE;AAC5B,MAAA,IAAI,CAACyE,EAAE,CAAC,IAAI,CAAChL,MAAM,CAAC3G,CAAC,CAAC,EAAE9L,KAAK,CAACyS,MAAM,CAAC3G,CAAC,CAAC,CAAC,EAAE;AACxC,QAAA,OAAO,KAAK,CAAA;AACd,OAAA;AACF,KAAA;AACA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AACF;;ACx+BA,MAAM4M,SAAO,GAAG,kBAAkB,CAAA;;AAElC;AACA,SAASkF,gBAAgBA,CAACrN,KAAK,EAAEE,GAAG,EAAE;AACpC,EAAA,IAAI,CAACF,KAAK,IAAI,CAACA,KAAK,CAACne,OAAO,EAAE;AAC5B,IAAA,OAAOyrB,QAAQ,CAACvD,OAAO,CAAC,0BAA0B,CAAC,CAAA;GACpD,MAAM,IAAI,CAAC7J,GAAG,IAAI,CAACA,GAAG,CAACre,OAAO,EAAE;AAC/B,IAAA,OAAOyrB,QAAQ,CAACvD,OAAO,CAAC,wBAAwB,CAAC,CAAA;AACnD,GAAC,MAAM,IAAI7J,GAAG,GAAGF,KAAK,EAAE;AACtB,IAAA,OAAOsN,QAAQ,CAACvD,OAAO,CACrB,kBAAkB,EACjB,qEAAoE/J,KAAK,CAACiL,KAAK,EAAG,YAAW/K,GAAG,CAAC+K,KAAK,EAAG,EAC5G,CAAC,CAAA;AACH,GAAC,MAAM;AACL,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AACF,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,MAAMqC,QAAQ,CAAC;AAC5B;AACF;AACA;EACElvB,WAAWA,CAACyrB,MAAM,EAAE;AAClB;AACJ;AACA;AACI,IAAA,IAAI,CAAC9qB,CAAC,GAAG8qB,MAAM,CAAC7J,KAAK,CAAA;AACrB;AACJ;AACA;AACI,IAAA,IAAI,CAAC9a,CAAC,GAAG2kB,MAAM,CAAC3J,GAAG,CAAA;AACnB;AACJ;AACA;AACI,IAAA,IAAI,CAAC6J,OAAO,GAAGF,MAAM,CAACE,OAAO,IAAI,IAAI,CAAA;AACrC;AACJ;AACA;IACI,IAAI,CAACwD,eAAe,GAAG,IAAI,CAAA;AAC7B,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACE,EAAA,OAAOxD,OAAOA,CAAC1rB,MAAM,EAAEkV,WAAW,GAAG,IAAI,EAAE;IACzC,IAAI,CAAClV,MAAM,EAAE;AACX,MAAA,MAAM,IAAIO,oBAAoB,CAAC,kDAAkD,CAAC,CAAA;AACpF,KAAA;AAEA,IAAA,MAAMmrB,OAAO,GAAG1rB,MAAM,YAAYiV,OAAO,GAAGjV,MAAM,GAAG,IAAIiV,OAAO,CAACjV,MAAM,EAAEkV,WAAW,CAAC,CAAA;IAErF,IAAInH,QAAQ,CAAC8G,cAAc,EAAE;AAC3B,MAAA,MAAM,IAAI3U,oBAAoB,CAACwrB,OAAO,CAAC,CAAA;AACzC,KAAC,MAAM;MACL,OAAO,IAAIuD,QAAQ,CAAC;AAAEvD,QAAAA,OAAAA;AAAQ,OAAC,CAAC,CAAA;AAClC,KAAA;AACF,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACE,EAAA,OAAOyD,aAAaA,CAACxN,KAAK,EAAEE,GAAG,EAAE;AAC/B,IAAA,MAAMuN,UAAU,GAAGC,gBAAgB,CAAC1N,KAAK,CAAC;AACxC2N,MAAAA,QAAQ,GAAGD,gBAAgB,CAACxN,GAAG,CAAC,CAAA;AAElC,IAAA,MAAM0N,aAAa,GAAGP,gBAAgB,CAACI,UAAU,EAAEE,QAAQ,CAAC,CAAA;IAE5D,IAAIC,aAAa,IAAI,IAAI,EAAE;MACzB,OAAO,IAAIN,QAAQ,CAAC;AAClBtN,QAAAA,KAAK,EAAEyN,UAAU;AACjBvN,QAAAA,GAAG,EAAEyN,QAAAA;AACP,OAAC,CAAC,CAAA;AACJ,KAAC,MAAM;AACL,MAAA,OAAOC,aAAa,CAAA;AACtB,KAAA;AACF,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACE,EAAA,OAAOC,KAAKA,CAAC7N,KAAK,EAAE+L,QAAQ,EAAE;AAC5B,IAAA,MAAM/K,GAAG,GAAG+H,QAAQ,CAACoB,gBAAgB,CAAC4B,QAAQ,CAAC;AAC7CljB,MAAAA,EAAE,GAAG6kB,gBAAgB,CAAC1N,KAAK,CAAC,CAAA;AAC9B,IAAA,OAAOsN,QAAQ,CAACE,aAAa,CAAC3kB,EAAE,EAAEA,EAAE,CAACiC,IAAI,CAACkW,GAAG,CAAC,CAAC,CAAA;AACjD,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACE,EAAA,OAAO8M,MAAMA,CAAC5N,GAAG,EAAE6L,QAAQ,EAAE;AAC3B,IAAA,MAAM/K,GAAG,GAAG+H,QAAQ,CAACoB,gBAAgB,CAAC4B,QAAQ,CAAC;AAC7CljB,MAAAA,EAAE,GAAG6kB,gBAAgB,CAACxN,GAAG,CAAC,CAAA;AAC5B,IAAA,OAAOoN,QAAQ,CAACE,aAAa,CAAC3kB,EAAE,CAACmjB,KAAK,CAAChL,GAAG,CAAC,EAAEnY,EAAE,CAAC,CAAA;AAClD,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,OAAOyhB,OAAOA,CAACC,IAAI,EAAEhpB,IAAI,EAAE;AACzB,IAAA,MAAM,CAACxC,CAAC,EAAEmG,CAAC,CAAC,GAAG,CAACqlB,IAAI,IAAI,EAAE,EAAEvY,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;IACzC,IAAIjT,CAAC,IAAImG,CAAC,EAAE;MACV,IAAI8a,KAAK,EAAE+N,YAAY,CAAA;MACvB,IAAI;QACF/N,KAAK,GAAGlX,QAAQ,CAACwhB,OAAO,CAACvrB,CAAC,EAAEwC,IAAI,CAAC,CAAA;QACjCwsB,YAAY,GAAG/N,KAAK,CAACne,OAAO,CAAA;OAC7B,CAAC,OAAOqD,CAAC,EAAE;AACV6oB,QAAAA,YAAY,GAAG,KAAK,CAAA;AACtB,OAAA;MAEA,IAAI7N,GAAG,EAAE8N,UAAU,CAAA;MACnB,IAAI;QACF9N,GAAG,GAAGpX,QAAQ,CAACwhB,OAAO,CAACplB,CAAC,EAAE3D,IAAI,CAAC,CAAA;QAC/BysB,UAAU,GAAG9N,GAAG,CAACre,OAAO,CAAA;OACzB,CAAC,OAAOqD,CAAC,EAAE;AACV8oB,QAAAA,UAAU,GAAG,KAAK,CAAA;AACpB,OAAA;MAEA,IAAID,YAAY,IAAIC,UAAU,EAAE;AAC9B,QAAA,OAAOV,QAAQ,CAACE,aAAa,CAACxN,KAAK,EAAEE,GAAG,CAAC,CAAA;AAC3C,OAAA;AAEA,MAAA,IAAI6N,YAAY,EAAE;QAChB,MAAM/M,GAAG,GAAG+H,QAAQ,CAACuB,OAAO,CAACplB,CAAC,EAAE3D,IAAI,CAAC,CAAA;QACrC,IAAIyf,GAAG,CAACnf,OAAO,EAAE;AACf,UAAA,OAAOyrB,QAAQ,CAACO,KAAK,CAAC7N,KAAK,EAAEgB,GAAG,CAAC,CAAA;AACnC,SAAA;OACD,MAAM,IAAIgN,UAAU,EAAE;QACrB,MAAMhN,GAAG,GAAG+H,QAAQ,CAACuB,OAAO,CAACvrB,CAAC,EAAEwC,IAAI,CAAC,CAAA;QACrC,IAAIyf,GAAG,CAACnf,OAAO,EAAE;AACf,UAAA,OAAOyrB,QAAQ,CAACQ,MAAM,CAAC5N,GAAG,EAAEc,GAAG,CAAC,CAAA;AAClC,SAAA;AACF,OAAA;AACF,KAAA;IACA,OAAOsM,QAAQ,CAACvD,OAAO,CAAC,YAAY,EAAG,CAAA,WAAA,EAAaQ,IAAK,CAAA,6BAAA,CAA8B,CAAC,CAAA;AAC1F,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,OAAO0D,UAAUA,CAAC7W,CAAC,EAAE;AACnB,IAAA,OAAQA,CAAC,IAAIA,CAAC,CAACmW,eAAe,IAAK,KAAK,CAAA;AAC1C,GAAA;;AAEA;AACF;AACA;AACA;EACE,IAAIvN,KAAKA,GAAG;IACV,OAAO,IAAI,CAACne,OAAO,GAAG,IAAI,CAAC9C,CAAC,GAAG,IAAI,CAAA;AACrC,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAImhB,GAAGA,GAAG;IACR,OAAO,IAAI,CAACre,OAAO,GAAG,IAAI,CAACqD,CAAC,GAAG,IAAI,CAAA;AACrC,GAAA;;AAEA;AACF;AACA;AACA;EACE,IAAIgpB,YAAYA,GAAG;AACjB,IAAA,OAAO,IAAI,CAACrsB,OAAO,GAAI,IAAI,CAACqD,CAAC,GAAG,IAAI,CAACA,CAAC,CAAC8mB,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,IAAI,CAAA;AAChE,GAAA;;AAEA;AACF;AACA;AACA;EACE,IAAInqB,OAAOA,GAAG;AACZ,IAAA,OAAO,IAAI,CAACgqB,aAAa,KAAK,IAAI,CAAA;AACpC,GAAA;;AAEA;AACF;AACA;AACA;EACE,IAAIA,aAAaA,GAAG;IAClB,OAAO,IAAI,CAAC9B,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC1rB,MAAM,GAAG,IAAI,CAAA;AAClD,GAAA;;AAEA;AACF;AACA;AACA;EACE,IAAI4uB,kBAAkBA,GAAG;IACvB,OAAO,IAAI,CAAClD,OAAO,GAAG,IAAI,CAACA,OAAO,CAACxW,WAAW,GAAG,IAAI,CAAA;AACvD,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACElP,EAAAA,MAAMA,CAAC1F,IAAI,GAAG,cAAc,EAAE;AAC5B,IAAA,OAAO,IAAI,CAACkD,OAAO,GAAG,IAAI,CAACssB,UAAU,CAAC,GAAG,CAACxvB,IAAI,CAAC,CAAC,CAACmE,GAAG,CAACnE,IAAI,CAAC,GAAGyG,GAAG,CAAA;AAClE,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEsG,EAAAA,KAAKA,CAAC/M,IAAI,GAAG,cAAc,EAAE4C,IAAI,EAAE;AACjC,IAAA,IAAI,CAAC,IAAI,CAACM,OAAO,EAAE,OAAOuD,GAAG,CAAA;IAC7B,MAAM4a,KAAK,GAAG,IAAI,CAACA,KAAK,CAACoO,OAAO,CAACzvB,IAAI,EAAE4C,IAAI,CAAC,CAAA;AAC5C,IAAA,IAAI2e,GAAG,CAAA;AACP,IAAA,IAAI3e,IAAI,IAAA,IAAA,IAAJA,IAAI,CAAE8sB,cAAc,EAAE;AACxBnO,MAAAA,GAAG,GAAG,IAAI,CAACA,GAAG,CAACmM,WAAW,CAAC;QAAEhqB,MAAM,EAAE2d,KAAK,CAAC3d,MAAAA;AAAO,OAAC,CAAC,CAAA;AACtD,KAAC,MAAM;MACL6d,GAAG,GAAG,IAAI,CAACA,GAAG,CAAA;AAChB,KAAA;IACAA,GAAG,GAAGA,GAAG,CAACkO,OAAO,CAACzvB,IAAI,EAAE4C,IAAI,CAAC,CAAA;AAC7B,IAAA,OAAOgE,IAAI,CAACuE,KAAK,CAACoW,GAAG,CAACoO,IAAI,CAACtO,KAAK,EAAErhB,IAAI,CAAC,CAACmE,GAAG,CAACnE,IAAI,CAAC,CAAC,IAAIuhB,GAAG,CAAC4L,OAAO,EAAE,KAAK,IAAI,CAAC5L,GAAG,CAAC4L,OAAO,EAAE,CAAC,CAAA;AAC7F,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACEyC,OAAOA,CAAC5vB,IAAI,EAAE;AACZ,IAAA,OAAO,IAAI,CAACkD,OAAO,GAAG,IAAI,CAAC2sB,OAAO,EAAE,IAAI,IAAI,CAACtpB,CAAC,CAAC8mB,KAAK,CAAC,CAAC,CAAC,CAACuC,OAAO,CAAC,IAAI,CAACxvB,CAAC,EAAEJ,IAAI,CAAC,GAAG,KAAK,CAAA;AACvF,GAAA;;AAEA;AACF;AACA;AACA;AACE6vB,EAAAA,OAAOA,GAAG;AACR,IAAA,OAAO,IAAI,CAACzvB,CAAC,CAAC+sB,OAAO,EAAE,KAAK,IAAI,CAAC5mB,CAAC,CAAC4mB,OAAO,EAAE,CAAA;AAC9C,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE2C,OAAOA,CAAChD,QAAQ,EAAE;AAChB,IAAA,IAAI,CAAC,IAAI,CAAC5pB,OAAO,EAAE,OAAO,KAAK,CAAA;AAC/B,IAAA,OAAO,IAAI,CAAC9C,CAAC,GAAG0sB,QAAQ,CAAA;AAC1B,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACEiD,QAAQA,CAACjD,QAAQ,EAAE;AACjB,IAAA,IAAI,CAAC,IAAI,CAAC5pB,OAAO,EAAE,OAAO,KAAK,CAAA;AAC/B,IAAA,OAAO,IAAI,CAACqD,CAAC,IAAIumB,QAAQ,CAAA;AAC3B,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACEkD,QAAQA,CAAClD,QAAQ,EAAE;AACjB,IAAA,IAAI,CAAC,IAAI,CAAC5pB,OAAO,EAAE,OAAO,KAAK,CAAA;IAC/B,OAAO,IAAI,CAAC9C,CAAC,IAAI0sB,QAAQ,IAAI,IAAI,CAACvmB,CAAC,GAAGumB,QAAQ,CAAA;AAChD,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACEvoB,EAAAA,GAAGA,CAAC;IAAE8c,KAAK;AAAEE,IAAAA,GAAAA;GAAK,GAAG,EAAE,EAAE;AACvB,IAAA,IAAI,CAAC,IAAI,CAACre,OAAO,EAAE,OAAO,IAAI,CAAA;AAC9B,IAAA,OAAOyrB,QAAQ,CAACE,aAAa,CAACxN,KAAK,IAAI,IAAI,CAACjhB,CAAC,EAAEmhB,GAAG,IAAI,IAAI,CAAChb,CAAC,CAAC,CAAA;AAC/D,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE0pB,OAAOA,CAAC,GAAGC,SAAS,EAAE;AACpB,IAAA,IAAI,CAAC,IAAI,CAAChtB,OAAO,EAAE,OAAO,EAAE,CAAA;AAC5B,IAAA,MAAMitB,MAAM,GAAGD,SAAS,CACnB7jB,GAAG,CAAC0iB,gBAAgB,CAAC,CACrB1L,MAAM,CAAEpO,CAAC,IAAK,IAAI,CAAC+a,QAAQ,CAAC/a,CAAC,CAAC,CAAC,CAC/Bmb,IAAI,CAAC,CAAC1W,CAAC,EAAE2W,CAAC,KAAK3W,CAAC,CAAC+S,QAAQ,EAAE,GAAG4D,CAAC,CAAC5D,QAAQ,EAAE,CAAC;AAC9Cxc,MAAAA,OAAO,GAAG,EAAE,CAAA;IACd,IAAI;AAAE7P,QAAAA,CAAAA;AAAE,OAAC,GAAG,IAAI;AACdqF,MAAAA,CAAC,GAAG,CAAC,CAAA;AAEP,IAAA,OAAOrF,CAAC,GAAG,IAAI,CAACmG,CAAC,EAAE;MACjB,MAAM+pB,KAAK,GAAGH,MAAM,CAAC1qB,CAAC,CAAC,IAAI,IAAI,CAACc,CAAC;AAC/BgT,QAAAA,IAAI,GAAG,CAAC+W,KAAK,GAAG,CAAC,IAAI,CAAC/pB,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG+pB,KAAK,CAAA;MAC1CrgB,OAAO,CAAC5F,IAAI,CAACskB,QAAQ,CAACE,aAAa,CAACzuB,CAAC,EAAEmZ,IAAI,CAAC,CAAC,CAAA;AAC7CnZ,MAAAA,CAAC,GAAGmZ,IAAI,CAAA;AACR9T,MAAAA,CAAC,IAAI,CAAC,CAAA;AACR,KAAA;AAEA,IAAA,OAAOwK,OAAO,CAAA;AAChB,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACEsgB,OAAOA,CAACnD,QAAQ,EAAE;AAChB,IAAA,MAAM/K,GAAG,GAAG+H,QAAQ,CAACoB,gBAAgB,CAAC4B,QAAQ,CAAC,CAAA;AAE/C,IAAA,IAAI,CAAC,IAAI,CAAClqB,OAAO,IAAI,CAACmf,GAAG,CAACnf,OAAO,IAAImf,GAAG,CAACsL,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;AACjE,MAAA,OAAO,EAAE,CAAA;AACX,KAAA;IAEA,IAAI;AAAEvtB,QAAAA,CAAAA;AAAE,OAAC,GAAG,IAAI;AACdowB,MAAAA,GAAG,GAAG,CAAC;MACPjX,IAAI,CAAA;IAEN,MAAMtJ,OAAO,GAAG,EAAE,CAAA;AAClB,IAAA,OAAO7P,CAAC,GAAG,IAAI,CAACmG,CAAC,EAAE;AACjB,MAAA,MAAM+pB,KAAK,GAAG,IAAI,CAACjP,KAAK,CAAClV,IAAI,CAACkW,GAAG,CAACkL,QAAQ,CAAElT,CAAC,IAAKA,CAAC,GAAGmW,GAAG,CAAC,CAAC,CAAA;AAC3DjX,MAAAA,IAAI,GAAG,CAAC+W,KAAK,GAAG,CAAC,IAAI,CAAC/pB,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG+pB,KAAK,CAAA;MACxCrgB,OAAO,CAAC5F,IAAI,CAACskB,QAAQ,CAACE,aAAa,CAACzuB,CAAC,EAAEmZ,IAAI,CAAC,CAAC,CAAA;AAC7CnZ,MAAAA,CAAC,GAAGmZ,IAAI,CAAA;AACRiX,MAAAA,GAAG,IAAI,CAAC,CAAA;AACV,KAAA;AAEA,IAAA,OAAOvgB,OAAO,CAAA;AAChB,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACEwgB,aAAaA,CAACC,aAAa,EAAE;AAC3B,IAAA,IAAI,CAAC,IAAI,CAACxtB,OAAO,EAAE,OAAO,EAAE,CAAA;AAC5B,IAAA,OAAO,IAAI,CAACqtB,OAAO,CAAC,IAAI,CAAC7qB,MAAM,EAAE,GAAGgrB,aAAa,CAAC,CAACxO,KAAK,CAAC,CAAC,EAAEwO,aAAa,CAAC,CAAA;AAC5E,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACEC,QAAQA,CAAC7f,KAAK,EAAE;AACd,IAAA,OAAO,IAAI,CAACvK,CAAC,GAAGuK,KAAK,CAAC1Q,CAAC,IAAI,IAAI,CAACA,CAAC,GAAG0Q,KAAK,CAACvK,CAAC,CAAA;AAC7C,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACEqqB,UAAUA,CAAC9f,KAAK,EAAE;AAChB,IAAA,IAAI,CAAC,IAAI,CAAC5N,OAAO,EAAE,OAAO,KAAK,CAAA;IAC/B,OAAO,CAAC,IAAI,CAACqD,CAAC,KAAK,CAACuK,KAAK,CAAC1Q,CAAC,CAAA;AAC7B,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACEywB,QAAQA,CAAC/f,KAAK,EAAE;AACd,IAAA,IAAI,CAAC,IAAI,CAAC5N,OAAO,EAAE,OAAO,KAAK,CAAA;IAC/B,OAAO,CAAC4N,KAAK,CAACvK,CAAC,KAAK,CAAC,IAAI,CAACnG,CAAC,CAAA;AAC7B,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE0wB,OAAOA,CAAChgB,KAAK,EAAE;AACb,IAAA,IAAI,CAAC,IAAI,CAAC5N,OAAO,EAAE,OAAO,KAAK,CAAA;AAC/B,IAAA,OAAO,IAAI,CAAC9C,CAAC,IAAI0Q,KAAK,CAAC1Q,CAAC,IAAI,IAAI,CAACmG,CAAC,IAAIuK,KAAK,CAACvK,CAAC,CAAA;AAC/C,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACEvD,MAAMA,CAAC8N,KAAK,EAAE;IACZ,IAAI,CAAC,IAAI,CAAC5N,OAAO,IAAI,CAAC4N,KAAK,CAAC5N,OAAO,EAAE;AACnC,MAAA,OAAO,KAAK,CAAA;AACd,KAAA;IAEA,OAAO,IAAI,CAAC9C,CAAC,CAAC4C,MAAM,CAAC8N,KAAK,CAAC1Q,CAAC,CAAC,IAAI,IAAI,CAACmG,CAAC,CAACvD,MAAM,CAAC8N,KAAK,CAACvK,CAAC,CAAC,CAAA;AACzD,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEwqB,YAAYA,CAACjgB,KAAK,EAAE;AAClB,IAAA,IAAI,CAAC,IAAI,CAAC5N,OAAO,EAAE,OAAO,IAAI,CAAA;AAC9B,IAAA,MAAM9C,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG0Q,KAAK,CAAC1Q,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG0Q,KAAK,CAAC1Q,CAAC;AAC3CmG,MAAAA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGuK,KAAK,CAACvK,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGuK,KAAK,CAACvK,CAAC,CAAA;IAEzC,IAAInG,CAAC,IAAImG,CAAC,EAAE;AACV,MAAA,OAAO,IAAI,CAAA;AACb,KAAC,MAAM;AACL,MAAA,OAAOooB,QAAQ,CAACE,aAAa,CAACzuB,CAAC,EAAEmG,CAAC,CAAC,CAAA;AACrC,KAAA;AACF,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACEyqB,KAAKA,CAAClgB,KAAK,EAAE;AACX,IAAA,IAAI,CAAC,IAAI,CAAC5N,OAAO,EAAE,OAAO,IAAI,CAAA;AAC9B,IAAA,MAAM9C,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG0Q,KAAK,CAAC1Q,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG0Q,KAAK,CAAC1Q,CAAC;AAC3CmG,MAAAA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGuK,KAAK,CAACvK,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGuK,KAAK,CAACvK,CAAC,CAAA;AACzC,IAAA,OAAOooB,QAAQ,CAACE,aAAa,CAACzuB,CAAC,EAAEmG,CAAC,CAAC,CAAA;AACrC,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO0qB,KAAKA,CAACC,SAAS,EAAE;AACtB,IAAA,MAAM,CAACjO,KAAK,EAAEkO,KAAK,CAAC,GAAGD,SAAS,CAC7Bd,IAAI,CAAC,CAAC1W,CAAC,EAAE2W,CAAC,KAAK3W,CAAC,CAACtZ,CAAC,GAAGiwB,CAAC,CAACjwB,CAAC,CAAC,CACzBiZ,MAAM,CACL,CAAC,CAAC+X,KAAK,EAAE5Q,OAAO,CAAC,EAAEgF,IAAI,KAAK;MAC1B,IAAI,CAAChF,OAAO,EAAE;AACZ,QAAA,OAAO,CAAC4Q,KAAK,EAAE5L,IAAI,CAAC,CAAA;AACtB,OAAC,MAAM,IAAIhF,OAAO,CAACmQ,QAAQ,CAACnL,IAAI,CAAC,IAAIhF,OAAO,CAACoQ,UAAU,CAACpL,IAAI,CAAC,EAAE;QAC7D,OAAO,CAAC4L,KAAK,EAAE5Q,OAAO,CAACwQ,KAAK,CAACxL,IAAI,CAAC,CAAC,CAAA;AACrC,OAAC,MAAM;QACL,OAAO,CAAC4L,KAAK,CAAClO,MAAM,CAAC,CAAC1C,OAAO,CAAC,CAAC,EAAEgF,IAAI,CAAC,CAAA;AACxC,OAAA;AACF,KAAC,EACD,CAAC,EAAE,EAAE,IAAI,CACX,CAAC,CAAA;AACH,IAAA,IAAI2L,KAAK,EAAE;AACTlO,MAAAA,KAAK,CAAC5Y,IAAI,CAAC8mB,KAAK,CAAC,CAAA;AACnB,KAAA;AACA,IAAA,OAAOlO,KAAK,CAAA;AACd,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,OAAOoO,GAAGA,CAACH,SAAS,EAAE;IACpB,IAAI7P,KAAK,GAAG,IAAI;AACdiQ,MAAAA,YAAY,GAAG,CAAC,CAAA;IAClB,MAAMrhB,OAAO,GAAG,EAAE;AAChBshB,MAAAA,IAAI,GAAGL,SAAS,CAAC7kB,GAAG,CAAE5G,CAAC,IAAK,CAC1B;QAAE+rB,IAAI,EAAE/rB,CAAC,CAACrF,CAAC;AAAEkC,QAAAA,IAAI,EAAE,GAAA;AAAI,OAAC,EACxB;QAAEkvB,IAAI,EAAE/rB,CAAC,CAACc,CAAC;AAAEjE,QAAAA,IAAI,EAAE,GAAA;AAAI,OAAC,CACzB,CAAC;MACFmvB,SAAS,GAAG1Y,KAAK,CAACJ,SAAS,CAACuK,MAAM,CAAC,GAAGqO,IAAI,CAAC;AAC3CrY,MAAAA,GAAG,GAAGuY,SAAS,CAACrB,IAAI,CAAC,CAAC1W,CAAC,EAAE2W,CAAC,KAAK3W,CAAC,CAAC8X,IAAI,GAAGnB,CAAC,CAACmB,IAAI,CAAC,CAAA;AAEjD,IAAA,KAAK,MAAM/rB,CAAC,IAAIyT,GAAG,EAAE;MACnBoY,YAAY,IAAI7rB,CAAC,CAACnD,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;MAEvC,IAAIgvB,YAAY,KAAK,CAAC,EAAE;QACtBjQ,KAAK,GAAG5b,CAAC,CAAC+rB,IAAI,CAAA;AAChB,OAAC,MAAM;QACL,IAAInQ,KAAK,IAAI,CAACA,KAAK,KAAK,CAAC5b,CAAC,CAAC+rB,IAAI,EAAE;AAC/BvhB,UAAAA,OAAO,CAAC5F,IAAI,CAACskB,QAAQ,CAACE,aAAa,CAACxN,KAAK,EAAE5b,CAAC,CAAC+rB,IAAI,CAAC,CAAC,CAAA;AACrD,SAAA;AAEAnQ,QAAAA,KAAK,GAAG,IAAI,CAAA;AACd,OAAA;AACF,KAAA;AAEA,IAAA,OAAOsN,QAAQ,CAACsC,KAAK,CAAChhB,OAAO,CAAC,CAAA;AAChC,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACEyhB,UAAUA,CAAC,GAAGR,SAAS,EAAE;AACvB,IAAA,OAAOvC,QAAQ,CAAC0C,GAAG,CAAC,CAAC,IAAI,CAAC,CAACnO,MAAM,CAACgO,SAAS,CAAC,CAAC,CAC1C7kB,GAAG,CAAE5G,CAAC,IAAK,IAAI,CAACsrB,YAAY,CAACtrB,CAAC,CAAC,CAAC,CAChC4d,MAAM,CAAE5d,CAAC,IAAKA,CAAC,IAAI,CAACA,CAAC,CAACoqB,OAAO,EAAE,CAAC,CAAA;AACrC,GAAA;;AAEA;AACF;AACA;AACA;AACE9e,EAAAA,QAAQA,GAAG;AACT,IAAA,IAAI,CAAC,IAAI,CAAC7N,OAAO,EAAE,OAAOsmB,SAAO,CAAA;AACjC,IAAA,OAAQ,IAAG,IAAI,CAACppB,CAAC,CAACksB,KAAK,EAAG,CAAK,GAAA,EAAA,IAAI,CAAC/lB,CAAC,CAAC+lB,KAAK,EAAG,CAAE,CAAA,CAAA,CAAA;AAClD,GAAA;;AAEA;AACF;AACA;AACA;AACE,EAAA,CAACU,MAAM,CAACC,GAAG,CAAC,4BAA4B,CAAC,CAAI,GAAA;IAC3C,IAAI,IAAI,CAAC/pB,OAAO,EAAE;AAChB,MAAA,OAAQ,qBAAoB,IAAI,CAAC9C,CAAC,CAACksB,KAAK,EAAG,CAAS,OAAA,EAAA,IAAI,CAAC/lB,CAAC,CAAC+lB,KAAK,EAAG,CAAG,EAAA,CAAA,CAAA;AACxE,KAAC,MAAM;AACL,MAAA,OAAQ,CAA8B,4BAAA,EAAA,IAAI,CAACY,aAAc,CAAG,EAAA,CAAA,CAAA;AAC9D,KAAA;AACF,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEyE,cAAcA,CAAC7Q,UAAU,GAAG3B,UAAkB,EAAEvc,IAAI,GAAG,EAAE,EAAE;IACzD,OAAO,IAAI,CAACM,OAAO,GACfmd,SAAS,CAACpa,MAAM,CAAC,IAAI,CAAC7F,CAAC,CAACoK,GAAG,CAACyE,KAAK,CAACrM,IAAI,CAAC,EAAEke,UAAU,CAAC,CAACK,cAAc,CAAC,IAAI,CAAC,GACzEqI,SAAO,CAAA;AACb,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE8C,KAAKA,CAAC1pB,IAAI,EAAE;AACV,IAAA,IAAI,CAAC,IAAI,CAACM,OAAO,EAAE,OAAOsmB,SAAO,CAAA;AACjC,IAAA,OAAQ,GAAE,IAAI,CAACppB,CAAC,CAACksB,KAAK,CAAC1pB,IAAI,CAAE,CAAG,CAAA,EAAA,IAAI,CAAC2D,CAAC,CAAC+lB,KAAK,CAAC1pB,IAAI,CAAE,CAAC,CAAA,CAAA;AACtD,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACEgvB,EAAAA,SAASA,GAAG;AACV,IAAA,IAAI,CAAC,IAAI,CAAC1uB,OAAO,EAAE,OAAOsmB,SAAO,CAAA;AACjC,IAAA,OAAQ,GAAE,IAAI,CAACppB,CAAC,CAACwxB,SAAS,EAAG,CAAG,CAAA,EAAA,IAAI,CAACrrB,CAAC,CAACqrB,SAAS,EAAG,CAAC,CAAA,CAAA;AACtD,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACErF,SAASA,CAAC3pB,IAAI,EAAE;AACd,IAAA,IAAI,CAAC,IAAI,CAACM,OAAO,EAAE,OAAOsmB,SAAO,CAAA;AACjC,IAAA,OAAQ,GAAE,IAAI,CAACppB,CAAC,CAACmsB,SAAS,CAAC3pB,IAAI,CAAE,CAAG,CAAA,EAAA,IAAI,CAAC2D,CAAC,CAACgmB,SAAS,CAAC3pB,IAAI,CAAE,CAAC,CAAA,CAAA;AAC9D,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEmpB,QAAQA,CAAC8F,UAAU,EAAE;AAAEC,IAAAA,SAAS,GAAG,KAAA;GAAO,GAAG,EAAE,EAAE;AAC/C,IAAA,IAAI,CAAC,IAAI,CAAC5uB,OAAO,EAAE,OAAOsmB,SAAO,CAAA;IACjC,OAAQ,CAAA,EAAE,IAAI,CAACppB,CAAC,CAAC2rB,QAAQ,CAAC8F,UAAU,CAAE,CAAA,EAAEC,SAAU,CAAE,EAAA,IAAI,CAACvrB,CAAC,CAACwlB,QAAQ,CAAC8F,UAAU,CAAE,CAAC,CAAA,CAAA;AACnF,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACErC,EAAAA,UAAUA,CAACxvB,IAAI,EAAE4C,IAAI,EAAE;AACrB,IAAA,IAAI,CAAC,IAAI,CAACM,OAAO,EAAE;AACjB,MAAA,OAAOknB,QAAQ,CAACgB,OAAO,CAAC,IAAI,CAAC8B,aAAa,CAAC,CAAA;AAC7C,KAAA;AACA,IAAA,OAAO,IAAI,CAAC3mB,CAAC,CAACopB,IAAI,CAAC,IAAI,CAACvvB,CAAC,EAAEJ,IAAI,EAAE4C,IAAI,CAAC,CAAA;AACxC,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEmvB,YAAYA,CAACC,KAAK,EAAE;AAClB,IAAA,OAAOrD,QAAQ,CAACE,aAAa,CAACmD,KAAK,CAAC,IAAI,CAAC5xB,CAAC,CAAC,EAAE4xB,KAAK,CAAC,IAAI,CAACzrB,CAAC,CAAC,CAAC,CAAA;AAC7D,GAAA;AACF;;ACppBA;AACA;AACA;AACe,MAAM0rB,IAAI,CAAC;AACxB;AACF;AACA;AACA;AACA;AACE,EAAA,OAAOC,MAAMA,CAAChsB,IAAI,GAAGuH,QAAQ,CAACgE,WAAW,EAAE;AACzC,IAAA,MAAM0gB,KAAK,GAAGhoB,QAAQ,CAACkK,GAAG,EAAE,CAACnI,OAAO,CAAChG,IAAI,CAAC,CAAC3B,GAAG,CAAC;AAAE/D,MAAAA,KAAK,EAAE,EAAA;AAAG,KAAC,CAAC,CAAA;AAE7D,IAAA,OAAO,CAAC0F,IAAI,CAACzD,WAAW,IAAI0vB,KAAK,CAACpvB,MAAM,KAAKovB,KAAK,CAAC5tB,GAAG,CAAC;AAAE/D,MAAAA,KAAK,EAAE,CAAA;KAAG,CAAC,CAACuC,MAAM,CAAA;AAC7E,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,OAAOqvB,eAAeA,CAAClsB,IAAI,EAAE;AAC3B,IAAA,OAAOF,QAAQ,CAACM,WAAW,CAACJ,IAAI,CAAC,CAAA;AACnC,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOqL,aAAaA,CAACC,KAAK,EAAE;AAC1B,IAAA,OAAOD,aAAa,CAACC,KAAK,EAAE/D,QAAQ,CAACgE,WAAW,CAAC,CAAA;AACnD,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,OAAOd,cAAcA,CAAC;AAAEjN,IAAAA,MAAM,GAAG,IAAI;AAAE2uB,IAAAA,MAAM,GAAG,IAAA;GAAM,GAAG,EAAE,EAAE;AAC3D,IAAA,OAAO,CAACA,MAAM,IAAIxpB,MAAM,CAAC5C,MAAM,CAACvC,MAAM,CAAC,EAAEiN,cAAc,EAAE,CAAA;AAC3D,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,OAAO2hB,yBAAyBA,CAAC;AAAE5uB,IAAAA,MAAM,GAAG,IAAI;AAAE2uB,IAAAA,MAAM,GAAG,IAAA;GAAM,GAAG,EAAE,EAAE;AACtE,IAAA,OAAO,CAACA,MAAM,IAAIxpB,MAAM,CAAC5C,MAAM,CAACvC,MAAM,CAAC,EAAEkN,qBAAqB,EAAE,CAAA;AAClE,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,OAAO2hB,kBAAkBA,CAAC;AAAE7uB,IAAAA,MAAM,GAAG,IAAI;AAAE2uB,IAAAA,MAAM,GAAG,IAAA;GAAM,GAAG,EAAE,EAAE;AAC/D;AACA,IAAA,OAAO,CAACA,MAAM,IAAIxpB,MAAM,CAAC5C,MAAM,CAACvC,MAAM,CAAC,EAAEmN,cAAc,EAAE,CAACqR,KAAK,EAAE,CAAA;AACnE,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,OAAO5S,MAAMA,CACX5J,MAAM,GAAG,MAAM,EACf;AAAEhC,IAAAA,MAAM,GAAG,IAAI;AAAEgG,IAAAA,eAAe,GAAG,IAAI;AAAE2oB,IAAAA,MAAM,GAAG,IAAI;AAAExoB,IAAAA,cAAc,GAAG,SAAA;GAAW,GAAG,EAAE,EACzF;AACA,IAAA,OAAO,CAACwoB,MAAM,IAAIxpB,MAAM,CAAC5C,MAAM,CAACvC,MAAM,EAAEgG,eAAe,EAAEG,cAAc,CAAC,EAAEyF,MAAM,CAAC5J,MAAM,CAAC,CAAA;AAC1F,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,OAAO8sB,YAAYA,CACjB9sB,MAAM,GAAG,MAAM,EACf;AAAEhC,IAAAA,MAAM,GAAG,IAAI;AAAEgG,IAAAA,eAAe,GAAG,IAAI;AAAE2oB,IAAAA,MAAM,GAAG,IAAI;AAAExoB,IAAAA,cAAc,GAAG,SAAA;GAAW,GAAG,EAAE,EACzF;AACA,IAAA,OAAO,CAACwoB,MAAM,IAAIxpB,MAAM,CAAC5C,MAAM,CAACvC,MAAM,EAAEgG,eAAe,EAAEG,cAAc,CAAC,EAAEyF,MAAM,CAAC5J,MAAM,EAAE,IAAI,CAAC,CAAA;AAChG,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,OAAOkK,QAAQA,CAAClK,MAAM,GAAG,MAAM,EAAE;AAAEhC,IAAAA,MAAM,GAAG,IAAI;AAAEgG,IAAAA,eAAe,GAAG,IAAI;AAAE2oB,IAAAA,MAAM,GAAG,IAAA;GAAM,GAAG,EAAE,EAAE;AAC9F,IAAA,OAAO,CAACA,MAAM,IAAIxpB,MAAM,CAAC5C,MAAM,CAACvC,MAAM,EAAEgG,eAAe,EAAE,IAAI,CAAC,EAAEkG,QAAQ,CAAClK,MAAM,CAAC,CAAA;AAClF,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,OAAO+sB,cAAcA,CACnB/sB,MAAM,GAAG,MAAM,EACf;AAAEhC,IAAAA,MAAM,GAAG,IAAI;AAAEgG,IAAAA,eAAe,GAAG,IAAI;AAAE2oB,IAAAA,MAAM,GAAG,IAAA;GAAM,GAAG,EAAE,EAC7D;AACA,IAAA,OAAO,CAACA,MAAM,IAAIxpB,MAAM,CAAC5C,MAAM,CAACvC,MAAM,EAAEgG,eAAe,EAAE,IAAI,CAAC,EAAEkG,QAAQ,CAAClK,MAAM,EAAE,IAAI,CAAC,CAAA;AACxF,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,OAAOmK,SAASA,CAAC;AAAEnM,IAAAA,MAAM,GAAG,IAAA;GAAM,GAAG,EAAE,EAAE;IACvC,OAAOmF,MAAM,CAAC5C,MAAM,CAACvC,MAAM,CAAC,CAACmM,SAAS,EAAE,CAAA;AAC1C,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,OAAOC,IAAIA,CAACpK,MAAM,GAAG,OAAO,EAAE;AAAEhC,IAAAA,MAAM,GAAG,IAAA;GAAM,GAAG,EAAE,EAAE;AACpD,IAAA,OAAOmF,MAAM,CAAC5C,MAAM,CAACvC,MAAM,EAAE,IAAI,EAAE,SAAS,CAAC,CAACoM,IAAI,CAACpK,MAAM,CAAC,CAAA;AAC5D,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOgtB,QAAQA,GAAG;IAChB,OAAO;MAAEC,QAAQ,EAAE9lB,WAAW,EAAE;MAAE+lB,UAAU,EAAEliB,iBAAiB,EAAC;KAAG,CAAA;AACrE,GAAA;AACF;;AC1MA,SAASmiB,OAAOA,CAACC,OAAO,EAAEC,KAAK,EAAE;EAC/B,MAAMC,WAAW,GAAI9oB,EAAE,IAAKA,EAAE,CAAC+oB,KAAK,CAAC,CAAC,EAAE;AAAEC,MAAAA,aAAa,EAAE,IAAA;KAAM,CAAC,CAACzD,OAAO,CAAC,KAAK,CAAC,CAACtC,OAAO,EAAE;IACvFljB,EAAE,GAAG+oB,WAAW,CAACD,KAAK,CAAC,GAAGC,WAAW,CAACF,OAAO,CAAC,CAAA;AAChD,EAAA,OAAOlsB,IAAI,CAACuE,KAAK,CAACif,QAAQ,CAACkB,UAAU,CAACrhB,EAAE,CAAC,CAAC0jB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAA;AACvD,CAAA;AAEA,SAASwF,cAAcA,CAAClP,MAAM,EAAE8O,KAAK,EAAEjV,KAAK,EAAE;AAC5C,EAAA,MAAMsV,OAAO,GAAG,CACd,CAAC,OAAO,EAAE,CAAC1Z,CAAC,EAAE2W,CAAC,KAAKA,CAAC,CAAC9vB,IAAI,GAAGmZ,CAAC,CAACnZ,IAAI,CAAC,EACpC,CAAC,UAAU,EAAE,CAACmZ,CAAC,EAAE2W,CAAC,KAAKA,CAAC,CAAClO,OAAO,GAAGzI,CAAC,CAACyI,OAAO,GAAG,CAACkO,CAAC,CAAC9vB,IAAI,GAAGmZ,CAAC,CAACnZ,IAAI,IAAI,CAAC,CAAC,EACrE,CAAC,QAAQ,EAAE,CAACmZ,CAAC,EAAE2W,CAAC,KAAKA,CAAC,CAAC7vB,KAAK,GAAGkZ,CAAC,CAAClZ,KAAK,GAAG,CAAC6vB,CAAC,CAAC9vB,IAAI,GAAGmZ,CAAC,CAACnZ,IAAI,IAAI,EAAE,CAAC,EAChE,CACE,OAAO,EACP,CAACmZ,CAAC,EAAE2W,CAAC,KAAK;AACR,IAAA,MAAMnS,IAAI,GAAG2U,OAAO,CAACnZ,CAAC,EAAE2W,CAAC,CAAC,CAAA;AAC1B,IAAA,OAAO,CAACnS,IAAI,GAAIA,IAAI,GAAG,CAAE,IAAI,CAAC,CAAA;AAChC,GAAC,CACF,EACD,CAAC,MAAM,EAAE2U,OAAO,CAAC,CAClB,CAAA;EAED,MAAM5iB,OAAO,GAAG,EAAE,CAAA;EAClB,MAAM6iB,OAAO,GAAG7O,MAAM,CAAA;EACtB,IAAIoP,WAAW,EAAEC,SAAS,CAAA;;AAE1B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,KAAK,MAAM,CAACtzB,IAAI,EAAEuzB,MAAM,CAAC,IAAIH,OAAO,EAAE;IACpC,IAAItV,KAAK,CAAC1U,OAAO,CAACpJ,IAAI,CAAC,IAAI,CAAC,EAAE;AAC5BqzB,MAAAA,WAAW,GAAGrzB,IAAI,CAAA;MAElBiQ,OAAO,CAACjQ,IAAI,CAAC,GAAGuzB,MAAM,CAACtP,MAAM,EAAE8O,KAAK,CAAC,CAAA;AACrCO,MAAAA,SAAS,GAAGR,OAAO,CAAC3mB,IAAI,CAAC8D,OAAO,CAAC,CAAA;MAEjC,IAAIqjB,SAAS,GAAGP,KAAK,EAAE;AACrB;QACA9iB,OAAO,CAACjQ,IAAI,CAAC,EAAE,CAAA;AACfikB,QAAAA,MAAM,GAAG6O,OAAO,CAAC3mB,IAAI,CAAC8D,OAAO,CAAC,CAAA;;AAE9B;AACA;AACA;QACA,IAAIgU,MAAM,GAAG8O,KAAK,EAAE;AAClB;AACAO,UAAAA,SAAS,GAAGrP,MAAM,CAAA;AAClB;UACAhU,OAAO,CAACjQ,IAAI,CAAC,EAAE,CAAA;AACfikB,UAAAA,MAAM,GAAG6O,OAAO,CAAC3mB,IAAI,CAAC8D,OAAO,CAAC,CAAA;AAChC,SAAA;AACF,OAAC,MAAM;AACLgU,QAAAA,MAAM,GAAGqP,SAAS,CAAA;AACpB,OAAA;AACF,KAAA;AACF,GAAA;EAEA,OAAO,CAACrP,MAAM,EAAEhU,OAAO,EAAEqjB,SAAS,EAAED,WAAW,CAAC,CAAA;AAClD,CAAA;AAEe,aAAA,EAAUP,OAAO,EAAEC,KAAK,EAAEjV,KAAK,EAAElb,IAAI,EAAE;AACpD,EAAA,IAAI,CAACqhB,MAAM,EAAEhU,OAAO,EAAEqjB,SAAS,EAAED,WAAW,CAAC,GAAGF,cAAc,CAACL,OAAO,EAAEC,KAAK,EAAEjV,KAAK,CAAC,CAAA;AAErF,EAAA,MAAM0V,eAAe,GAAGT,KAAK,GAAG9O,MAAM,CAAA;EAEtC,MAAMwP,eAAe,GAAG3V,KAAK,CAACuF,MAAM,CACjCzG,CAAC,IAAK,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,cAAc,CAAC,CAACxT,OAAO,CAACwT,CAAC,CAAC,IAAI,CACvE,CAAC,CAAA;AAED,EAAA,IAAI6W,eAAe,CAAC/tB,MAAM,KAAK,CAAC,EAAE;IAChC,IAAI4tB,SAAS,GAAGP,KAAK,EAAE;AACrBO,MAAAA,SAAS,GAAGrP,MAAM,CAAC9X,IAAI,CAAC;AAAE,QAAA,CAACknB,WAAW,GAAG,CAAA;AAAE,OAAC,CAAC,CAAA;AAC/C,KAAA;IAEA,IAAIC,SAAS,KAAKrP,MAAM,EAAE;AACxBhU,MAAAA,OAAO,CAACojB,WAAW,CAAC,GAAG,CAACpjB,OAAO,CAACojB,WAAW,CAAC,IAAI,CAAC,IAAIG,eAAe,IAAIF,SAAS,GAAGrP,MAAM,CAAC,CAAA;AAC7F,KAAA;AACF,GAAA;EAEA,MAAMmJ,QAAQ,GAAGhD,QAAQ,CAACjc,UAAU,CAAC8B,OAAO,EAAErN,IAAI,CAAC,CAAA;AAEnD,EAAA,IAAI6wB,eAAe,CAAC/tB,MAAM,GAAG,CAAC,EAAE;AAC9B,IAAA,OAAO0kB,QAAQ,CAACkB,UAAU,CAACkI,eAAe,EAAE5wB,IAAI,CAAC,CAC9CwgB,OAAO,CAAC,GAAGqQ,eAAe,CAAC,CAC3BtnB,IAAI,CAACihB,QAAQ,CAAC,CAAA;AACnB,GAAC,MAAM;AACL,IAAA,OAAOA,QAAQ,CAAA;AACjB,GAAA;AACF;;ACtFA,MAAMsG,WAAW,GAAG,mDAAmD,CAAA;AAEvE,SAASC,OAAOA,CAACxf,KAAK,EAAEyf,IAAI,GAAInuB,CAAC,IAAKA,CAAC,EAAE;EACvC,OAAO;IAAE0O,KAAK;IAAE0f,KAAK,EAAEA,CAAC,CAACzzB,CAAC,CAAC,KAAKwzB,IAAI,CAACtgB,WAAW,CAAClT,CAAC,CAAC,CAAA;GAAG,CAAA;AACxD,CAAA;AAEA,MAAM0zB,IAAI,GAAGC,MAAM,CAACC,YAAY,CAAC,GAAG,CAAC,CAAA;AACrC,MAAMC,WAAW,GAAI,CAAIH,EAAAA,EAAAA,IAAK,CAAE,CAAA,CAAA,CAAA;AAChC,MAAMI,iBAAiB,GAAG,IAAI9f,MAAM,CAAC6f,WAAW,EAAE,GAAG,CAAC,CAAA;AAEtD,SAASE,YAAYA,CAAC/zB,CAAC,EAAE;AACvB;AACA;AACA,EAAA,OAAOA,CAAC,CAACwE,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAACA,OAAO,CAACsvB,iBAAiB,EAAED,WAAW,CAAC,CAAA;AACzE,CAAA;AAEA,SAASG,oBAAoBA,CAACh0B,CAAC,EAAE;EAC/B,OAAOA,CAAC,CACLwE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AAAC,GACnBA,OAAO,CAACsvB,iBAAiB,EAAE,GAAG,CAAC;GAC/B7jB,WAAW,EAAE,CAAA;AAClB,CAAA;AAEA,SAASgkB,KAAKA,CAACC,OAAO,EAAEC,UAAU,EAAE;EAClC,IAAID,OAAO,KAAK,IAAI,EAAE;AACpB,IAAA,OAAO,IAAI,CAAA;AACb,GAAC,MAAM;IACL,OAAO;AACLngB,MAAAA,KAAK,EAAEC,MAAM,CAACkgB,OAAO,CAACjoB,GAAG,CAAC8nB,YAAY,CAAC,CAAC7nB,IAAI,CAAC,GAAG,CAAC,CAAC;MAClDunB,KAAK,EAAEA,CAAC,CAACzzB,CAAC,CAAC,KACTk0B,OAAO,CAACze,SAAS,CAAEpQ,CAAC,IAAK2uB,oBAAoB,CAACh0B,CAAC,CAAC,KAAKg0B,oBAAoB,CAAC3uB,CAAC,CAAC,CAAC,GAAG8uB,UAAAA;KACnF,CAAA;AACH,GAAA;AACF,CAAA;AAEA,SAASxxB,MAAMA,CAACoR,KAAK,EAAEqgB,MAAM,EAAE;EAC7B,OAAO;IAAErgB,KAAK;AAAE0f,IAAAA,KAAK,EAAEA,CAAC,GAAGY,CAAC,EAAErkB,CAAC,CAAC,KAAKiB,YAAY,CAACojB,CAAC,EAAErkB,CAAC,CAAC;AAAEokB,IAAAA,MAAAA;GAAQ,CAAA;AACnE,CAAA;AAEA,SAASE,MAAMA,CAACvgB,KAAK,EAAE;EACrB,OAAO;IAAEA,KAAK;AAAE0f,IAAAA,KAAK,EAAEA,CAAC,CAACzzB,CAAC,CAAC,KAAKA,CAAAA;GAAG,CAAA;AACrC,CAAA;AAEA,SAASu0B,WAAWA,CAAChvB,KAAK,EAAE;AAC1B,EAAA,OAAOA,KAAK,CAACf,OAAO,CAAC,6BAA6B,EAAE,MAAM,CAAC,CAAA;AAC7D,CAAA;;AAEA;AACA;AACA;AACA;AACA,SAASgwB,YAAYA,CAAC9V,KAAK,EAAEtU,GAAG,EAAE;AAChC,EAAA,MAAMqqB,GAAG,GAAG9gB,UAAU,CAACvJ,GAAG,CAAC;AACzBsqB,IAAAA,GAAG,GAAG/gB,UAAU,CAACvJ,GAAG,EAAE,KAAK,CAAC;AAC5BuqB,IAAAA,KAAK,GAAGhhB,UAAU,CAACvJ,GAAG,EAAE,KAAK,CAAC;AAC9BwqB,IAAAA,IAAI,GAAGjhB,UAAU,CAACvJ,GAAG,EAAE,KAAK,CAAC;AAC7ByqB,IAAAA,GAAG,GAAGlhB,UAAU,CAACvJ,GAAG,EAAE,KAAK,CAAC;AAC5B0qB,IAAAA,QAAQ,GAAGnhB,UAAU,CAACvJ,GAAG,EAAE,OAAO,CAAC;AACnC2qB,IAAAA,UAAU,GAAGphB,UAAU,CAACvJ,GAAG,EAAE,OAAO,CAAC;AACrC4qB,IAAAA,QAAQ,GAAGrhB,UAAU,CAACvJ,GAAG,EAAE,OAAO,CAAC;AACnC6qB,IAAAA,SAAS,GAAGthB,UAAU,CAACvJ,GAAG,EAAE,OAAO,CAAC;AACpC8qB,IAAAA,SAAS,GAAGvhB,UAAU,CAACvJ,GAAG,EAAE,OAAO,CAAC;AACpC+qB,IAAAA,SAAS,GAAGxhB,UAAU,CAACvJ,GAAG,EAAE,OAAO,CAAC;IACpCuU,OAAO,GAAItK,CAAC,KAAM;MAAEN,KAAK,EAAEC,MAAM,CAACugB,WAAW,CAAClgB,CAAC,CAACuK,GAAG,CAAC,CAAC;AAAE6U,MAAAA,KAAK,EAAEA,CAAC,CAACzzB,CAAC,CAAC,KAAKA,CAAC;AAAE2e,MAAAA,OAAO,EAAE,IAAA;AAAK,KAAC,CAAC;IAC1FyW,OAAO,GAAI/gB,CAAC,IAAK;MACf,IAAIqK,KAAK,CAACC,OAAO,EAAE;QACjB,OAAOA,OAAO,CAACtK,CAAC,CAAC,CAAA;AACnB,OAAA;MACA,QAAQA,CAAC,CAACuK,GAAG;AACX;AACA,QAAA,KAAK,GAAG;UACN,OAAOqV,KAAK,CAAC7pB,GAAG,CAACsF,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAA;AACpC,QAAA,KAAK,IAAI;UACP,OAAOukB,KAAK,CAAC7pB,GAAG,CAACsF,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;AACnC;AACA,QAAA,KAAK,GAAG;UACN,OAAO6jB,OAAO,CAACyB,QAAQ,CAAC,CAAA;AAC1B,QAAA,KAAK,IAAI;AACP,UAAA,OAAOzB,OAAO,CAAC2B,SAAS,EAAE1Z,cAAc,CAAC,CAAA;AAC3C,QAAA,KAAK,MAAM;UACT,OAAO+X,OAAO,CAACqB,IAAI,CAAC,CAAA;AACtB,QAAA,KAAK,OAAO;UACV,OAAOrB,OAAO,CAAC4B,SAAS,CAAC,CAAA;AAC3B,QAAA,KAAK,QAAQ;UACX,OAAO5B,OAAO,CAACsB,GAAG,CAAC,CAAA;AACrB;AACA,QAAA,KAAK,GAAG;UACN,OAAOtB,OAAO,CAACuB,QAAQ,CAAC,CAAA;AAC1B,QAAA,KAAK,IAAI;UACP,OAAOvB,OAAO,CAACmB,GAAG,CAAC,CAAA;AACrB,QAAA,KAAK,KAAK;AACR,UAAA,OAAOT,KAAK,CAAC7pB,GAAG,CAAC8E,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;AAC5C,QAAA,KAAK,MAAM;AACT,UAAA,OAAO+kB,KAAK,CAAC7pB,GAAG,CAAC8E,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;AAC3C,QAAA,KAAK,GAAG;UACN,OAAOqkB,OAAO,CAACuB,QAAQ,CAAC,CAAA;AAC1B,QAAA,KAAK,IAAI;UACP,OAAOvB,OAAO,CAACmB,GAAG,CAAC,CAAA;AACrB,QAAA,KAAK,KAAK;AACR,UAAA,OAAOT,KAAK,CAAC7pB,GAAG,CAAC8E,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAA;AAC7C,QAAA,KAAK,MAAM;AACT,UAAA,OAAO+kB,KAAK,CAAC7pB,GAAG,CAAC8E,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAA;AAC5C;AACA,QAAA,KAAK,GAAG;UACN,OAAOqkB,OAAO,CAACuB,QAAQ,CAAC,CAAA;AAC1B,QAAA,KAAK,IAAI;UACP,OAAOvB,OAAO,CAACmB,GAAG,CAAC,CAAA;AACrB;AACA,QAAA,KAAK,GAAG;UACN,OAAOnB,OAAO,CAACwB,UAAU,CAAC,CAAA;AAC5B,QAAA,KAAK,KAAK;UACR,OAAOxB,OAAO,CAACoB,KAAK,CAAC,CAAA;AACvB;AACA,QAAA,KAAK,IAAI;UACP,OAAOpB,OAAO,CAACmB,GAAG,CAAC,CAAA;AACrB,QAAA,KAAK,GAAG;UACN,OAAOnB,OAAO,CAACuB,QAAQ,CAAC,CAAA;AAC1B,QAAA,KAAK,IAAI;UACP,OAAOvB,OAAO,CAACmB,GAAG,CAAC,CAAA;AACrB,QAAA,KAAK,GAAG;UACN,OAAOnB,OAAO,CAACuB,QAAQ,CAAC,CAAA;AAC1B,QAAA,KAAK,IAAI;UACP,OAAOvB,OAAO,CAACmB,GAAG,CAAC,CAAA;AACrB,QAAA,KAAK,GAAG;UACN,OAAOnB,OAAO,CAACuB,QAAQ,CAAC,CAAA;AAC1B,QAAA,KAAK,GAAG;UACN,OAAOvB,OAAO,CAACuB,QAAQ,CAAC,CAAA;AAC1B,QAAA,KAAK,IAAI;UACP,OAAOvB,OAAO,CAACmB,GAAG,CAAC,CAAA;AACrB,QAAA,KAAK,GAAG;UACN,OAAOnB,OAAO,CAACuB,QAAQ,CAAC,CAAA;AAC1B,QAAA,KAAK,IAAI;UACP,OAAOvB,OAAO,CAACmB,GAAG,CAAC,CAAA;AACrB,QAAA,KAAK,GAAG;UACN,OAAOnB,OAAO,CAACwB,UAAU,CAAC,CAAA;AAC5B,QAAA,KAAK,KAAK;UACR,OAAOxB,OAAO,CAACoB,KAAK,CAAC,CAAA;AACvB,QAAA,KAAK,GAAG;UACN,OAAOL,MAAM,CAACW,SAAS,CAAC,CAAA;AAC1B,QAAA,KAAK,IAAI;UACP,OAAOX,MAAM,CAACQ,QAAQ,CAAC,CAAA;AACzB,QAAA,KAAK,KAAK;UACR,OAAOvB,OAAO,CAACkB,GAAG,CAAC,CAAA;AACrB;AACA,QAAA,KAAK,GAAG;UACN,OAAOR,KAAK,CAAC7pB,GAAG,CAACqF,SAAS,EAAE,EAAE,CAAC,CAAC,CAAA;AAClC;AACA,QAAA,KAAK,MAAM;UACT,OAAO8jB,OAAO,CAACqB,IAAI,CAAC,CAAA;AACtB,QAAA,KAAK,IAAI;AACP,UAAA,OAAOrB,OAAO,CAAC2B,SAAS,EAAE1Z,cAAc,CAAC,CAAA;AAC3C;AACA,QAAA,KAAK,GAAG;UACN,OAAO+X,OAAO,CAACuB,QAAQ,CAAC,CAAA;AAC1B,QAAA,KAAK,IAAI;UACP,OAAOvB,OAAO,CAACmB,GAAG,CAAC,CAAA;AACrB;AACA,QAAA,KAAK,GAAG,CAAA;AACR,QAAA,KAAK,GAAG;UACN,OAAOnB,OAAO,CAACkB,GAAG,CAAC,CAAA;AACrB,QAAA,KAAK,KAAK;AACR,UAAA,OAAOR,KAAK,CAAC7pB,GAAG,CAACoF,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAA;AAC/C,QAAA,KAAK,MAAM;AACT,UAAA,OAAOykB,KAAK,CAAC7pB,GAAG,CAACoF,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAA;AAC9C,QAAA,KAAK,KAAK;AACR,UAAA,OAAOykB,KAAK,CAAC7pB,GAAG,CAACoF,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;AAC9C,QAAA,KAAK,MAAM;AACT,UAAA,OAAOykB,KAAK,CAAC7pB,GAAG,CAACoF,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;AAC7C;AACA,QAAA,KAAK,GAAG,CAAA;AACR,QAAA,KAAK,IAAI;AACP,UAAA,OAAO7M,MAAM,CAAC,IAAIqR,MAAM,CAAE,QAAO8gB,QAAQ,CAACtR,MAAO,CAAA,MAAA,EAAQkR,GAAG,CAAClR,MAAO,KAAI,CAAC,EAAE,CAAC,CAAC,CAAA;AAC/E,QAAA,KAAK,KAAK;AACR,UAAA,OAAO7gB,MAAM,CAAC,IAAIqR,MAAM,CAAE,QAAO8gB,QAAQ,CAACtR,MAAO,CAAA,EAAA,EAAIkR,GAAG,CAAClR,MAAO,IAAG,CAAC,EAAE,CAAC,CAAC,CAAA;AAC1E;AACA;AACA,QAAA,KAAK,GAAG;UACN,OAAO8Q,MAAM,CAAC,oBAAoB,CAAC,CAAA;AACrC;AACA;AACA,QAAA,KAAK,GAAG;UACN,OAAOA,MAAM,CAAC,WAAW,CAAC,CAAA;AAC5B,QAAA;UACE,OAAO3V,OAAO,CAACtK,CAAC,CAAC,CAAA;AACrB,OAAA;KACD,CAAA;AAEH,EAAA,MAAMzU,IAAI,GAAGw1B,OAAO,CAAC1W,KAAK,CAAC,IAAI;AAC7BoO,IAAAA,aAAa,EAAEwG,WAAAA;GAChB,CAAA;EAED1zB,IAAI,CAAC8e,KAAK,GAAGA,KAAK,CAAA;AAElB,EAAA,OAAO9e,IAAI,CAAA;AACb,CAAA;AAEA,MAAMy1B,uBAAuB,GAAG;AAC9Bl1B,EAAAA,IAAI,EAAE;AACJ,IAAA,SAAS,EAAE,IAAI;AACf0M,IAAAA,OAAO,EAAE,OAAA;GACV;AACDzM,EAAAA,KAAK,EAAE;AACLyM,IAAAA,OAAO,EAAE,GAAG;AACZ,IAAA,SAAS,EAAE,IAAI;AACfyoB,IAAAA,KAAK,EAAE,KAAK;AACZC,IAAAA,IAAI,EAAE,MAAA;GACP;AACDl1B,EAAAA,GAAG,EAAE;AACHwM,IAAAA,OAAO,EAAE,GAAG;AACZ,IAAA,SAAS,EAAE,IAAA;GACZ;AACDrM,EAAAA,OAAO,EAAE;AACP80B,IAAAA,KAAK,EAAE,KAAK;AACZC,IAAAA,IAAI,EAAE,MAAA;GACP;AACDC,EAAAA,SAAS,EAAE,GAAG;AACdC,EAAAA,SAAS,EAAE,GAAG;AACdxxB,EAAAA,MAAM,EAAE;AACN4I,IAAAA,OAAO,EAAE,GAAG;AACZ,IAAA,SAAS,EAAE,IAAA;GACZ;AACD6oB,EAAAA,MAAM,EAAE;AACN7oB,IAAAA,OAAO,EAAE,GAAG;AACZ,IAAA,SAAS,EAAE,IAAA;GACZ;AACDhM,EAAAA,MAAM,EAAE;AACNgM,IAAAA,OAAO,EAAE,GAAG;AACZ,IAAA,SAAS,EAAE,IAAA;GACZ;AACD9L,EAAAA,MAAM,EAAE;AACN8L,IAAAA,OAAO,EAAE,GAAG;AACZ,IAAA,SAAS,EAAE,IAAA;GACZ;AACD5L,EAAAA,YAAY,EAAE;AACZs0B,IAAAA,IAAI,EAAE,OAAO;AACbD,IAAAA,KAAK,EAAE,KAAA;AACT,GAAA;AACF,CAAC,CAAA;AAED,SAASK,YAAYA,CAACtpB,IAAI,EAAEqU,UAAU,EAAEkV,YAAY,EAAE;EACpD,MAAM;IAAE1zB,IAAI;AAAEqD,IAAAA,KAAAA;AAAM,GAAC,GAAG8G,IAAI,CAAA;EAE5B,IAAInK,IAAI,KAAK,SAAS,EAAE;AACtB,IAAA,MAAM2zB,OAAO,GAAG,OAAO,CAACpV,IAAI,CAAClb,KAAK,CAAC,CAAA;IACnC,OAAO;MACLoZ,OAAO,EAAE,CAACkX,OAAO;AACjBjX,MAAAA,GAAG,EAAEiX,OAAO,GAAG,GAAG,GAAGtwB,KAAAA;KACtB,CAAA;AACH,GAAA;AAEA,EAAA,MAAMiH,KAAK,GAAGkU,UAAU,CAACxe,IAAI,CAAC,CAAA;;AAE9B;AACA;AACA;EACA,IAAI4zB,UAAU,GAAG5zB,IAAI,CAAA;EACrB,IAAIA,IAAI,KAAK,MAAM,EAAE;AACnB,IAAA,IAAIwe,UAAU,CAACzc,MAAM,IAAI,IAAI,EAAE;AAC7B6xB,MAAAA,UAAU,GAAGpV,UAAU,CAACzc,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAA;AACtD,KAAC,MAAM,IAAIyc,UAAU,CAACtf,SAAS,IAAI,IAAI,EAAE;MACvC,IAAIsf,UAAU,CAACtf,SAAS,KAAK,KAAK,IAAIsf,UAAU,CAACtf,SAAS,KAAK,KAAK,EAAE;AACpE00B,QAAAA,UAAU,GAAG,QAAQ,CAAA;AACvB,OAAC,MAAM;AACLA,QAAAA,UAAU,GAAG,QAAQ,CAAA;AACvB,OAAA;AACF,KAAC,MAAM;AACL;AACA;AACAA,MAAAA,UAAU,GAAGF,YAAY,CAAC3xB,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAA;AACxD,KAAA;AACF,GAAA;AACA,EAAA,IAAI2a,GAAG,GAAGyW,uBAAuB,CAACS,UAAU,CAAC,CAAA;AAC7C,EAAA,IAAI,OAAOlX,GAAG,KAAK,QAAQ,EAAE;AAC3BA,IAAAA,GAAG,GAAGA,GAAG,CAACpS,KAAK,CAAC,CAAA;AAClB,GAAA;AAEA,EAAA,IAAIoS,GAAG,EAAE;IACP,OAAO;AACLD,MAAAA,OAAO,EAAE,KAAK;AACdC,MAAAA,GAAAA;KACD,CAAA;AACH,GAAA;AAEA,EAAA,OAAO5a,SAAS,CAAA;AAClB,CAAA;AAEA,SAAS+xB,UAAUA,CAACrY,KAAK,EAAE;AACzB,EAAA,MAAMsY,EAAE,GAAGtY,KAAK,CAACzR,GAAG,CAAEuQ,CAAC,IAAKA,CAAC,CAACzI,KAAK,CAAC,CAACkF,MAAM,CAAC,CAACrP,CAAC,EAAEmH,CAAC,KAAM,CAAEnH,EAAAA,CAAE,CAAGmH,CAAAA,EAAAA,CAAC,CAACyS,MAAO,CAAE,CAAA,CAAA,EAAE,EAAE,CAAC,CAAA;AAC9E,EAAA,OAAO,CAAE,CAAGwS,CAAAA,EAAAA,EAAG,CAAE,CAAA,CAAA,EAAEtY,KAAK,CAAC,CAAA;AAC3B,CAAA;AAEA,SAAS1M,KAAKA,CAACI,KAAK,EAAE2C,KAAK,EAAEkiB,QAAQ,EAAE;AACrC,EAAA,MAAMC,OAAO,GAAG9kB,KAAK,CAACJ,KAAK,CAAC+C,KAAK,CAAC,CAAA;AAElC,EAAA,IAAImiB,OAAO,EAAE;IACX,MAAMC,GAAG,GAAG,EAAE,CAAA;IACd,IAAIC,UAAU,GAAG,CAAC,CAAA;AAClB,IAAA,KAAK,MAAM/wB,CAAC,IAAI4wB,QAAQ,EAAE;AACxB,MAAA,IAAIzc,cAAc,CAACyc,QAAQ,EAAE5wB,CAAC,CAAC,EAAE;AAC/B,QAAA,MAAMgvB,CAAC,GAAG4B,QAAQ,CAAC5wB,CAAC,CAAC;UACnB+uB,MAAM,GAAGC,CAAC,CAACD,MAAM,GAAGC,CAAC,CAACD,MAAM,GAAG,CAAC,GAAG,CAAC,CAAA;QACtC,IAAI,CAACC,CAAC,CAAC1V,OAAO,IAAI0V,CAAC,CAAC3V,KAAK,EAAE;UACzByX,GAAG,CAAC9B,CAAC,CAAC3V,KAAK,CAACE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGyV,CAAC,CAACZ,KAAK,CAACyC,OAAO,CAACpU,KAAK,CAACsU,UAAU,EAAEA,UAAU,GAAGhC,MAAM,CAAC,CAAC,CAAA;AAC/E,SAAA;AACAgC,QAAAA,UAAU,IAAIhC,MAAM,CAAA;AACtB,OAAA;AACF,KAAA;AACA,IAAA,OAAO,CAAC8B,OAAO,EAAEC,GAAG,CAAC,CAAA;AACvB,GAAC,MAAM;AACL,IAAA,OAAO,CAACD,OAAO,EAAE,EAAE,CAAC,CAAA;AACtB,GAAA;AACF,CAAA;AAEA,SAASG,mBAAmBA,CAACH,OAAO,EAAE;EACpC,MAAMI,OAAO,GAAI5X,KAAK,IAAK;AACzB,IAAA,QAAQA,KAAK;AACX,MAAA,KAAK,GAAG;AACN,QAAA,OAAO,aAAa,CAAA;AACtB,MAAA,KAAK,GAAG;AACN,QAAA,OAAO,QAAQ,CAAA;AACjB,MAAA,KAAK,GAAG;AACN,QAAA,OAAO,QAAQ,CAAA;AACjB,MAAA,KAAK,GAAG,CAAA;AACR,MAAA,KAAK,GAAG;AACN,QAAA,OAAO,MAAM,CAAA;AACf,MAAA,KAAK,GAAG;AACN,QAAA,OAAO,KAAK,CAAA;AACd,MAAA,KAAK,GAAG;AACN,QAAA,OAAO,SAAS,CAAA;AAClB,MAAA,KAAK,GAAG,CAAA;AACR,MAAA,KAAK,GAAG;AACN,QAAA,OAAO,OAAO,CAAA;AAChB,MAAA,KAAK,GAAG;AACN,QAAA,OAAO,MAAM,CAAA;AACf,MAAA,KAAK,GAAG,CAAA;AACR,MAAA,KAAK,GAAG;AACN,QAAA,OAAO,SAAS,CAAA;AAClB,MAAA,KAAK,GAAG;AACN,QAAA,OAAO,YAAY,CAAA;AACrB,MAAA,KAAK,GAAG;AACN,QAAA,OAAO,UAAU,CAAA;AACnB,MAAA,KAAK,GAAG;AACN,QAAA,OAAO,SAAS,CAAA;AAClB,MAAA;AACE,QAAA,OAAO,IAAI,CAAA;AACf,KAAA;GACD,CAAA;EAED,IAAI5Y,IAAI,GAAG,IAAI,CAAA;AACf,EAAA,IAAIywB,cAAc,CAAA;AAClB,EAAA,IAAI,CAAC9wB,WAAW,CAACywB,OAAO,CAACvqB,CAAC,CAAC,EAAE;IAC3B7F,IAAI,GAAGF,QAAQ,CAACC,MAAM,CAACqwB,OAAO,CAACvqB,CAAC,CAAC,CAAA;AACnC,GAAA;AAEA,EAAA,IAAI,CAAClG,WAAW,CAACywB,OAAO,CAACM,CAAC,CAAC,EAAE;IAC3B,IAAI,CAAC1wB,IAAI,EAAE;AACTA,MAAAA,IAAI,GAAG,IAAI8K,eAAe,CAACslB,OAAO,CAACM,CAAC,CAAC,CAAA;AACvC,KAAA;IACAD,cAAc,GAAGL,OAAO,CAACM,CAAC,CAAA;AAC5B,GAAA;AAEA,EAAA,IAAI,CAAC/wB,WAAW,CAACywB,OAAO,CAACO,CAAC,CAAC,EAAE;AAC3BP,IAAAA,OAAO,CAACQ,CAAC,GAAG,CAACR,OAAO,CAACO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AACrC,GAAA;AAEA,EAAA,IAAI,CAAChxB,WAAW,CAACywB,OAAO,CAAC7B,CAAC,CAAC,EAAE;IAC3B,IAAI6B,OAAO,CAAC7B,CAAC,GAAG,EAAE,IAAI6B,OAAO,CAAC5c,CAAC,KAAK,CAAC,EAAE;MACrC4c,OAAO,CAAC7B,CAAC,IAAI,EAAE,CAAA;AACjB,KAAC,MAAM,IAAI6B,OAAO,CAAC7B,CAAC,KAAK,EAAE,IAAI6B,OAAO,CAAC5c,CAAC,KAAK,CAAC,EAAE;MAC9C4c,OAAO,CAAC7B,CAAC,GAAG,CAAC,CAAA;AACf,KAAA;AACF,GAAA;EAEA,IAAI6B,OAAO,CAACS,CAAC,KAAK,CAAC,IAAIT,OAAO,CAACU,CAAC,EAAE;AAChCV,IAAAA,OAAO,CAACU,CAAC,GAAG,CAACV,OAAO,CAACU,CAAC,CAAA;AACxB,GAAA;AAEA,EAAA,IAAI,CAACnxB,WAAW,CAACywB,OAAO,CAAC1Z,CAAC,CAAC,EAAE;IAC3B0Z,OAAO,CAACW,CAAC,GAAGrc,WAAW,CAAC0b,OAAO,CAAC1Z,CAAC,CAAC,CAAA;AACpC,GAAA;AAEA,EAAA,MAAM0N,IAAI,GAAGjf,MAAM,CAACC,IAAI,CAACgrB,OAAO,CAAC,CAACjd,MAAM,CAAC,CAAClI,CAAC,EAAEwI,CAAC,KAAK;AACjD,IAAA,MAAM3P,CAAC,GAAG0sB,OAAO,CAAC/c,CAAC,CAAC,CAAA;AACpB,IAAA,IAAI3P,CAAC,EAAE;AACLmH,MAAAA,CAAC,CAACnH,CAAC,CAAC,GAAGssB,OAAO,CAAC3c,CAAC,CAAC,CAAA;AACnB,KAAA;AAEA,IAAA,OAAOxI,CAAC,CAAA;GACT,EAAE,EAAE,CAAC,CAAA;AAEN,EAAA,OAAO,CAACmZ,IAAI,EAAEpkB,IAAI,EAAEywB,cAAc,CAAC,CAAA;AACrC,CAAA;AAEA,IAAIO,kBAAkB,GAAG,IAAI,CAAA;AAE7B,SAASC,gBAAgBA,GAAG;EAC1B,IAAI,CAACD,kBAAkB,EAAE;AACvBA,IAAAA,kBAAkB,GAAG/sB,QAAQ,CAACmhB,UAAU,CAAC,aAAa,CAAC,CAAA;AACzD,GAAA;AAEA,EAAA,OAAO4L,kBAAkB,CAAA;AAC3B,CAAA;AAEA,SAASE,qBAAqBA,CAACtY,KAAK,EAAEpb,MAAM,EAAE;EAC5C,IAAIob,KAAK,CAACC,OAAO,EAAE;AACjB,IAAA,OAAOD,KAAK,CAAA;AACd,GAAA;EAEA,MAAMgC,UAAU,GAAGT,SAAS,CAACpB,sBAAsB,CAACH,KAAK,CAACE,GAAG,CAAC,CAAA;AAC9D,EAAA,MAAM+D,MAAM,GAAGsU,kBAAkB,CAACvW,UAAU,EAAEpd,MAAM,CAAC,CAAA;EAErD,IAAIqf,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACjZ,QAAQ,CAAC1F,SAAS,CAAC,EAAE;AAChD,IAAA,OAAO0a,KAAK,CAAA;AACd,GAAA;AAEA,EAAA,OAAOiE,MAAM,CAAA;AACf,CAAA;AAEO,SAASuU,iBAAiBA,CAACvU,MAAM,EAAErf,MAAM,EAAE;EAChD,OAAOqV,KAAK,CAACJ,SAAS,CAACuK,MAAM,CAAC,GAAGH,MAAM,CAAC1W,GAAG,CAAEoI,CAAC,IAAK2iB,qBAAqB,CAAC3iB,CAAC,EAAE/Q,MAAM,CAAC,CAAC,CAAC,CAAA;AACvF,CAAA;;AAEA;AACA;AACA;;AAEO,MAAM6zB,WAAW,CAAC;AACvB93B,EAAAA,WAAWA,CAACiE,MAAM,EAAEZ,MAAM,EAAE;IAC1B,IAAI,CAACY,MAAM,GAAGA,MAAM,CAAA;IACpB,IAAI,CAACZ,MAAM,GAAGA,MAAM,CAAA;AACpB,IAAA,IAAI,CAACigB,MAAM,GAAGuU,iBAAiB,CAACjX,SAAS,CAACC,WAAW,CAACxd,MAAM,CAAC,EAAEY,MAAM,CAAC,CAAA;AACtE,IAAA,IAAI,CAACoa,KAAK,GAAG,IAAI,CAACiF,MAAM,CAAC1W,GAAG,CAAEoI,CAAC,IAAKmgB,YAAY,CAACngB,CAAC,EAAE/Q,MAAM,CAAC,CAAC,CAAA;AAC5D,IAAA,IAAI,CAAC8zB,iBAAiB,GAAG,IAAI,CAAC1Z,KAAK,CAAC3N,IAAI,CAAEsE,CAAC,IAAKA,CAAC,CAACyY,aAAa,CAAC,CAAA;AAEhE,IAAA,IAAI,CAAC,IAAI,CAACsK,iBAAiB,EAAE;MAC3B,MAAM,CAACC,WAAW,EAAEpB,QAAQ,CAAC,GAAGF,UAAU,CAAC,IAAI,CAACrY,KAAK,CAAC,CAAA;MACtD,IAAI,CAAC3J,KAAK,GAAGC,MAAM,CAACqjB,WAAW,EAAE,GAAG,CAAC,CAAA;MACrC,IAAI,CAACpB,QAAQ,GAAGA,QAAQ,CAAA;AAC1B,KAAA;AACF,GAAA;EAEAqB,iBAAiBA,CAAClmB,KAAK,EAAE;AACvB,IAAA,IAAI,CAAC,IAAI,CAACtO,OAAO,EAAE;MACjB,OAAO;QAAEsO,KAAK;QAAEuR,MAAM,EAAE,IAAI,CAACA,MAAM;QAAEmK,aAAa,EAAE,IAAI,CAACA,aAAAA;OAAe,CAAA;AAC1E,KAAC,MAAM;AACL,MAAA,MAAM,CAACyK,UAAU,EAAErB,OAAO,CAAC,GAAGllB,KAAK,CAACI,KAAK,EAAE,IAAI,CAAC2C,KAAK,EAAE,IAAI,CAACkiB,QAAQ,CAAC;QACnE,CAAC3O,MAAM,EAAExhB,IAAI,EAAEywB,cAAc,CAAC,GAAGL,OAAO,GACpCG,mBAAmB,CAACH,OAAO,CAAC,GAC5B,CAAC,IAAI,EAAE,IAAI,EAAElyB,SAAS,CAAC,CAAA;AAC7B,MAAA,IAAIwV,cAAc,CAAC0c,OAAO,EAAE,GAAG,CAAC,IAAI1c,cAAc,CAAC0c,OAAO,EAAE,GAAG,CAAC,EAAE;AAChE,QAAA,MAAM,IAAIx2B,6BAA6B,CACrC,uDACF,CAAC,CAAA;AACH,OAAA;MACA,OAAO;QACL0R,KAAK;QACLuR,MAAM,EAAE,IAAI,CAACA,MAAM;QACnB5O,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBwjB,UAAU;QACVrB,OAAO;QACP5O,MAAM;QACNxhB,IAAI;AACJywB,QAAAA,cAAAA;OACD,CAAA;AACH,KAAA;AACF,GAAA;EAEA,IAAIzzB,OAAOA,GAAG;IACZ,OAAO,CAAC,IAAI,CAACs0B,iBAAiB,CAAA;AAChC,GAAA;EAEA,IAAItK,aAAaA,GAAG;IAClB,OAAO,IAAI,CAACsK,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACtK,aAAa,GAAG,IAAI,CAAA;AAC7E,GAAA;AACF,CAAA;AAEO,SAASwK,iBAAiBA,CAACh0B,MAAM,EAAE8N,KAAK,EAAE1O,MAAM,EAAE;EACvD,MAAM80B,MAAM,GAAG,IAAIL,WAAW,CAAC7zB,MAAM,EAAEZ,MAAM,CAAC,CAAA;AAC9C,EAAA,OAAO80B,MAAM,CAACF,iBAAiB,CAAClmB,KAAK,CAAC,CAAA;AACxC,CAAA;AAEO,SAASqmB,eAAeA,CAACn0B,MAAM,EAAE8N,KAAK,EAAE1O,MAAM,EAAE;EACrD,MAAM;IAAE4kB,MAAM;IAAExhB,IAAI;IAAEywB,cAAc;AAAEzJ,IAAAA,aAAAA;GAAe,GAAGwK,iBAAiB,CAACh0B,MAAM,EAAE8N,KAAK,EAAE1O,MAAM,CAAC,CAAA;EAChG,OAAO,CAAC4kB,MAAM,EAAExhB,IAAI,EAAEywB,cAAc,EAAEzJ,aAAa,CAAC,CAAA;AACtD,CAAA;AAEO,SAASmK,kBAAkBA,CAACvW,UAAU,EAAEpd,MAAM,EAAE;EACrD,IAAI,CAACod,UAAU,EAAE;AACf,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEA,MAAMgX,SAAS,GAAGzX,SAAS,CAACpa,MAAM,CAACvC,MAAM,EAAEod,UAAU,CAAC,CAAA;EACtD,MAAM9Q,EAAE,GAAG8nB,SAAS,CAACnoB,WAAW,CAACwnB,gBAAgB,EAAE,CAAC,CAAA;AACpD,EAAA,MAAM3qB,KAAK,GAAGwD,EAAE,CAACzK,aAAa,EAAE,CAAA;AAChC,EAAA,MAAMywB,YAAY,GAAGhmB,EAAE,CAACxM,eAAe,EAAE,CAAA;AACzC,EAAA,OAAOgJ,KAAK,CAACH,GAAG,CAAEoV,CAAC,IAAKsU,YAAY,CAACtU,CAAC,EAAEX,UAAU,EAAEkV,YAAY,CAAC,CAAC,CAAA;AACpE;;ACncA,MAAMxM,OAAO,GAAG,kBAAkB,CAAA;AAClC,MAAMuO,QAAQ,GAAG,OAAO,CAAA;AAExB,SAASC,eAAeA,CAAC9xB,IAAI,EAAE;EAC7B,OAAO,IAAIyO,OAAO,CAAC,kBAAkB,EAAG,aAAYzO,IAAI,CAAC3D,IAAK,CAAA,kBAAA,CAAmB,CAAC,CAAA;AACpF,CAAA;;AAEA;AACA;AACA;AACA;AACA,SAAS01B,sBAAsBA,CAAC/tB,EAAE,EAAE;AAClC,EAAA,IAAIA,EAAE,CAACuM,QAAQ,KAAK,IAAI,EAAE;IACxBvM,EAAE,CAACuM,QAAQ,GAAGR,eAAe,CAAC/L,EAAE,CAACyW,CAAC,CAAC,CAAA;AACrC,GAAA;EACA,OAAOzW,EAAE,CAACuM,QAAQ,CAAA;AACpB,CAAA;;AAEA;AACA;AACA;AACA,SAASyhB,2BAA2BA,CAAChuB,EAAE,EAAE;AACvC,EAAA,IAAIA,EAAE,CAACiuB,aAAa,KAAK,IAAI,EAAE;IAC7BjuB,EAAE,CAACiuB,aAAa,GAAGliB,eAAe,CAChC/L,EAAE,CAACyW,CAAC,EACJzW,EAAE,CAACM,GAAG,CAACoG,qBAAqB,EAAE,EAC9B1G,EAAE,CAACM,GAAG,CAACmG,cAAc,EACvB,CAAC,CAAA;AACH,GAAA;EACA,OAAOzG,EAAE,CAACiuB,aAAa,CAAA;AACzB,CAAA;;AAEA;AACA;AACA,SAASlpB,KAAKA,CAACmpB,IAAI,EAAElpB,IAAI,EAAE;AACzB,EAAA,MAAMsR,OAAO,GAAG;IACd7d,EAAE,EAAEy1B,IAAI,CAACz1B,EAAE;IACXuD,IAAI,EAAEkyB,IAAI,CAAClyB,IAAI;IACfya,CAAC,EAAEyX,IAAI,CAACzX,CAAC;IACTlI,CAAC,EAAE2f,IAAI,CAAC3f,CAAC;IACTjO,GAAG,EAAE4tB,IAAI,CAAC5tB,GAAG;IACb4gB,OAAO,EAAEgN,IAAI,CAAChN,OAAAA;GACf,CAAA;EACD,OAAO,IAAIjhB,QAAQ,CAAC;AAAE,IAAA,GAAGqW,OAAO;AAAE,IAAA,GAAGtR,IAAI;AAAEmpB,IAAAA,GAAG,EAAE7X,OAAAA;AAAQ,GAAC,CAAC,CAAA;AAC5D,CAAA;;AAEA;AACA;AACA,SAAS8X,SAASA,CAACC,OAAO,EAAE9f,CAAC,EAAE+f,EAAE,EAAE;AACjC;EACA,IAAIC,QAAQ,GAAGF,OAAO,GAAG9f,CAAC,GAAG,EAAE,GAAG,IAAI,CAAA;;AAEtC;AACA,EAAA,MAAMigB,EAAE,GAAGF,EAAE,CAACz1B,MAAM,CAAC01B,QAAQ,CAAC,CAAA;;AAE9B;EACA,IAAIhgB,CAAC,KAAKigB,EAAE,EAAE;AACZ,IAAA,OAAO,CAACD,QAAQ,EAAEhgB,CAAC,CAAC,CAAA;AACtB,GAAA;;AAEA;EACAggB,QAAQ,IAAI,CAACC,EAAE,GAAGjgB,CAAC,IAAI,EAAE,GAAG,IAAI,CAAA;;AAEhC;AACA,EAAA,MAAMkgB,EAAE,GAAGH,EAAE,CAACz1B,MAAM,CAAC01B,QAAQ,CAAC,CAAA;EAC9B,IAAIC,EAAE,KAAKC,EAAE,EAAE;AACb,IAAA,OAAO,CAACF,QAAQ,EAAEC,EAAE,CAAC,CAAA;AACvB,GAAA;;AAEA;EACA,OAAO,CAACH,OAAO,GAAG3xB,IAAI,CAAC+M,GAAG,CAAC+kB,EAAE,EAAEC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE/xB,IAAI,CAACgN,GAAG,CAAC8kB,EAAE,EAAEC,EAAE,CAAC,CAAC,CAAA;AACnE,CAAA;;AAEA;AACA,SAASC,OAAOA,CAACj2B,EAAE,EAAEI,MAAM,EAAE;AAC3BJ,EAAAA,EAAE,IAAII,MAAM,GAAG,EAAE,GAAG,IAAI,CAAA;AAExB,EAAA,MAAMkS,CAAC,GAAG,IAAIrR,IAAI,CAACjB,EAAE,CAAC,CAAA;EAEtB,OAAO;AACLpC,IAAAA,IAAI,EAAE0U,CAAC,CAACG,cAAc,EAAE;AACxB5U,IAAAA,KAAK,EAAEyU,CAAC,CAAC4jB,WAAW,EAAE,GAAG,CAAC;AAC1Bp4B,IAAAA,GAAG,EAAEwU,CAAC,CAAC6jB,UAAU,EAAE;AACnB93B,IAAAA,IAAI,EAAEiU,CAAC,CAAC8jB,WAAW,EAAE;AACrB93B,IAAAA,MAAM,EAAEgU,CAAC,CAAC+jB,aAAa,EAAE;AACzB73B,IAAAA,MAAM,EAAE8T,CAAC,CAACgkB,aAAa,EAAE;AACzBhyB,IAAAA,WAAW,EAAEgO,CAAC,CAACikB,kBAAkB,EAAC;GACnC,CAAA;AACH,CAAA;;AAEA;AACA,SAASC,OAAOA,CAACjiB,GAAG,EAAEnU,MAAM,EAAEmD,IAAI,EAAE;EAClC,OAAOoyB,SAAS,CAACtxB,YAAY,CAACkQ,GAAG,CAAC,EAAEnU,MAAM,EAAEmD,IAAI,CAAC,CAAA;AACnD,CAAA;;AAEA;AACA,SAASkzB,UAAUA,CAAChB,IAAI,EAAE/V,GAAG,EAAE;AAC7B,EAAA,MAAMgX,IAAI,GAAGjB,IAAI,CAAC3f,CAAC;AACjBlY,IAAAA,IAAI,GAAG63B,IAAI,CAACzX,CAAC,CAACpgB,IAAI,GAAGqG,IAAI,CAACuU,KAAK,CAACkH,GAAG,CAACtE,KAAK,CAAC;IAC1Cvd,KAAK,GAAG43B,IAAI,CAACzX,CAAC,CAACngB,KAAK,GAAGoG,IAAI,CAACuU,KAAK,CAACkH,GAAG,CAAC/S,MAAM,CAAC,GAAG1I,IAAI,CAACuU,KAAK,CAACkH,GAAG,CAACrE,QAAQ,CAAC,GAAG,CAAC;AAC5E2C,IAAAA,CAAC,GAAG;MACF,GAAGyX,IAAI,CAACzX,CAAC;MACTpgB,IAAI;MACJC,KAAK;AACLC,MAAAA,GAAG,EACDmG,IAAI,CAAC+M,GAAG,CAACykB,IAAI,CAACzX,CAAC,CAAClgB,GAAG,EAAE0X,WAAW,CAAC5X,IAAI,EAAEC,KAAK,CAAC,CAAC,GAC9CoG,IAAI,CAACuU,KAAK,CAACkH,GAAG,CAACnE,IAAI,CAAC,GACpBtX,IAAI,CAACuU,KAAK,CAACkH,GAAG,CAACpE,KAAK,CAAC,GAAG,CAAA;KAC3B;AACDqb,IAAAA,WAAW,GAAGlP,QAAQ,CAACjc,UAAU,CAAC;AAChC4P,MAAAA,KAAK,EAAEsE,GAAG,CAACtE,KAAK,GAAGnX,IAAI,CAACuU,KAAK,CAACkH,GAAG,CAACtE,KAAK,CAAC;AACxCC,MAAAA,QAAQ,EAAEqE,GAAG,CAACrE,QAAQ,GAAGpX,IAAI,CAACuU,KAAK,CAACkH,GAAG,CAACrE,QAAQ,CAAC;AACjD1O,MAAAA,MAAM,EAAE+S,GAAG,CAAC/S,MAAM,GAAG1I,IAAI,CAACuU,KAAK,CAACkH,GAAG,CAAC/S,MAAM,CAAC;AAC3C2O,MAAAA,KAAK,EAAEoE,GAAG,CAACpE,KAAK,GAAGrX,IAAI,CAACuU,KAAK,CAACkH,GAAG,CAACpE,KAAK,CAAC;AACxCC,MAAAA,IAAI,EAAEmE,GAAG,CAACnE,IAAI,GAAGtX,IAAI,CAACuU,KAAK,CAACkH,GAAG,CAACnE,IAAI,CAAC;MACrCrB,KAAK,EAAEwF,GAAG,CAACxF,KAAK;MAChBzQ,OAAO,EAAEiW,GAAG,CAACjW,OAAO;MACpB+R,OAAO,EAAEkE,GAAG,CAAClE,OAAO;MACpBuH,YAAY,EAAErD,GAAG,CAACqD,YAAAA;AACpB,KAAC,CAAC,CAACiI,EAAE,CAAC,cAAc,CAAC;AACrB4K,IAAAA,OAAO,GAAGvxB,YAAY,CAAC2Z,CAAC,CAAC,CAAA;AAE3B,EAAA,IAAI,CAAChe,EAAE,EAAE8V,CAAC,CAAC,GAAG6f,SAAS,CAACC,OAAO,EAAEc,IAAI,EAAEjB,IAAI,CAAClyB,IAAI,CAAC,CAAA;EAEjD,IAAIozB,WAAW,KAAK,CAAC,EAAE;AACrB32B,IAAAA,EAAE,IAAI22B,WAAW,CAAA;AACjB;IACA7gB,CAAC,GAAG2f,IAAI,CAAClyB,IAAI,CAACnD,MAAM,CAACJ,EAAE,CAAC,CAAA;AAC1B,GAAA;EAEA,OAAO;IAAEA,EAAE;AAAE8V,IAAAA,CAAAA;GAAG,CAAA;AAClB,CAAA;;AAEA;AACA;AACA,SAAS8gB,mBAAmBA,CAAC10B,MAAM,EAAE20B,UAAU,EAAE52B,IAAI,EAAEE,MAAM,EAAE8oB,IAAI,EAAE+K,cAAc,EAAE;EACnF,MAAM;IAAEzqB,OAAO;AAAEhG,IAAAA,IAAAA;AAAK,GAAC,GAAGtD,IAAI,CAAA;AAC9B,EAAA,IAAKiC,MAAM,IAAIwG,MAAM,CAACC,IAAI,CAACzG,MAAM,CAAC,CAACa,MAAM,KAAK,CAAC,IAAK8zB,UAAU,EAAE;AAC9D,IAAA,MAAMC,kBAAkB,GAAGD,UAAU,IAAItzB,IAAI;AAC3CkyB,MAAAA,IAAI,GAAGjuB,QAAQ,CAACgE,UAAU,CAACtJ,MAAM,EAAE;AACjC,QAAA,GAAGjC,IAAI;AACPsD,QAAAA,IAAI,EAAEuzB,kBAAkB;AACxB9C,QAAAA,cAAAA;AACF,OAAC,CAAC,CAAA;IACJ,OAAOzqB,OAAO,GAAGksB,IAAI,GAAGA,IAAI,CAAClsB,OAAO,CAAChG,IAAI,CAAC,CAAA;AAC5C,GAAC,MAAM;AACL,IAAA,OAAOiE,QAAQ,CAACihB,OAAO,CACrB,IAAIzW,OAAO,CAAC,YAAY,EAAG,cAAaiX,IAAK,CAAA,qBAAA,EAAuB9oB,MAAO,CAAA,CAAC,CAC9E,CAAC,CAAA;AACH,GAAA;AACF,CAAA;;AAEA;AACA;AACA,SAAS42B,YAAYA,CAACxvB,EAAE,EAAEpH,MAAM,EAAEif,MAAM,GAAG,IAAI,EAAE;AAC/C,EAAA,OAAO7X,EAAE,CAAChH,OAAO,GACbmd,SAAS,CAACpa,MAAM,CAAC4C,MAAM,CAAC5C,MAAM,CAAC,OAAO,CAAC,EAAE;IACvC8b,MAAM;AACN9W,IAAAA,WAAW,EAAE,IAAA;GACd,CAAC,CAAC0W,wBAAwB,CAACzX,EAAE,EAAEpH,MAAM,CAAC,GACvC,IAAI,CAAA;AACV,CAAA;AAEA,SAAS8uB,SAASA,CAACnZ,CAAC,EAAEkhB,QAAQ,EAAEC,SAAS,EAAE;AACzC,EAAA,MAAMC,UAAU,GAAGphB,CAAC,CAACkI,CAAC,CAACpgB,IAAI,GAAG,IAAI,IAAIkY,CAAC,CAACkI,CAAC,CAACpgB,IAAI,GAAG,CAAC,CAAA;EAClD,IAAIogB,CAAC,GAAG,EAAE,CAAA;AACV,EAAA,IAAIkZ,UAAU,IAAIphB,CAAC,CAACkI,CAAC,CAACpgB,IAAI,IAAI,CAAC,EAAEogB,CAAC,IAAI,GAAG,CAAA;AACzCA,EAAAA,CAAC,IAAI/U,QAAQ,CAAC6M,CAAC,CAACkI,CAAC,CAACpgB,IAAI,EAAEs5B,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;AAC3C,EAAA,IAAID,SAAS,KAAK,MAAM,EAAE,OAAOjZ,CAAC,CAAA;AAClC,EAAA,IAAIgZ,QAAQ,EAAE;AACZhZ,IAAAA,CAAC,IAAI,GAAG,CAAA;IACRA,CAAC,IAAI/U,QAAQ,CAAC6M,CAAC,CAACkI,CAAC,CAACngB,KAAK,CAAC,CAAA;AACxB,IAAA,IAAIo5B,SAAS,KAAK,OAAO,EAAE,OAAOjZ,CAAC,CAAA;AACnCA,IAAAA,CAAC,IAAI,GAAG,CAAA;AACV,GAAC,MAAM;IACLA,CAAC,IAAI/U,QAAQ,CAAC6M,CAAC,CAACkI,CAAC,CAACngB,KAAK,CAAC,CAAA;AACxB,IAAA,IAAIo5B,SAAS,KAAK,OAAO,EAAE,OAAOjZ,CAAC,CAAA;AACrC,GAAA;EACAA,CAAC,IAAI/U,QAAQ,CAAC6M,CAAC,CAACkI,CAAC,CAAClgB,GAAG,CAAC,CAAA;AACtB,EAAA,OAAOkgB,CAAC,CAAA;AACV,CAAA;AAEA,SAAS4L,SAASA,CAChB9T,CAAC,EACDkhB,QAAQ,EACRhN,eAAe,EACfD,oBAAoB,EACpBG,aAAa,EACbiN,YAAY,EACZF,SAAS,EACT;AACA,EAAA,IAAIG,WAAW,GAAG,CAACpN,eAAe,IAAIlU,CAAC,CAACkI,CAAC,CAAC1Z,WAAW,KAAK,CAAC,IAAIwR,CAAC,CAACkI,CAAC,CAACxf,MAAM,KAAK,CAAC;AAC7Ewf,IAAAA,CAAC,GAAG,EAAE,CAAA;AACR,EAAA,QAAQiZ,SAAS;AACf,IAAA,KAAK,KAAK,CAAA;AACV,IAAA,KAAK,OAAO,CAAA;AACZ,IAAA,KAAK,MAAM;AACT,MAAA,MAAA;AACF,IAAA;MACEjZ,CAAC,IAAI/U,QAAQ,CAAC6M,CAAC,CAACkI,CAAC,CAAC3f,IAAI,CAAC,CAAA;MACvB,IAAI44B,SAAS,KAAK,MAAM,EAAE,MAAA;AAC1B,MAAA,IAAID,QAAQ,EAAE;AACZhZ,QAAAA,CAAC,IAAI,GAAG,CAAA;QACRA,CAAC,IAAI/U,QAAQ,CAAC6M,CAAC,CAACkI,CAAC,CAAC1f,MAAM,CAAC,CAAA;QACzB,IAAI24B,SAAS,KAAK,QAAQ,EAAE,MAAA;AAC5B,QAAA,IAAIG,WAAW,EAAE;AACfpZ,UAAAA,CAAC,IAAI,GAAG,CAAA;UACRA,CAAC,IAAI/U,QAAQ,CAAC6M,CAAC,CAACkI,CAAC,CAACxf,MAAM,CAAC,CAAA;AAC3B,SAAA;AACF,OAAC,MAAM;QACLwf,CAAC,IAAI/U,QAAQ,CAAC6M,CAAC,CAACkI,CAAC,CAAC1f,MAAM,CAAC,CAAA;QACzB,IAAI24B,SAAS,KAAK,QAAQ,EAAE,MAAA;AAC5B,QAAA,IAAIG,WAAW,EAAE;UACfpZ,CAAC,IAAI/U,QAAQ,CAAC6M,CAAC,CAACkI,CAAC,CAACxf,MAAM,CAAC,CAAA;AAC3B,SAAA;AACF,OAAA;MACA,IAAIy4B,SAAS,KAAK,QAAQ,EAAE,MAAA;AAC5B,MAAA,IAAIG,WAAW,KAAK,CAACrN,oBAAoB,IAAIjU,CAAC,CAACkI,CAAC,CAAC1Z,WAAW,KAAK,CAAC,CAAC,EAAE;AACnE0Z,QAAAA,CAAC,IAAI,GAAG,CAAA;QACRA,CAAC,IAAI/U,QAAQ,CAAC6M,CAAC,CAACkI,CAAC,CAAC1Z,WAAW,EAAE,CAAC,CAAC,CAAA;AACnC,OAAA;AACJ,GAAA;AAEA,EAAA,IAAI4lB,aAAa,EAAE;AACjB,IAAA,IAAIpU,CAAC,CAACqJ,aAAa,IAAIrJ,CAAC,CAAC1V,MAAM,KAAK,CAAC,IAAI,CAAC+2B,YAAY,EAAE;AACtDnZ,MAAAA,CAAC,IAAI,GAAG,CAAA;AACV,KAAC,MAAM,IAAIlI,CAAC,CAACA,CAAC,GAAG,CAAC,EAAE;AAClBkI,MAAAA,CAAC,IAAI,GAAG,CAAA;AACRA,MAAAA,CAAC,IAAI/U,QAAQ,CAAChF,IAAI,CAACuU,KAAK,CAAC,CAAC1C,CAAC,CAACA,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;AACpCkI,MAAAA,CAAC,IAAI,GAAG,CAAA;AACRA,MAAAA,CAAC,IAAI/U,QAAQ,CAAChF,IAAI,CAACuU,KAAK,CAAC,CAAC1C,CAAC,CAACA,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;AACtC,KAAC,MAAM;AACLkI,MAAAA,CAAC,IAAI,GAAG,CAAA;AACRA,MAAAA,CAAC,IAAI/U,QAAQ,CAAChF,IAAI,CAACuU,KAAK,CAAC1C,CAAC,CAACA,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;AACnCkI,MAAAA,CAAC,IAAI,GAAG,CAAA;AACRA,MAAAA,CAAC,IAAI/U,QAAQ,CAAChF,IAAI,CAACuU,KAAK,CAAC1C,CAAC,CAACA,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;AACrC,KAAA;AACF,GAAA;AAEA,EAAA,IAAIqhB,YAAY,EAAE;IAChBnZ,CAAC,IAAI,GAAG,GAAGlI,CAAC,CAACvS,IAAI,CAAC1D,QAAQ,GAAG,GAAG,CAAA;AAClC,GAAA;AACA,EAAA,OAAOme,CAAC,CAAA;AACV,CAAA;;AAEA;AACA,MAAMqZ,iBAAiB,GAAG;AACtBx5B,IAAAA,KAAK,EAAE,CAAC;AACRC,IAAAA,GAAG,EAAE,CAAC;AACNO,IAAAA,IAAI,EAAE,CAAC;AACPC,IAAAA,MAAM,EAAE,CAAC;AACTE,IAAAA,MAAM,EAAE,CAAC;AACT8F,IAAAA,WAAW,EAAE,CAAA;GACd;AACDgzB,EAAAA,qBAAqB,GAAG;AACtB7jB,IAAAA,UAAU,EAAE,CAAC;AACbxV,IAAAA,OAAO,EAAE,CAAC;AACVI,IAAAA,IAAI,EAAE,CAAC;AACPC,IAAAA,MAAM,EAAE,CAAC;AACTE,IAAAA,MAAM,EAAE,CAAC;AACT8F,IAAAA,WAAW,EAAE,CAAA;GACd;AACDizB,EAAAA,wBAAwB,GAAG;AACzBxkB,IAAAA,OAAO,EAAE,CAAC;AACV1U,IAAAA,IAAI,EAAE,CAAC;AACPC,IAAAA,MAAM,EAAE,CAAC;AACTE,IAAAA,MAAM,EAAE,CAAC;AACT8F,IAAAA,WAAW,EAAE,CAAA;GACd,CAAA;;AAEH;AACA,MAAM6iB,YAAY,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC;AACtFqQ,EAAAA,gBAAgB,GAAG,CACjB,UAAU,EACV,YAAY,EACZ,SAAS,EACT,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,aAAa,CACd;AACDC,EAAAA,mBAAmB,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAA;;AAEtF;AACA,SAAS7O,aAAaA,CAACvrB,IAAI,EAAE;AAC3B,EAAA,MAAM2c,UAAU,GAAG;AACjBpc,IAAAA,IAAI,EAAE,MAAM;AACZwd,IAAAA,KAAK,EAAE,MAAM;AACbvd,IAAAA,KAAK,EAAE,OAAO;AACd8O,IAAAA,MAAM,EAAE,OAAO;AACf7O,IAAAA,GAAG,EAAE,KAAK;AACVyd,IAAAA,IAAI,EAAE,KAAK;AACXld,IAAAA,IAAI,EAAE,MAAM;AACZ6b,IAAAA,KAAK,EAAE,MAAM;AACb5b,IAAAA,MAAM,EAAE,QAAQ;AAChBmL,IAAAA,OAAO,EAAE,QAAQ;AACjB+V,IAAAA,OAAO,EAAE,SAAS;AAClBnE,IAAAA,QAAQ,EAAE,SAAS;AACnB7c,IAAAA,MAAM,EAAE,QAAQ;AAChBgd,IAAAA,OAAO,EAAE,QAAQ;AACjBlX,IAAAA,WAAW,EAAE,aAAa;AAC1Bye,IAAAA,YAAY,EAAE,aAAa;AAC3B9kB,IAAAA,OAAO,EAAE,SAAS;AAClBgP,IAAAA,QAAQ,EAAE,SAAS;AACnByqB,IAAAA,UAAU,EAAE,YAAY;AACxBC,IAAAA,WAAW,EAAE,YAAY;AACzBC,IAAAA,WAAW,EAAE,YAAY;AACzBC,IAAAA,QAAQ,EAAE,UAAU;AACpBC,IAAAA,SAAS,EAAE,UAAU;AACrB/kB,IAAAA,OAAO,EAAE,SAAA;AACX,GAAC,CAAC1V,IAAI,CAACqQ,WAAW,EAAE,CAAC,CAAA;EAErB,IAAI,CAACsM,UAAU,EAAE,MAAM,IAAI5c,gBAAgB,CAACC,IAAI,CAAC,CAAA;AAEjD,EAAA,OAAO2c,UAAU,CAAA;AACnB,CAAA;AAEA,SAAS+d,2BAA2BA,CAAC16B,IAAI,EAAE;AACzC,EAAA,QAAQA,IAAI,CAACqQ,WAAW,EAAE;AACxB,IAAA,KAAK,cAAc,CAAA;AACnB,IAAA,KAAK,eAAe;AAClB,MAAA,OAAO,cAAc,CAAA;AACvB,IAAA,KAAK,iBAAiB,CAAA;AACtB,IAAA,KAAK,kBAAkB;AACrB,MAAA,OAAO,iBAAiB,CAAA;AAC1B,IAAA,KAAK,eAAe,CAAA;AACpB,IAAA,KAAK,gBAAgB;AACnB,MAAA,OAAO,eAAe,CAAA;AACxB,IAAA;MACE,OAAOkb,aAAa,CAACvrB,IAAI,CAAC,CAAA;AAC9B,GAAA;AACF,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS26B,kBAAkBA,CAACz0B,IAAI,EAAE;EAChC,IAAI00B,YAAY,KAAKx2B,SAAS,EAAE;AAC9Bw2B,IAAAA,YAAY,GAAGntB,QAAQ,CAAC4G,GAAG,EAAE,CAAA;AAC/B,GAAA;;AAEA;AACA;AACA,EAAA,IAAInO,IAAI,CAAC5D,IAAI,KAAK,MAAM,EAAE;AACxB,IAAA,OAAO4D,IAAI,CAACnD,MAAM,CAAC63B,YAAY,CAAC,CAAA;AAClC,GAAA;AACA,EAAA,MAAM32B,QAAQ,GAAGiC,IAAI,CAAC3D,IAAI,CAAA;AAC1B,EAAA,IAAIs4B,WAAW,GAAGC,oBAAoB,CAAC32B,GAAG,CAACF,QAAQ,CAAC,CAAA;EACpD,IAAI42B,WAAW,KAAKz2B,SAAS,EAAE;AAC7By2B,IAAAA,WAAW,GAAG30B,IAAI,CAACnD,MAAM,CAAC63B,YAAY,CAAC,CAAA;AACvCE,IAAAA,oBAAoB,CAACv2B,GAAG,CAACN,QAAQ,EAAE42B,WAAW,CAAC,CAAA;AACjD,GAAA;AACA,EAAA,OAAOA,WAAW,CAAA;AACpB,CAAA;;AAEA;AACA;AACA;AACA,SAASE,OAAOA,CAAC7jB,GAAG,EAAEtU,IAAI,EAAE;EAC1B,MAAMsD,IAAI,GAAGqL,aAAa,CAAC3O,IAAI,CAACsD,IAAI,EAAEuH,QAAQ,CAACgE,WAAW,CAAC,CAAA;AAC3D,EAAA,IAAI,CAACvL,IAAI,CAAChD,OAAO,EAAE;IACjB,OAAOiH,QAAQ,CAACihB,OAAO,CAAC4M,eAAe,CAAC9xB,IAAI,CAAC,CAAC,CAAA;AAChD,GAAA;AAEA,EAAA,MAAMsE,GAAG,GAAG3B,MAAM,CAACsF,UAAU,CAACvL,IAAI,CAAC,CAAA;EAEnC,IAAID,EAAE,EAAE8V,CAAC,CAAA;;AAET;AACA,EAAA,IAAI,CAAC5S,WAAW,CAACqR,GAAG,CAAC3W,IAAI,CAAC,EAAE;AAC1B,IAAA,KAAK,MAAMqc,CAAC,IAAIkN,YAAY,EAAE;AAC5B,MAAA,IAAIjkB,WAAW,CAACqR,GAAG,CAAC0F,CAAC,CAAC,CAAC,EAAE;AACvB1F,QAAAA,GAAG,CAAC0F,CAAC,CAAC,GAAGod,iBAAiB,CAACpd,CAAC,CAAC,CAAA;AAC/B,OAAA;AACF,KAAA;IAEA,MAAMwO,OAAO,GAAGpT,uBAAuB,CAACd,GAAG,CAAC,IAAIkB,kBAAkB,CAAClB,GAAG,CAAC,CAAA;AACvE,IAAA,IAAIkU,OAAO,EAAE;AACX,MAAA,OAAOjhB,QAAQ,CAACihB,OAAO,CAACA,OAAO,CAAC,CAAA;AAClC,KAAA;AAEA,IAAA,MAAM4P,YAAY,GAAGL,kBAAkB,CAACz0B,IAAI,CAAC,CAAA;AAC7C,IAAA,CAACvD,EAAE,EAAE8V,CAAC,CAAC,GAAG0gB,OAAO,CAACjiB,GAAG,EAAE8jB,YAAY,EAAE90B,IAAI,CAAC,CAAA;AAC5C,GAAC,MAAM;AACLvD,IAAAA,EAAE,GAAG8K,QAAQ,CAAC4G,GAAG,EAAE,CAAA;AACrB,GAAA;EAEA,OAAO,IAAIlK,QAAQ,CAAC;IAAExH,EAAE;IAAEuD,IAAI;IAAEsE,GAAG;AAAEiO,IAAAA,CAAAA;AAAE,GAAC,CAAC,CAAA;AAC3C,CAAA;AAEA,SAASwiB,YAAYA,CAAC5Z,KAAK,EAAEE,GAAG,EAAE3e,IAAI,EAAE;AACtC,EAAA,MAAMwY,KAAK,GAAGvV,WAAW,CAACjD,IAAI,CAACwY,KAAK,CAAC,GAAG,IAAI,GAAGxY,IAAI,CAACwY,KAAK;AACvDJ,IAAAA,QAAQ,GAAGnV,WAAW,CAACjD,IAAI,CAACoY,QAAQ,CAAC,GAAG,OAAO,GAAGpY,IAAI,CAACoY,QAAQ;AAC/DlY,IAAAA,MAAM,GAAGA,CAAC6d,CAAC,EAAE3gB,IAAI,KAAK;MACpB2gB,CAAC,GAAGhV,OAAO,CAACgV,CAAC,EAAEvF,KAAK,IAAIxY,IAAI,CAACs4B,SAAS,GAAG,CAAC,GAAG,CAAC,EAAEt4B,IAAI,CAACs4B,SAAS,GAAG,OAAO,GAAGlgB,QAAQ,CAAC,CAAA;AACpF,MAAA,MAAM8c,SAAS,GAAGvW,GAAG,CAAC/W,GAAG,CAACyE,KAAK,CAACrM,IAAI,CAAC,CAAC2N,YAAY,CAAC3N,IAAI,CAAC,CAAA;AACxD,MAAA,OAAOk1B,SAAS,CAACh1B,MAAM,CAAC6d,CAAC,EAAE3gB,IAAI,CAAC,CAAA;KACjC;IACDuzB,MAAM,GAAIvzB,IAAI,IAAK;MACjB,IAAI4C,IAAI,CAACs4B,SAAS,EAAE;QAClB,IAAI,CAAC3Z,GAAG,CAACqO,OAAO,CAACvO,KAAK,EAAErhB,IAAI,CAAC,EAAE;UAC7B,OAAOuhB,GAAG,CAACkO,OAAO,CAACzvB,IAAI,CAAC,CAAC2vB,IAAI,CAACtO,KAAK,CAACoO,OAAO,CAACzvB,IAAI,CAAC,EAAEA,IAAI,CAAC,CAACmE,GAAG,CAACnE,IAAI,CAAC,CAAA;SACnE,MAAM,OAAO,CAAC,CAAA;AACjB,OAAC,MAAM;AACL,QAAA,OAAOuhB,GAAG,CAACoO,IAAI,CAACtO,KAAK,EAAErhB,IAAI,CAAC,CAACmE,GAAG,CAACnE,IAAI,CAAC,CAAA;AACxC,OAAA;KACD,CAAA;EAEH,IAAI4C,IAAI,CAAC5C,IAAI,EAAE;AACb,IAAA,OAAO8C,MAAM,CAACywB,MAAM,CAAC3wB,IAAI,CAAC5C,IAAI,CAAC,EAAE4C,IAAI,CAAC5C,IAAI,CAAC,CAAA;AAC7C,GAAA;AAEA,EAAA,KAAK,MAAMA,IAAI,IAAI4C,IAAI,CAACkb,KAAK,EAAE;AAC7B,IAAA,MAAM/Q,KAAK,GAAGwmB,MAAM,CAACvzB,IAAI,CAAC,CAAA;IAC1B,IAAI4G,IAAI,CAACC,GAAG,CAACkG,KAAK,CAAC,IAAI,CAAC,EAAE;AACxB,MAAA,OAAOjK,MAAM,CAACiK,KAAK,EAAE/M,IAAI,CAAC,CAAA;AAC5B,KAAA;AACF,GAAA;EACA,OAAO8C,MAAM,CAACue,KAAK,GAAGE,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE3e,IAAI,CAACkb,KAAK,CAAClb,IAAI,CAACkb,KAAK,CAACpY,MAAM,GAAG,CAAC,CAAC,CAAC,CAAA;AACxE,CAAA;AAEA,SAASy1B,QAAQA,CAACC,OAAO,EAAE;EACzB,IAAIx4B,IAAI,GAAG,EAAE;IACXy4B,IAAI,CAAA;AACN,EAAA,IAAID,OAAO,CAAC11B,MAAM,GAAG,CAAC,IAAI,OAAO01B,OAAO,CAACA,OAAO,CAAC11B,MAAM,GAAG,CAAC,CAAC,KAAK,QAAQ,EAAE;IACzE9C,IAAI,GAAGw4B,OAAO,CAACA,OAAO,CAAC11B,MAAM,GAAG,CAAC,CAAC,CAAA;AAClC21B,IAAAA,IAAI,GAAGtiB,KAAK,CAACkB,IAAI,CAACmhB,OAAO,CAAC,CAAClZ,KAAK,CAAC,CAAC,EAAEkZ,OAAO,CAAC11B,MAAM,GAAG,CAAC,CAAC,CAAA;AACzD,GAAC,MAAM;AACL21B,IAAAA,IAAI,GAAGtiB,KAAK,CAACkB,IAAI,CAACmhB,OAAO,CAAC,CAAA;AAC5B,GAAA;AACA,EAAA,OAAO,CAACx4B,IAAI,EAAEy4B,IAAI,CAAC,CAAA;AACrB,CAAA;;AAEA;AACA;AACA;AACA,IAAIT,YAAY,CAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,oBAAoB,GAAG,IAAI/2B,GAAG,EAAE,CAAA;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,MAAMoG,QAAQ,CAAC;AAC5B;AACF;AACA;EACE1K,WAAWA,CAACyrB,MAAM,EAAE;IAClB,MAAMhlB,IAAI,GAAGglB,MAAM,CAAChlB,IAAI,IAAIuH,QAAQ,CAACgE,WAAW,CAAA;AAEhD,IAAA,IAAI2Z,OAAO,GACTF,MAAM,CAACE,OAAO,KACblP,MAAM,CAACxV,KAAK,CAACwkB,MAAM,CAACvoB,EAAE,CAAC,GAAG,IAAIgS,OAAO,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC,KAC9D,CAACzO,IAAI,CAAChD,OAAO,GAAG80B,eAAe,CAAC9xB,IAAI,CAAC,GAAG,IAAI,CAAC,CAAA;AAChD;AACJ;AACA;AACI,IAAA,IAAI,CAACvD,EAAE,GAAGkD,WAAW,CAACqlB,MAAM,CAACvoB,EAAE,CAAC,GAAG8K,QAAQ,CAAC4G,GAAG,EAAE,GAAG6W,MAAM,CAACvoB,EAAE,CAAA;IAE7D,IAAIge,CAAC,GAAG,IAAI;AACVlI,MAAAA,CAAC,GAAG,IAAI,CAAA;IACV,IAAI,CAAC2S,OAAO,EAAE;MACZ,MAAMkQ,SAAS,GAAGpQ,MAAM,CAACmN,GAAG,IAAInN,MAAM,CAACmN,GAAG,CAAC11B,EAAE,KAAK,IAAI,CAACA,EAAE,IAAIuoB,MAAM,CAACmN,GAAG,CAACnyB,IAAI,CAAClD,MAAM,CAACkD,IAAI,CAAC,CAAA;AAEzF,MAAA,IAAIo1B,SAAS,EAAE;AACb,QAAA,CAAC3a,CAAC,EAAElI,CAAC,CAAC,GAAG,CAACyS,MAAM,CAACmN,GAAG,CAAC1X,CAAC,EAAEuK,MAAM,CAACmN,GAAG,CAAC5f,CAAC,CAAC,CAAA;AACvC,OAAC,MAAM;AACL;AACA;QACA,MAAM8iB,EAAE,GAAG3pB,QAAQ,CAACsZ,MAAM,CAACzS,CAAC,CAAC,IAAI,CAACyS,MAAM,CAACmN,GAAG,GAAGnN,MAAM,CAACzS,CAAC,GAAGvS,IAAI,CAACnD,MAAM,CAAC,IAAI,CAACJ,EAAE,CAAC,CAAA;QAC9Ege,CAAC,GAAGiY,OAAO,CAAC,IAAI,CAACj2B,EAAE,EAAE44B,EAAE,CAAC,CAAA;AACxBnQ,QAAAA,OAAO,GAAGlP,MAAM,CAACxV,KAAK,CAACia,CAAC,CAACpgB,IAAI,CAAC,GAAG,IAAIoU,OAAO,CAAC,eAAe,CAAC,GAAG,IAAI,CAAA;AACpEgM,QAAAA,CAAC,GAAGyK,OAAO,GAAG,IAAI,GAAGzK,CAAC,CAAA;AACtBlI,QAAAA,CAAC,GAAG2S,OAAO,GAAG,IAAI,GAAGmQ,EAAE,CAAA;AACzB,OAAA;AACF,KAAA;;AAEA;AACJ;AACA;IACI,IAAI,CAACC,KAAK,GAAGt1B,IAAI,CAAA;AACjB;AACJ;AACA;IACI,IAAI,CAACsE,GAAG,GAAG0gB,MAAM,CAAC1gB,GAAG,IAAI3B,MAAM,CAAC5C,MAAM,EAAE,CAAA;AACxC;AACJ;AACA;IACI,IAAI,CAACmlB,OAAO,GAAGA,OAAO,CAAA;AACtB;AACJ;AACA;IACI,IAAI,CAAC3U,QAAQ,GAAG,IAAI,CAAA;AACpB;AACJ;AACA;IACI,IAAI,CAAC0hB,aAAa,GAAG,IAAI,CAAA;AACzB;AACJ;AACA;IACI,IAAI,CAACxX,CAAC,GAAGA,CAAC,CAAA;AACV;AACJ;AACA;IACI,IAAI,CAAClI,CAAC,GAAGA,CAAC,CAAA;AACV;AACJ;AACA;IACI,IAAI,CAACgjB,eAAe,GAAG,IAAI,CAAA;AAC7B,GAAA;;AAEA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAOpnB,GAAGA,GAAG;AACX,IAAA,OAAO,IAAIlK,QAAQ,CAAC,EAAE,CAAC,CAAA;AACzB,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOyb,KAAKA,GAAG;IACb,MAAM,CAAChjB,IAAI,EAAEy4B,IAAI,CAAC,GAAGF,QAAQ,CAACO,SAAS,CAAC;AACtC,MAAA,CAACn7B,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEO,IAAI,EAAEC,MAAM,EAAEE,MAAM,EAAE8F,WAAW,CAAC,GAAGo0B,IAAI,CAAA;AAC9D,IAAA,OAAON,OAAO,CAAC;MAAEx6B,IAAI;MAAEC,KAAK;MAAEC,GAAG;MAAEO,IAAI;MAAEC,MAAM;MAAEE,MAAM;AAAE8F,MAAAA,WAAAA;KAAa,EAAErE,IAAI,CAAC,CAAA;AAC/E,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOwH,GAAGA,GAAG;IACX,MAAM,CAACxH,IAAI,EAAEy4B,IAAI,CAAC,GAAGF,QAAQ,CAACO,SAAS,CAAC;AACtC,MAAA,CAACn7B,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEO,IAAI,EAAEC,MAAM,EAAEE,MAAM,EAAE8F,WAAW,CAAC,GAAGo0B,IAAI,CAAA;AAE9Dz4B,IAAAA,IAAI,CAACsD,IAAI,GAAG8K,eAAe,CAACC,WAAW,CAAA;AACvC,IAAA,OAAO8pB,OAAO,CAAC;MAAEx6B,IAAI;MAAEC,KAAK;MAAEC,GAAG;MAAEO,IAAI;MAAEC,MAAM;MAAEE,MAAM;AAAE8F,MAAAA,WAAAA;KAAa,EAAErE,IAAI,CAAC,CAAA;AAC/E,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAO+4B,UAAUA,CAACj3B,IAAI,EAAE6E,OAAO,GAAG,EAAE,EAAE;AACpC,IAAA,MAAM5G,EAAE,GAAG+V,MAAM,CAAChU,IAAI,CAAC,GAAGA,IAAI,CAACyoB,OAAO,EAAE,GAAG1mB,GAAG,CAAA;AAC9C,IAAA,IAAIyV,MAAM,CAACxV,KAAK,CAAC/D,EAAE,CAAC,EAAE;AACpB,MAAA,OAAOwH,QAAQ,CAACihB,OAAO,CAAC,eAAe,CAAC,CAAA;AAC1C,KAAA;IAEA,MAAMwQ,SAAS,GAAGrqB,aAAa,CAAChI,OAAO,CAACrD,IAAI,EAAEuH,QAAQ,CAACgE,WAAW,CAAC,CAAA;AACnE,IAAA,IAAI,CAACmqB,SAAS,CAAC14B,OAAO,EAAE;MACtB,OAAOiH,QAAQ,CAACihB,OAAO,CAAC4M,eAAe,CAAC4D,SAAS,CAAC,CAAC,CAAA;AACrD,KAAA;IAEA,OAAO,IAAIzxB,QAAQ,CAAC;AAClBxH,MAAAA,EAAE,EAAEA,EAAE;AACNuD,MAAAA,IAAI,EAAE01B,SAAS;AACfpxB,MAAAA,GAAG,EAAE3B,MAAM,CAACsF,UAAU,CAAC5E,OAAO,CAAA;AAChC,KAAC,CAAC,CAAA;AACJ,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO+hB,UAAUA,CAAC5F,YAAY,EAAEnc,OAAO,GAAG,EAAE,EAAE;AAC5C,IAAA,IAAI,CAACqI,QAAQ,CAAC8T,YAAY,CAAC,EAAE;MAC3B,MAAM,IAAIzlB,oBAAoB,CAC3B,CAAA,sDAAA,EAAwD,OAAOylB,YAAa,CAAA,YAAA,EAAcA,YAAa,CAAA,CAC1G,CAAC,CAAA;KACF,MAAM,IAAIA,YAAY,GAAG,CAACqS,QAAQ,IAAIrS,YAAY,GAAGqS,QAAQ,EAAE;AAC9D;AACA,MAAA,OAAO5tB,QAAQ,CAACihB,OAAO,CAAC,wBAAwB,CAAC,CAAA;AACnD,KAAC,MAAM;MACL,OAAO,IAAIjhB,QAAQ,CAAC;AAClBxH,QAAAA,EAAE,EAAE+iB,YAAY;QAChBxf,IAAI,EAAEqL,aAAa,CAAChI,OAAO,CAACrD,IAAI,EAAEuH,QAAQ,CAACgE,WAAW,CAAC;AACvDjH,QAAAA,GAAG,EAAE3B,MAAM,CAACsF,UAAU,CAAC5E,OAAO,CAAA;AAChC,OAAC,CAAC,CAAA;AACJ,KAAA;AACF,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOsyB,WAAWA,CAAC1d,OAAO,EAAE5U,OAAO,GAAG,EAAE,EAAE;AACxC,IAAA,IAAI,CAACqI,QAAQ,CAACuM,OAAO,CAAC,EAAE;AACtB,MAAA,MAAM,IAAIle,oBAAoB,CAAC,wCAAwC,CAAC,CAAA;AAC1E,KAAC,MAAM;MACL,OAAO,IAAIkK,QAAQ,CAAC;QAClBxH,EAAE,EAAEwb,OAAO,GAAG,IAAI;QAClBjY,IAAI,EAAEqL,aAAa,CAAChI,OAAO,CAACrD,IAAI,EAAEuH,QAAQ,CAACgE,WAAW,CAAC;AACvDjH,QAAAA,GAAG,EAAE3B,MAAM,CAACsF,UAAU,CAAC5E,OAAO,CAAA;AAChC,OAAC,CAAC,CAAA;AACJ,KAAA;AACF,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO4E,UAAUA,CAAC+I,GAAG,EAAEtU,IAAI,GAAG,EAAE,EAAE;AAChCsU,IAAAA,GAAG,GAAGA,GAAG,IAAI,EAAE,CAAA;IACf,MAAM0kB,SAAS,GAAGrqB,aAAa,CAAC3O,IAAI,CAACsD,IAAI,EAAEuH,QAAQ,CAACgE,WAAW,CAAC,CAAA;AAChE,IAAA,IAAI,CAACmqB,SAAS,CAAC14B,OAAO,EAAE;MACtB,OAAOiH,QAAQ,CAACihB,OAAO,CAAC4M,eAAe,CAAC4D,SAAS,CAAC,CAAC,CAAA;AACrD,KAAA;AAEA,IAAA,MAAMpxB,GAAG,GAAG3B,MAAM,CAACsF,UAAU,CAACvL,IAAI,CAAC,CAAA;AACnC,IAAA,MAAM+Z,UAAU,GAAGF,eAAe,CAACvF,GAAG,EAAEwjB,2BAA2B,CAAC,CAAA;IACpE,MAAM;MAAEvkB,kBAAkB;AAAEH,MAAAA,WAAAA;AAAY,KAAC,GAAGiB,mBAAmB,CAAC0F,UAAU,EAAEnS,GAAG,CAAC,CAAA;AAEhF,IAAA,MAAMsxB,KAAK,GAAGruB,QAAQ,CAAC4G,GAAG,EAAE;AAC1B2mB,MAAAA,YAAY,GAAG,CAACn1B,WAAW,CAACjD,IAAI,CAAC+zB,cAAc,CAAC,GAC5C/zB,IAAI,CAAC+zB,cAAc,GACnBiF,SAAS,CAAC74B,MAAM,CAAC+4B,KAAK,CAAC;AAC3BC,MAAAA,eAAe,GAAG,CAACl2B,WAAW,CAAC8W,UAAU,CAACjH,OAAO,CAAC;AAClDsmB,MAAAA,kBAAkB,GAAG,CAACn2B,WAAW,CAAC8W,UAAU,CAACpc,IAAI,CAAC;AAClD07B,MAAAA,gBAAgB,GAAG,CAACp2B,WAAW,CAAC8W,UAAU,CAACnc,KAAK,CAAC,IAAI,CAACqF,WAAW,CAAC8W,UAAU,CAAClc,GAAG,CAAC;MACjFy7B,cAAc,GAAGF,kBAAkB,IAAIC,gBAAgB;AACvDE,MAAAA,eAAe,GAAGxf,UAAU,CAACtG,QAAQ,IAAIsG,UAAU,CAACvG,UAAU,CAAA;;AAEhE;AACA;AACA;AACA;AACA;;AAEA,IAAA,IAAI,CAAC8lB,cAAc,IAAIH,eAAe,KAAKI,eAAe,EAAE;AAC1D,MAAA,MAAM,IAAIr8B,6BAA6B,CACrC,qEACF,CAAC,CAAA;AACH,KAAA;IAEA,IAAIm8B,gBAAgB,IAAIF,eAAe,EAAE;AACvC,MAAA,MAAM,IAAIj8B,6BAA6B,CAAC,wCAAwC,CAAC,CAAA;AACnF,KAAA;IAEA,MAAMs8B,WAAW,GAAGD,eAAe,IAAKxf,UAAU,CAAC/b,OAAO,IAAI,CAACs7B,cAAe,CAAA;;AAE9E;AACA,IAAA,IAAIpe,KAAK;MACPue,aAAa;AACbC,MAAAA,MAAM,GAAG1D,OAAO,CAACkD,KAAK,EAAEd,YAAY,CAAC,CAAA;AACvC,IAAA,IAAIoB,WAAW,EAAE;AACfte,MAAAA,KAAK,GAAGqc,gBAAgB,CAAA;AACxBkC,MAAAA,aAAa,GAAGpC,qBAAqB,CAAA;MACrCqC,MAAM,GAAGrmB,eAAe,CAACqmB,MAAM,EAAEnmB,kBAAkB,EAAEH,WAAW,CAAC,CAAA;KAClE,MAAM,IAAI+lB,eAAe,EAAE;AAC1Bje,MAAAA,KAAK,GAAGsc,mBAAmB,CAAA;AAC3BiC,MAAAA,aAAa,GAAGnC,wBAAwB,CAAA;AACxCoC,MAAAA,MAAM,GAAGzlB,kBAAkB,CAACylB,MAAM,CAAC,CAAA;AACrC,KAAC,MAAM;AACLxe,MAAAA,KAAK,GAAGgM,YAAY,CAAA;AACpBuS,MAAAA,aAAa,GAAGrC,iBAAiB,CAAA;AACnC,KAAA;;AAEA;IACA,IAAIuC,UAAU,GAAG,KAAK,CAAA;AACtB,IAAA,KAAK,MAAM3f,CAAC,IAAIkB,KAAK,EAAE;AACrB,MAAA,MAAM9D,CAAC,GAAG2C,UAAU,CAACC,CAAC,CAAC,CAAA;AACvB,MAAA,IAAI,CAAC/W,WAAW,CAACmU,CAAC,CAAC,EAAE;AACnBuiB,QAAAA,UAAU,GAAG,IAAI,CAAA;OAClB,MAAM,IAAIA,UAAU,EAAE;AACrB5f,QAAAA,UAAU,CAACC,CAAC,CAAC,GAAGyf,aAAa,CAACzf,CAAC,CAAC,CAAA;AAClC,OAAC,MAAM;AACLD,QAAAA,UAAU,CAACC,CAAC,CAAC,GAAG0f,MAAM,CAAC1f,CAAC,CAAC,CAAA;AAC3B,OAAA;AACF,KAAA;;AAEA;IACA,MAAM4f,kBAAkB,GAAGJ,WAAW,GAChC5kB,kBAAkB,CAACmF,UAAU,EAAExG,kBAAkB,EAAEH,WAAW,CAAC,GAC/D+lB,eAAe,GACfjkB,qBAAqB,CAAC6E,UAAU,CAAC,GACjC3E,uBAAuB,CAAC2E,UAAU,CAAC;AACvCyO,MAAAA,OAAO,GAAGoR,kBAAkB,IAAIpkB,kBAAkB,CAACuE,UAAU,CAAC,CAAA;AAEhE,IAAA,IAAIyO,OAAO,EAAE;AACX,MAAA,OAAOjhB,QAAQ,CAACihB,OAAO,CAACA,OAAO,CAAC,CAAA;AAClC,KAAA;;AAEA;IACA,MAAMqR,SAAS,GAAGL,WAAW,GACvB5lB,eAAe,CAACmG,UAAU,EAAExG,kBAAkB,EAAEH,WAAW,CAAC,GAC5D+lB,eAAe,GACfhlB,kBAAkB,CAAC4F,UAAU,CAAC,GAC9BA,UAAU;AACd,MAAA,CAAC+f,OAAO,EAAEC,WAAW,CAAC,GAAGxD,OAAO,CAACsD,SAAS,EAAEzB,YAAY,EAAEY,SAAS,CAAC;MACpExD,IAAI,GAAG,IAAIjuB,QAAQ,CAAC;AAClBxH,QAAAA,EAAE,EAAE+5B,OAAO;AACXx2B,QAAAA,IAAI,EAAE01B,SAAS;AACfnjB,QAAAA,CAAC,EAAEkkB,WAAW;AACdnyB,QAAAA,GAAAA;AACF,OAAC,CAAC,CAAA;;AAEJ;AACA,IAAA,IAAImS,UAAU,CAAC/b,OAAO,IAAIs7B,cAAc,IAAIhlB,GAAG,CAACtW,OAAO,KAAKw3B,IAAI,CAACx3B,OAAO,EAAE;AACxE,MAAA,OAAOuJ,QAAQ,CAACihB,OAAO,CACrB,oBAAoB,EACnB,CAAsCzO,oCAAAA,EAAAA,UAAU,CAAC/b,OAAQ,kBAAiBw3B,IAAI,CAAC9L,KAAK,EAAG,EAC1F,CAAC,CAAA;AACH,KAAA;AAEA,IAAA,IAAI,CAAC8L,IAAI,CAACl1B,OAAO,EAAE;AACjB,MAAA,OAAOiH,QAAQ,CAACihB,OAAO,CAACgN,IAAI,CAAChN,OAAO,CAAC,CAAA;AACvC,KAAA;AAEA,IAAA,OAAOgN,IAAI,CAAA;AACb,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOzM,OAAOA,CAACC,IAAI,EAAEhpB,IAAI,GAAG,EAAE,EAAE;IAC9B,MAAM,CAAC0nB,IAAI,EAAEkP,UAAU,CAAC,GAAG1Q,YAAY,CAAC8C,IAAI,CAAC,CAAA;IAC7C,OAAO2N,mBAAmB,CAACjP,IAAI,EAAEkP,UAAU,EAAE52B,IAAI,EAAE,UAAU,EAAEgpB,IAAI,CAAC,CAAA;AACtE,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOgR,WAAWA,CAAChR,IAAI,EAAEhpB,IAAI,GAAG,EAAE,EAAE;IAClC,MAAM,CAAC0nB,IAAI,EAAEkP,UAAU,CAAC,GAAGzQ,gBAAgB,CAAC6C,IAAI,CAAC,CAAA;IACjD,OAAO2N,mBAAmB,CAACjP,IAAI,EAAEkP,UAAU,EAAE52B,IAAI,EAAE,UAAU,EAAEgpB,IAAI,CAAC,CAAA;AACtE,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOiR,QAAQA,CAACjR,IAAI,EAAEhpB,IAAI,GAAG,EAAE,EAAE;IAC/B,MAAM,CAAC0nB,IAAI,EAAEkP,UAAU,CAAC,GAAGxQ,aAAa,CAAC4C,IAAI,CAAC,CAAA;IAC9C,OAAO2N,mBAAmB,CAACjP,IAAI,EAAEkP,UAAU,EAAE52B,IAAI,EAAE,MAAM,EAAEA,IAAI,CAAC,CAAA;AAClE,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOk6B,UAAUA,CAAClR,IAAI,EAAErL,GAAG,EAAE3d,IAAI,GAAG,EAAE,EAAE;IACtC,IAAIiD,WAAW,CAAC+lB,IAAI,CAAC,IAAI/lB,WAAW,CAAC0a,GAAG,CAAC,EAAE;AACzC,MAAA,MAAM,IAAItgB,oBAAoB,CAAC,kDAAkD,CAAC,CAAA;AACpF,KAAA;IAEA,MAAM;AAAEyD,QAAAA,MAAM,GAAG,IAAI;AAAEgG,QAAAA,eAAe,GAAG,IAAA;AAAK,OAAC,GAAG9G,IAAI;AACpDm6B,MAAAA,WAAW,GAAGl0B,MAAM,CAACwE,QAAQ,CAAC;QAC5B3J,MAAM;QACNgG,eAAe;AACf6D,QAAAA,WAAW,EAAE,IAAA;AACf,OAAC,CAAC;AACF,MAAA,CAAC+c,IAAI,EAAEkP,UAAU,EAAE7C,cAAc,EAAEvL,OAAO,CAAC,GAAGyM,eAAe,CAACkF,WAAW,EAAEnR,IAAI,EAAErL,GAAG,CAAC,CAAA;AACvF,IAAA,IAAI6K,OAAO,EAAE;AACX,MAAA,OAAOjhB,QAAQ,CAACihB,OAAO,CAACA,OAAO,CAAC,CAAA;AAClC,KAAC,MAAM;AACL,MAAA,OAAOmO,mBAAmB,CAACjP,IAAI,EAAEkP,UAAU,EAAE52B,IAAI,EAAG,CAAA,OAAA,EAAS2d,GAAI,CAAC,CAAA,EAAEqL,IAAI,EAAE+K,cAAc,CAAC,CAAA;AAC3F,KAAA;AACF,GAAA;;AAEA;AACF;AACA;EACE,OAAOqG,UAAUA,CAACpR,IAAI,EAAErL,GAAG,EAAE3d,IAAI,GAAG,EAAE,EAAE;IACtC,OAAOuH,QAAQ,CAAC2yB,UAAU,CAAClR,IAAI,EAAErL,GAAG,EAAE3d,IAAI,CAAC,CAAA;AAC7C,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOq6B,OAAOA,CAACrR,IAAI,EAAEhpB,IAAI,GAAG,EAAE,EAAE;IAC9B,MAAM,CAAC0nB,IAAI,EAAEkP,UAAU,CAAC,GAAGjQ,QAAQ,CAACqC,IAAI,CAAC,CAAA;IACzC,OAAO2N,mBAAmB,CAACjP,IAAI,EAAEkP,UAAU,EAAE52B,IAAI,EAAE,KAAK,EAAEgpB,IAAI,CAAC,CAAA;AACjE,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACE,EAAA,OAAOR,OAAOA,CAAC1rB,MAAM,EAAEkV,WAAW,GAAG,IAAI,EAAE;IACzC,IAAI,CAAClV,MAAM,EAAE;AACX,MAAA,MAAM,IAAIO,oBAAoB,CAAC,kDAAkD,CAAC,CAAA;AACpF,KAAA;AAEA,IAAA,MAAMmrB,OAAO,GAAG1rB,MAAM,YAAYiV,OAAO,GAAGjV,MAAM,GAAG,IAAIiV,OAAO,CAACjV,MAAM,EAAEkV,WAAW,CAAC,CAAA;IAErF,IAAInH,QAAQ,CAAC8G,cAAc,EAAE;AAC3B,MAAA,MAAM,IAAI/U,oBAAoB,CAAC4rB,OAAO,CAAC,CAAA;AACzC,KAAC,MAAM;MACL,OAAO,IAAIjhB,QAAQ,CAAC;AAAEihB,QAAAA,OAAAA;AAAQ,OAAC,CAAC,CAAA;AAClC,KAAA;AACF,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,OAAO8R,UAAUA,CAACzkB,CAAC,EAAE;AACnB,IAAA,OAAQA,CAAC,IAAIA,CAAC,CAACgjB,eAAe,IAAK,KAAK,CAAA;AAC1C,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE,OAAO0B,kBAAkBA,CAACrc,UAAU,EAAEsc,UAAU,GAAG,EAAE,EAAE;AACrD,IAAA,MAAMC,SAAS,GAAGhG,kBAAkB,CAACvW,UAAU,EAAEjY,MAAM,CAACsF,UAAU,CAACivB,UAAU,CAAC,CAAC,CAAA;IAC/E,OAAO,CAACC,SAAS,GAAG,IAAI,GAAGA,SAAS,CAAChxB,GAAG,CAAEoI,CAAC,IAAMA,CAAC,GAAGA,CAAC,CAACuK,GAAG,GAAG,IAAK,CAAC,CAAC1S,IAAI,CAAC,EAAE,CAAC,CAAA;AAC9E,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAOgxB,YAAYA,CAAC/c,GAAG,EAAE6c,UAAU,GAAG,EAAE,EAAE;AACxC,IAAA,MAAMG,QAAQ,GAAGjG,iBAAiB,CAACjX,SAAS,CAACC,WAAW,CAACC,GAAG,CAAC,EAAE1X,MAAM,CAACsF,UAAU,CAACivB,UAAU,CAAC,CAAC,CAAA;AAC7F,IAAA,OAAOG,QAAQ,CAAClxB,GAAG,CAAEoI,CAAC,IAAKA,CAAC,CAACuK,GAAG,CAAC,CAAC1S,IAAI,CAAC,EAAE,CAAC,CAAA;AAC5C,GAAA;EAEA,OAAOnG,UAAUA,GAAG;AAClBy0B,IAAAA,YAAY,GAAGx2B,SAAS,CAAA;IACxB02B,oBAAoB,CAAC10B,KAAK,EAAE,CAAA;AAC9B,GAAA;;AAEA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEjC,GAAGA,CAACnE,IAAI,EAAE;IACR,OAAO,IAAI,CAACA,IAAI,CAAC,CAAA;AACnB,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIkD,OAAOA,GAAG;AACZ,IAAA,OAAO,IAAI,CAACkoB,OAAO,KAAK,IAAI,CAAA;AAC9B,GAAA;;AAEA;AACF;AACA;AACA;EACE,IAAI8B,aAAaA,GAAG;IAClB,OAAO,IAAI,CAAC9B,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC1rB,MAAM,GAAG,IAAI,CAAA;AAClD,GAAA;;AAEA;AACF;AACA;AACA;EACE,IAAI4uB,kBAAkBA,GAAG;IACvB,OAAO,IAAI,CAAClD,OAAO,GAAG,IAAI,CAACA,OAAO,CAACxW,WAAW,GAAG,IAAI,CAAA;AACvD,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAIlR,MAAMA,GAAG;IACX,OAAO,IAAI,CAACR,OAAO,GAAG,IAAI,CAACsH,GAAG,CAAC9G,MAAM,GAAG,IAAI,CAAA;AAC9C,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAIgG,eAAeA,GAAG;IACpB,OAAO,IAAI,CAACxG,OAAO,GAAG,IAAI,CAACsH,GAAG,CAACd,eAAe,GAAG,IAAI,CAAA;AACvD,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAIG,cAAcA,GAAG;IACnB,OAAO,IAAI,CAAC3G,OAAO,GAAG,IAAI,CAACsH,GAAG,CAACX,cAAc,GAAG,IAAI,CAAA;AACtD,GAAA;;AAEA;AACF;AACA;AACA;EACE,IAAI3D,IAAIA,GAAG;IACT,OAAO,IAAI,CAACs1B,KAAK,CAAA;AACnB,GAAA;;AAEA;AACF;AACA;AACA;EACE,IAAIv3B,QAAQA,GAAG;IACb,OAAO,IAAI,CAACf,OAAO,GAAG,IAAI,CAACgD,IAAI,CAAC3D,IAAI,GAAG,IAAI,CAAA;AAC7C,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAIhC,IAAIA,GAAG;IACT,OAAO,IAAI,CAAC2C,OAAO,GAAG,IAAI,CAACyd,CAAC,CAACpgB,IAAI,GAAGkG,GAAG,CAAA;AACzC,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAI0b,OAAOA,GAAG;AACZ,IAAA,OAAO,IAAI,CAACjf,OAAO,GAAG0D,IAAI,CAACsU,IAAI,CAAC,IAAI,CAACyF,CAAC,CAACngB,KAAK,GAAG,CAAC,CAAC,GAAGiG,GAAG,CAAA;AACzD,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAIjG,KAAKA,GAAG;IACV,OAAO,IAAI,CAAC0C,OAAO,GAAG,IAAI,CAACyd,CAAC,CAACngB,KAAK,GAAGiG,GAAG,CAAA;AAC1C,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAIhG,GAAGA,GAAG;IACR,OAAO,IAAI,CAACyC,OAAO,GAAG,IAAI,CAACyd,CAAC,CAAClgB,GAAG,GAAGgG,GAAG,CAAA;AACxC,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAIzF,IAAIA,GAAG;IACT,OAAO,IAAI,CAACkC,OAAO,GAAG,IAAI,CAACyd,CAAC,CAAC3f,IAAI,GAAGyF,GAAG,CAAA;AACzC,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAIxF,MAAMA,GAAG;IACX,OAAO,IAAI,CAACiC,OAAO,GAAG,IAAI,CAACyd,CAAC,CAAC1f,MAAM,GAAGwF,GAAG,CAAA;AAC3C,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAItF,MAAMA,GAAG;IACX,OAAO,IAAI,CAAC+B,OAAO,GAAG,IAAI,CAACyd,CAAC,CAACxf,MAAM,GAAGsF,GAAG,CAAA;AAC3C,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAIQ,WAAWA,GAAG;IAChB,OAAO,IAAI,CAAC/D,OAAO,GAAG,IAAI,CAACyd,CAAC,CAAC1Z,WAAW,GAAGR,GAAG,CAAA;AAChD,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAI4P,QAAQA,GAAG;IACb,OAAO,IAAI,CAACnT,OAAO,GAAG+0B,sBAAsB,CAAC,IAAI,CAAC,CAAC5hB,QAAQ,GAAG5P,GAAG,CAAA;AACnE,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAI2P,UAAUA,GAAG;IACf,OAAO,IAAI,CAAClT,OAAO,GAAG+0B,sBAAsB,CAAC,IAAI,CAAC,CAAC7hB,UAAU,GAAG3P,GAAG,CAAA;AACrE,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAI7F,OAAOA,GAAG;IACZ,OAAO,IAAI,CAACsC,OAAO,GAAG+0B,sBAAsB,CAAC,IAAI,CAAC,CAACr3B,OAAO,GAAG6F,GAAG,CAAA;AAClE,GAAA;;AAEA;AACF;AACA;AACA;EACE,IAAI+2B,SAASA,GAAG;AACd,IAAA,OAAO,IAAI,CAACt6B,OAAO,IAAI,IAAI,CAACsH,GAAG,CAACqG,cAAc,EAAE,CAAC/G,QAAQ,CAAC,IAAI,CAAClJ,OAAO,CAAC,CAAA;AACzE,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIwW,YAAYA,GAAG;IACjB,OAAO,IAAI,CAAClU,OAAO,GAAGg1B,2BAA2B,CAAC,IAAI,CAAC,CAACt3B,OAAO,GAAG6F,GAAG,CAAA;AACvE,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAI4Q,eAAeA,GAAG;IACpB,OAAO,IAAI,CAACnU,OAAO,GAAGg1B,2BAA2B,CAAC,IAAI,CAAC,CAAC9hB,UAAU,GAAG3P,GAAG,CAAA;AAC1E,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAI6Q,aAAaA,GAAG;IAClB,OAAO,IAAI,CAACpU,OAAO,GAAGg1B,2BAA2B,CAAC,IAAI,CAAC,CAAC7hB,QAAQ,GAAG5P,GAAG,CAAA;AACxE,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAIiP,OAAOA,GAAG;AACZ,IAAA,OAAO,IAAI,CAACxS,OAAO,GAAG2T,kBAAkB,CAAC,IAAI,CAAC8J,CAAC,CAAC,CAACjL,OAAO,GAAGjP,GAAG,CAAA;AAChE,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIg3B,UAAUA,GAAG;IACf,OAAO,IAAI,CAACv6B,OAAO,GAAG+uB,IAAI,CAAC3iB,MAAM,CAAC,OAAO,EAAE;MAAE+iB,MAAM,EAAE,IAAI,CAAC7nB,GAAAA;KAAK,CAAC,CAAC,IAAI,CAAChK,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAA;AACzF,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIk9B,SAASA,GAAG;IACd,OAAO,IAAI,CAACx6B,OAAO,GAAG+uB,IAAI,CAAC3iB,MAAM,CAAC,MAAM,EAAE;MAAE+iB,MAAM,EAAE,IAAI,CAAC7nB,GAAAA;KAAK,CAAC,CAAC,IAAI,CAAChK,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAA;AACxF,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIm9B,YAAYA,GAAG;IACjB,OAAO,IAAI,CAACz6B,OAAO,GAAG+uB,IAAI,CAACriB,QAAQ,CAAC,OAAO,EAAE;MAAEyiB,MAAM,EAAE,IAAI,CAAC7nB,GAAAA;KAAK,CAAC,CAAC,IAAI,CAAC5J,OAAO,GAAG,CAAC,CAAC,GAAG,IAAI,CAAA;AAC7F,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIg9B,WAAWA,GAAG;IAChB,OAAO,IAAI,CAAC16B,OAAO,GAAG+uB,IAAI,CAACriB,QAAQ,CAAC,MAAM,EAAE;MAAEyiB,MAAM,EAAE,IAAI,CAAC7nB,GAAAA;KAAK,CAAC,CAAC,IAAI,CAAC5J,OAAO,GAAG,CAAC,CAAC,GAAG,IAAI,CAAA;AAC5F,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAImC,MAAMA,GAAG;IACX,OAAO,IAAI,CAACG,OAAO,GAAG,CAAC,IAAI,CAACuV,CAAC,GAAGhS,GAAG,CAAA;AACrC,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAIo3B,eAAeA,GAAG;IACpB,IAAI,IAAI,CAAC36B,OAAO,EAAE;MAChB,OAAO,IAAI,CAACgD,IAAI,CAACxD,UAAU,CAAC,IAAI,CAACC,EAAE,EAAE;AACnCG,QAAAA,MAAM,EAAE,OAAO;QACfY,MAAM,EAAE,IAAI,CAACA,MAAAA;AACf,OAAC,CAAC,CAAA;AACJ,KAAC,MAAM;AACL,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AACF,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE,IAAIo6B,cAAcA,GAAG;IACnB,IAAI,IAAI,CAAC56B,OAAO,EAAE;MAChB,OAAO,IAAI,CAACgD,IAAI,CAACxD,UAAU,CAAC,IAAI,CAACC,EAAE,EAAE;AACnCG,QAAAA,MAAM,EAAE,MAAM;QACdY,MAAM,EAAE,IAAI,CAACA,MAAAA;AACf,OAAC,CAAC,CAAA;AACJ,KAAC,MAAM;AACL,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AACF,GAAA;;AAEA;AACF;AACA;AACA;EACE,IAAIoe,aAAaA,GAAG;IAClB,OAAO,IAAI,CAAC5e,OAAO,GAAG,IAAI,CAACgD,IAAI,CAACzD,WAAW,GAAG,IAAI,CAAA;AACpD,GAAA;;AAEA;AACF;AACA;AACA;EACE,IAAIs7B,OAAOA,GAAG;IACZ,IAAI,IAAI,CAACjc,aAAa,EAAE;AACtB,MAAA,OAAO,KAAK,CAAA;AACd,KAAC,MAAM;AACL,MAAA,OACE,IAAI,CAAC/e,MAAM,GAAG,IAAI,CAACwB,GAAG,CAAC;AAAE/D,QAAAA,KAAK,EAAE,CAAC;AAAEC,QAAAA,GAAG,EAAE,CAAA;OAAG,CAAC,CAACsC,MAAM,IACnD,IAAI,CAACA,MAAM,GAAG,IAAI,CAACwB,GAAG,CAAC;AAAE/D,QAAAA,KAAK,EAAE,CAAA;OAAG,CAAC,CAACuC,MAAM,CAAA;AAE/C,KAAA;AACF,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACEi7B,EAAAA,kBAAkBA,GAAG;IACnB,IAAI,CAAC,IAAI,CAAC96B,OAAO,IAAI,IAAI,CAAC4e,aAAa,EAAE;MACvC,OAAO,CAAC,IAAI,CAAC,CAAA;AACf,KAAA;IACA,MAAMmc,KAAK,GAAG,QAAQ,CAAA;IACtB,MAAMC,QAAQ,GAAG,KAAK,CAAA;AACtB,IAAA,MAAM3F,OAAO,GAAGvxB,YAAY,CAAC,IAAI,CAAC2Z,CAAC,CAAC,CAAA;IACpC,MAAMwd,QAAQ,GAAG,IAAI,CAACj4B,IAAI,CAACnD,MAAM,CAACw1B,OAAO,GAAG0F,KAAK,CAAC,CAAA;IAClD,MAAMG,MAAM,GAAG,IAAI,CAACl4B,IAAI,CAACnD,MAAM,CAACw1B,OAAO,GAAG0F,KAAK,CAAC,CAAA;AAEhD,IAAA,MAAMI,EAAE,GAAG,IAAI,CAACn4B,IAAI,CAACnD,MAAM,CAACw1B,OAAO,GAAG4F,QAAQ,GAAGD,QAAQ,CAAC,CAAA;AAC1D,IAAA,MAAMxF,EAAE,GAAG,IAAI,CAACxyB,IAAI,CAACnD,MAAM,CAACw1B,OAAO,GAAG6F,MAAM,GAAGF,QAAQ,CAAC,CAAA;IACxD,IAAIG,EAAE,KAAK3F,EAAE,EAAE;MACb,OAAO,CAAC,IAAI,CAAC,CAAA;AACf,KAAA;AACA,IAAA,MAAM4F,GAAG,GAAG/F,OAAO,GAAG8F,EAAE,GAAGH,QAAQ,CAAA;AACnC,IAAA,MAAMK,GAAG,GAAGhG,OAAO,GAAGG,EAAE,GAAGwF,QAAQ,CAAA;AACnC,IAAA,MAAMM,EAAE,GAAG5F,OAAO,CAAC0F,GAAG,EAAED,EAAE,CAAC,CAAA;AAC3B,IAAA,MAAMI,EAAE,GAAG7F,OAAO,CAAC2F,GAAG,EAAE7F,EAAE,CAAC,CAAA;AAC3B,IAAA,IACE8F,EAAE,CAACx9B,IAAI,KAAKy9B,EAAE,CAACz9B,IAAI,IACnBw9B,EAAE,CAACv9B,MAAM,KAAKw9B,EAAE,CAACx9B,MAAM,IACvBu9B,EAAE,CAACr9B,MAAM,KAAKs9B,EAAE,CAACt9B,MAAM,IACvBq9B,EAAE,CAACv3B,WAAW,KAAKw3B,EAAE,CAACx3B,WAAW,EACjC;AACA,MAAA,OAAO,CAACgI,KAAK,CAAC,IAAI,EAAE;AAAEtM,QAAAA,EAAE,EAAE27B,GAAAA;AAAI,OAAC,CAAC,EAAErvB,KAAK,CAAC,IAAI,EAAE;AAAEtM,QAAAA,EAAE,EAAE47B,GAAAA;AAAI,OAAC,CAAC,CAAC,CAAA;AAC7D,KAAA;IACA,OAAO,CAAC,IAAI,CAAC,CAAA;AACf,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIG,YAAYA,GAAG;AACjB,IAAA,OAAOlpB,UAAU,CAAC,IAAI,CAACjV,IAAI,CAAC,CAAA;AAC9B,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAI4X,WAAWA,GAAG;IAChB,OAAOA,WAAW,CAAC,IAAI,CAAC5X,IAAI,EAAE,IAAI,CAACC,KAAK,CAAC,CAAA;AAC3C,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIoW,UAAUA,GAAG;IACf,OAAO,IAAI,CAAC1T,OAAO,GAAG0T,UAAU,CAAC,IAAI,CAACrW,IAAI,CAAC,GAAGkG,GAAG,CAAA;AACnD,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAI6P,eAAeA,GAAG;IACpB,OAAO,IAAI,CAACpT,OAAO,GAAGoT,eAAe,CAAC,IAAI,CAACD,QAAQ,CAAC,GAAG5P,GAAG,CAAA;AAC5D,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIk4B,oBAAoBA,GAAG;IACzB,OAAO,IAAI,CAACz7B,OAAO,GACfoT,eAAe,CACb,IAAI,CAACgB,aAAa,EAClB,IAAI,CAAC9M,GAAG,CAACoG,qBAAqB,EAAE,EAChC,IAAI,CAACpG,GAAG,CAACmG,cAAc,EACzB,CAAC,GACDlK,GAAG,CAAA;AACT,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACEm4B,EAAAA,qBAAqBA,CAACh8B,IAAI,GAAG,EAAE,EAAE;IAC/B,MAAM;MAAEc,MAAM;MAAEgG,eAAe;AAAEC,MAAAA,QAAAA;KAAU,GAAG0W,SAAS,CAACpa,MAAM,CAC5D,IAAI,CAACuE,GAAG,CAACyE,KAAK,CAACrM,IAAI,CAAC,EACpBA,IACF,CAAC,CAACY,eAAe,CAAC,IAAI,CAAC,CAAA;IACvB,OAAO;MAAEE,MAAM;MAAEgG,eAAe;AAAEG,MAAAA,cAAc,EAAEF,QAAAA;KAAU,CAAA;AAC9D,GAAA;;AAEA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEspB,KAAKA,CAAClwB,MAAM,GAAG,CAAC,EAAEH,IAAI,GAAG,EAAE,EAAE;AAC3B,IAAA,OAAO,IAAI,CAACsJ,OAAO,CAAC8E,eAAe,CAAC3N,QAAQ,CAACN,MAAM,CAAC,EAAEH,IAAI,CAAC,CAAA;AAC7D,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACEi8B,EAAAA,OAAOA,GAAG;AACR,IAAA,OAAO,IAAI,CAAC3yB,OAAO,CAACuB,QAAQ,CAACgE,WAAW,CAAC,CAAA;AAC3C,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEvF,OAAOA,CAAChG,IAAI,EAAE;AAAEgtB,IAAAA,aAAa,GAAG,KAAK;AAAE4L,IAAAA,gBAAgB,GAAG,KAAA;GAAO,GAAG,EAAE,EAAE;IACtE54B,IAAI,GAAGqL,aAAa,CAACrL,IAAI,EAAEuH,QAAQ,CAACgE,WAAW,CAAC,CAAA;IAChD,IAAIvL,IAAI,CAAClD,MAAM,CAAC,IAAI,CAACkD,IAAI,CAAC,EAAE;AAC1B,MAAA,OAAO,IAAI,CAAA;AACb,KAAC,MAAM,IAAI,CAACA,IAAI,CAAChD,OAAO,EAAE;MACxB,OAAOiH,QAAQ,CAACihB,OAAO,CAAC4M,eAAe,CAAC9xB,IAAI,CAAC,CAAC,CAAA;AAChD,KAAC,MAAM;AACL,MAAA,IAAI64B,KAAK,GAAG,IAAI,CAACp8B,EAAE,CAAA;MACnB,IAAIuwB,aAAa,IAAI4L,gBAAgB,EAAE;QACrC,MAAMjE,WAAW,GAAG30B,IAAI,CAACnD,MAAM,CAAC,IAAI,CAACJ,EAAE,CAAC,CAAA;AACxC,QAAA,MAAMq8B,KAAK,GAAG,IAAI,CAAC3S,QAAQ,EAAE,CAAA;QAC7B,CAAC0S,KAAK,CAAC,GAAG5F,OAAO,CAAC6F,KAAK,EAAEnE,WAAW,EAAE30B,IAAI,CAAC,CAAA;AAC7C,OAAA;MACA,OAAO+I,KAAK,CAAC,IAAI,EAAE;AAAEtM,QAAAA,EAAE,EAAEo8B,KAAK;AAAE74B,QAAAA,IAAAA;AAAK,OAAC,CAAC,CAAA;AACzC,KAAA;AACF,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACEwnB,EAAAA,WAAWA,CAAC;IAAEhqB,MAAM;IAAEgG,eAAe;AAAEG,IAAAA,cAAAA;GAAgB,GAAG,EAAE,EAAE;AAC5D,IAAA,MAAMW,GAAG,GAAG,IAAI,CAACA,GAAG,CAACyE,KAAK,CAAC;MAAEvL,MAAM;MAAEgG,eAAe;AAAEG,MAAAA,cAAAA;AAAe,KAAC,CAAC,CAAA;IACvE,OAAOoF,KAAK,CAAC,IAAI,EAAE;AAAEzE,MAAAA,GAAAA;AAAI,KAAC,CAAC,CAAA;AAC7B,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACEy0B,SAASA,CAACv7B,MAAM,EAAE;IAChB,OAAO,IAAI,CAACgqB,WAAW,CAAC;AAAEhqB,MAAAA,MAAAA;AAAO,KAAC,CAAC,CAAA;AACrC,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEa,GAAGA,CAACgf,MAAM,EAAE;AACV,IAAA,IAAI,CAAC,IAAI,CAACrgB,OAAO,EAAE,OAAO,IAAI,CAAA;AAE9B,IAAA,MAAMyZ,UAAU,GAAGF,eAAe,CAAC8G,MAAM,EAAEmX,2BAA2B,CAAC,CAAA;IACvE,MAAM;MAAEvkB,kBAAkB;AAAEH,MAAAA,WAAAA;KAAa,GAAGiB,mBAAmB,CAAC0F,UAAU,EAAE,IAAI,CAACnS,GAAG,CAAC,CAAA;IAErF,MAAM00B,gBAAgB,GAClB,CAACr5B,WAAW,CAAC8W,UAAU,CAACtG,QAAQ,CAAC,IACjC,CAACxQ,WAAW,CAAC8W,UAAU,CAACvG,UAAU,CAAC,IACnC,CAACvQ,WAAW,CAAC8W,UAAU,CAAC/b,OAAO,CAAC;AAClCm7B,MAAAA,eAAe,GAAG,CAACl2B,WAAW,CAAC8W,UAAU,CAACjH,OAAO,CAAC;AAClDsmB,MAAAA,kBAAkB,GAAG,CAACn2B,WAAW,CAAC8W,UAAU,CAACpc,IAAI,CAAC;AAClD07B,MAAAA,gBAAgB,GAAG,CAACp2B,WAAW,CAAC8W,UAAU,CAACnc,KAAK,CAAC,IAAI,CAACqF,WAAW,CAAC8W,UAAU,CAAClc,GAAG,CAAC;MACjFy7B,cAAc,GAAGF,kBAAkB,IAAIC,gBAAgB;AACvDE,MAAAA,eAAe,GAAGxf,UAAU,CAACtG,QAAQ,IAAIsG,UAAU,CAACvG,UAAU,CAAA;AAEhE,IAAA,IAAI,CAAC8lB,cAAc,IAAIH,eAAe,KAAKI,eAAe,EAAE;AAC1D,MAAA,MAAM,IAAIr8B,6BAA6B,CACrC,qEACF,CAAC,CAAA;AACH,KAAA;IAEA,IAAIm8B,gBAAgB,IAAIF,eAAe,EAAE;AACvC,MAAA,MAAM,IAAIj8B,6BAA6B,CAAC,wCAAwC,CAAC,CAAA;AACnF,KAAA;AAEA,IAAA,IAAI2tB,KAAK,CAAA;AACT,IAAA,IAAIyR,gBAAgB,EAAE;MACpBzR,KAAK,GAAGjX,eAAe,CACrB;QAAE,GAAGP,eAAe,CAAC,IAAI,CAAC0K,CAAC,EAAExK,kBAAkB,EAAEH,WAAW,CAAC;QAAE,GAAG2G,UAAAA;AAAW,OAAC,EAC9ExG,kBAAkB,EAClBH,WACF,CAAC,CAAA;KACF,MAAM,IAAI,CAACnQ,WAAW,CAAC8W,UAAU,CAACjH,OAAO,CAAC,EAAE;MAC3C+X,KAAK,GAAG1W,kBAAkB,CAAC;AAAE,QAAA,GAAGF,kBAAkB,CAAC,IAAI,CAAC8J,CAAC,CAAC;QAAE,GAAGhE,UAAAA;AAAW,OAAC,CAAC,CAAA;AAC9E,KAAC,MAAM;AACL8Q,MAAAA,KAAK,GAAG;AAAE,QAAA,GAAG,IAAI,CAACpB,QAAQ,EAAE;QAAE,GAAG1P,UAAAA;OAAY,CAAA;;AAE7C;AACA;AACA,MAAA,IAAI9W,WAAW,CAAC8W,UAAU,CAAClc,GAAG,CAAC,EAAE;QAC/BgtB,KAAK,CAAChtB,GAAG,GAAGmG,IAAI,CAAC+M,GAAG,CAACwE,WAAW,CAACsV,KAAK,CAACltB,IAAI,EAAEktB,KAAK,CAACjtB,KAAK,CAAC,EAAEitB,KAAK,CAAChtB,GAAG,CAAC,CAAA;AACvE,OAAA;AACF,KAAA;AAEA,IAAA,MAAM,CAACkC,EAAE,EAAE8V,CAAC,CAAC,GAAG0gB,OAAO,CAAC1L,KAAK,EAAE,IAAI,CAAChV,CAAC,EAAE,IAAI,CAACvS,IAAI,CAAC,CAAA;IACjD,OAAO+I,KAAK,CAAC,IAAI,EAAE;MAAEtM,EAAE;AAAE8V,MAAAA,CAAAA;AAAE,KAAC,CAAC,CAAA;AAC/B,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEtM,IAAIA,CAACihB,QAAQ,EAAE;AACb,IAAA,IAAI,CAAC,IAAI,CAAClqB,OAAO,EAAE,OAAO,IAAI,CAAA;AAC9B,IAAA,MAAMmf,GAAG,GAAG+H,QAAQ,CAACoB,gBAAgB,CAAC4B,QAAQ,CAAC,CAAA;IAC/C,OAAOne,KAAK,CAAC,IAAI,EAAEmqB,UAAU,CAAC,IAAI,EAAE/W,GAAG,CAAC,CAAC,CAAA;AAC3C,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;EACEgL,KAAKA,CAACD,QAAQ,EAAE;AACd,IAAA,IAAI,CAAC,IAAI,CAAClqB,OAAO,EAAE,OAAO,IAAI,CAAA;IAC9B,MAAMmf,GAAG,GAAG+H,QAAQ,CAACoB,gBAAgB,CAAC4B,QAAQ,CAAC,CAACE,MAAM,EAAE,CAAA;IACxD,OAAOre,KAAK,CAAC,IAAI,EAAEmqB,UAAU,CAAC,IAAI,EAAE/W,GAAG,CAAC,CAAC,CAAA;AAC3C,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEoN,OAAOA,CAACzvB,IAAI,EAAE;AAAE0vB,IAAAA,cAAc,GAAG,KAAA;GAAO,GAAG,EAAE,EAAE;AAC7C,IAAA,IAAI,CAAC,IAAI,CAACxsB,OAAO,EAAE,OAAO,IAAI,CAAA;IAE9B,MAAMuV,CAAC,GAAG,EAAE;AACV0mB,MAAAA,cAAc,GAAG/U,QAAQ,CAACmB,aAAa,CAACvrB,IAAI,CAAC,CAAA;AAC/C,IAAA,QAAQm/B,cAAc;AACpB,MAAA,KAAK,OAAO;QACV1mB,CAAC,CAACjY,KAAK,GAAG,CAAC,CAAA;AACb;AACA,MAAA,KAAK,UAAU,CAAA;AACf,MAAA,KAAK,QAAQ;QACXiY,CAAC,CAAChY,GAAG,GAAG,CAAC,CAAA;AACX;AACA,MAAA,KAAK,OAAO,CAAA;AACZ,MAAA,KAAK,MAAM;QACTgY,CAAC,CAACzX,IAAI,GAAG,CAAC,CAAA;AACZ;AACA,MAAA,KAAK,OAAO;QACVyX,CAAC,CAACxX,MAAM,GAAG,CAAC,CAAA;AACd;AACA,MAAA,KAAK,SAAS;QACZwX,CAAC,CAACtX,MAAM,GAAG,CAAC,CAAA;AACd;AACA,MAAA,KAAK,SAAS;QACZsX,CAAC,CAACxR,WAAW,GAAG,CAAC,CAAA;AACjB,QAAA,MAAA;AAGF;AACF,KAAA;;IAEA,IAAIk4B,cAAc,KAAK,OAAO,EAAE;AAC9B,MAAA,IAAIzP,cAAc,EAAE;QAClB,MAAM1Z,WAAW,GAAG,IAAI,CAACxL,GAAG,CAACmG,cAAc,EAAE,CAAA;QAC7C,MAAM;AAAE/P,UAAAA,OAAAA;AAAQ,SAAC,GAAG,IAAI,CAAA;QACxB,IAAIA,OAAO,GAAGoV,WAAW,EAAE;AACzByC,UAAAA,CAAC,CAACrC,UAAU,GAAG,IAAI,CAACA,UAAU,GAAG,CAAC,CAAA;AACpC,SAAA;QACAqC,CAAC,CAAC7X,OAAO,GAAGoV,WAAW,CAAA;AACzB,OAAC,MAAM;QACLyC,CAAC,CAAC7X,OAAO,GAAG,CAAC,CAAA;AACf,OAAA;AACF,KAAA;IAEA,IAAIu+B,cAAc,KAAK,UAAU,EAAE;MACjC,MAAMtI,CAAC,GAAGjwB,IAAI,CAACsU,IAAI,CAAC,IAAI,CAAC1a,KAAK,GAAG,CAAC,CAAC,CAAA;MACnCiY,CAAC,CAACjY,KAAK,GAAG,CAACq2B,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AAC3B,KAAA;AAEA,IAAA,OAAO,IAAI,CAACtyB,GAAG,CAACkU,CAAC,CAAC,CAAA;AACpB,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE2mB,EAAAA,KAAKA,CAACp/B,IAAI,EAAE4C,IAAI,EAAE;AAChB,IAAA,OAAO,IAAI,CAACM,OAAO,GACf,IAAI,CAACiJ,IAAI,CAAC;AAAE,MAAA,CAACnM,IAAI,GAAG,CAAA;AAAE,KAAC,CAAC,CACrByvB,OAAO,CAACzvB,IAAI,EAAE4C,IAAI,CAAC,CACnByqB,KAAK,CAAC,CAAC,CAAC,GACX,IAAI,CAAA;AACV,GAAA;;AAEA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEtB,EAAAA,QAAQA,CAACxL,GAAG,EAAE3d,IAAI,GAAG,EAAE,EAAE;IACvB,OAAO,IAAI,CAACM,OAAO,GACfmd,SAAS,CAACpa,MAAM,CAAC,IAAI,CAACuE,GAAG,CAAC4E,aAAa,CAACxM,IAAI,CAAC,CAAC,CAAC+e,wBAAwB,CAAC,IAAI,EAAEpB,GAAG,CAAC,GAClFiJ,OAAO,CAAA;AACb,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEmI,cAAcA,CAAC7Q,UAAU,GAAG3B,UAAkB,EAAEvc,IAAI,GAAG,EAAE,EAAE;IACzD,OAAO,IAAI,CAACM,OAAO,GACfmd,SAAS,CAACpa,MAAM,CAAC,IAAI,CAACuE,GAAG,CAACyE,KAAK,CAACrM,IAAI,CAAC,EAAEke,UAAU,CAAC,CAACG,cAAc,CAAC,IAAI,CAAC,GACvEuI,OAAO,CAAA;AACb,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE6V,EAAAA,aAAaA,CAACz8B,IAAI,GAAG,EAAE,EAAE;IACvB,OAAO,IAAI,CAACM,OAAO,GACfmd,SAAS,CAACpa,MAAM,CAAC,IAAI,CAACuE,GAAG,CAACyE,KAAK,CAACrM,IAAI,CAAC,EAAEA,IAAI,CAAC,CAACse,mBAAmB,CAAC,IAAI,CAAC,GACtE,EAAE,CAAA;AACR,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEoL,EAAAA,KAAKA,CAAC;AACJxpB,IAAAA,MAAM,GAAG,UAAU;AACnB6pB,IAAAA,eAAe,GAAG,KAAK;AACvBD,IAAAA,oBAAoB,GAAG,KAAK;AAC5BG,IAAAA,aAAa,GAAG,IAAI;AACpBiN,IAAAA,YAAY,GAAG,KAAK;AACpBF,IAAAA,SAAS,GAAG,cAAA;GACb,GAAG,EAAE,EAAE;AACN,IAAA,IAAI,CAAC,IAAI,CAAC12B,OAAO,EAAE;AACjB,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AAEA02B,IAAAA,SAAS,GAAGrO,aAAa,CAACqO,SAAS,CAAC,CAAA;AACpC,IAAA,MAAM0F,GAAG,GAAGx8B,MAAM,KAAK,UAAU,CAAA;IAEjC,IAAI6d,CAAC,GAAGiR,SAAS,CAAC,IAAI,EAAE0N,GAAG,EAAE1F,SAAS,CAAC,CAAA;IACvC,IAAI9P,YAAY,CAAC1gB,OAAO,CAACwwB,SAAS,CAAC,IAAI,CAAC,EAAEjZ,CAAC,IAAI,GAAG,CAAA;AAClDA,IAAAA,CAAC,IAAI4L,SAAS,CACZ,IAAI,EACJ+S,GAAG,EACH3S,eAAe,EACfD,oBAAoB,EACpBG,aAAa,EACbiN,YAAY,EACZF,SACF,CAAC,CAAA;AACD,IAAA,OAAOjZ,CAAC,CAAA;AACV,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEiR,EAAAA,SAASA,CAAC;AAAE9uB,IAAAA,MAAM,GAAG,UAAU;AAAE82B,IAAAA,SAAS,GAAG,KAAA;GAAO,GAAG,EAAE,EAAE;AACzD,IAAA,IAAI,CAAC,IAAI,CAAC12B,OAAO,EAAE;AACjB,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AACA,IAAA,OAAO0uB,SAAS,CAAC,IAAI,EAAE9uB,MAAM,KAAK,UAAU,EAAEyoB,aAAa,CAACqO,SAAS,CAAC,CAAC,CAAA;AACzE,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACE2F,EAAAA,aAAaA,GAAG;AACd,IAAA,OAAO7F,YAAY,CAAC,IAAI,EAAE,cAAc,CAAC,CAAA;AAC3C,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEnN,EAAAA,SAASA,CAAC;AACRG,IAAAA,oBAAoB,GAAG,KAAK;AAC5BC,IAAAA,eAAe,GAAG,KAAK;AACvBE,IAAAA,aAAa,GAAG,IAAI;AACpBD,IAAAA,aAAa,GAAG,KAAK;AACrBkN,IAAAA,YAAY,GAAG,KAAK;AACpBh3B,IAAAA,MAAM,GAAG,UAAU;AACnB82B,IAAAA,SAAS,GAAG,cAAA;GACb,GAAG,EAAE,EAAE;AACN,IAAA,IAAI,CAAC,IAAI,CAAC12B,OAAO,EAAE;AACjB,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AAEA02B,IAAAA,SAAS,GAAGrO,aAAa,CAACqO,SAAS,CAAC,CAAA;AACpC,IAAA,IAAIjZ,CAAC,GAAGiM,aAAa,IAAI9C,YAAY,CAAC1gB,OAAO,CAACwwB,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,CAAA;AACxE,IAAA,OACEjZ,CAAC,GACD4L,SAAS,CACP,IAAI,EACJzpB,MAAM,KAAK,UAAU,EACrB6pB,eAAe,EACfD,oBAAoB,EACpBG,aAAa,EACbiN,YAAY,EACZF,SACF,CAAC,CAAA;AAEL,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACE4F,EAAAA,SAASA,GAAG;AACV,IAAA,OAAO9F,YAAY,CAAC,IAAI,EAAE,+BAA+B,EAAE,KAAK,CAAC,CAAA;AACnE,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACE+F,EAAAA,MAAMA,GAAG;IACP,OAAO/F,YAAY,CAAC,IAAI,CAACzG,KAAK,EAAE,EAAE,iCAAiC,CAAC,CAAA;AACtE,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACEyM,EAAAA,SAASA,GAAG;AACV,IAAA,IAAI,CAAC,IAAI,CAACx8B,OAAO,EAAE;AACjB,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AACA,IAAA,OAAO0uB,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;AAC9B,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE+N,EAAAA,SAASA,CAAC;AAAE9S,IAAAA,aAAa,GAAG,IAAI;AAAE+S,IAAAA,WAAW,GAAG,KAAK;AAAEC,IAAAA,kBAAkB,GAAG,IAAA;GAAM,GAAG,EAAE,EAAE;IACvF,IAAItf,GAAG,GAAG,cAAc,CAAA;IAExB,IAAIqf,WAAW,IAAI/S,aAAa,EAAE;AAChC,MAAA,IAAIgT,kBAAkB,EAAE;AACtBtf,QAAAA,GAAG,IAAI,GAAG,CAAA;AACZ,OAAA;AACA,MAAA,IAAIqf,WAAW,EAAE;AACfrf,QAAAA,GAAG,IAAI,GAAG,CAAA;OACX,MAAM,IAAIsM,aAAa,EAAE;AACxBtM,QAAAA,GAAG,IAAI,IAAI,CAAA;AACb,OAAA;AACF,KAAA;AAEA,IAAA,OAAOmZ,YAAY,CAAC,IAAI,EAAEnZ,GAAG,EAAE,IAAI,CAAC,CAAA;AACtC,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEuf,EAAAA,KAAKA,CAACl9B,IAAI,GAAG,EAAE,EAAE;AACf,IAAA,IAAI,CAAC,IAAI,CAACM,OAAO,EAAE;AACjB,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AAEA,IAAA,OAAQ,CAAE,EAAA,IAAI,CAACw8B,SAAS,EAAG,CAAG,CAAA,EAAA,IAAI,CAACC,SAAS,CAAC/8B,IAAI,CAAE,CAAC,CAAA,CAAA;AACtD,GAAA;;AAEA;AACF;AACA;AACA;AACEmO,EAAAA,QAAQA,GAAG;IACT,OAAO,IAAI,CAAC7N,OAAO,GAAG,IAAI,CAACopB,KAAK,EAAE,GAAG9C,OAAO,CAAA;AAC9C,GAAA;;AAEA;AACF;AACA;AACA;AACE,EAAA,CAACwD,MAAM,CAACC,GAAG,CAAC,4BAA4B,CAAC,CAAI,GAAA;IAC3C,IAAI,IAAI,CAAC/pB,OAAO,EAAE;AAChB,MAAA,OAAQ,kBAAiB,IAAI,CAACopB,KAAK,EAAG,CAAU,QAAA,EAAA,IAAI,CAACpmB,IAAI,CAAC3D,IAAK,CAAA,UAAA,EAAY,IAAI,CAACmB,MAAO,CAAG,EAAA,CAAA,CAAA;AAC5F,KAAC,MAAM;AACL,MAAA,OAAQ,CAA8B,4BAAA,EAAA,IAAI,CAACwpB,aAAc,CAAG,EAAA,CAAA,CAAA;AAC9D,KAAA;AACF,GAAA;;AAEA;AACF;AACA;AACA;AACEC,EAAAA,OAAOA,GAAG;AACR,IAAA,OAAO,IAAI,CAACV,QAAQ,EAAE,CAAA;AACxB,GAAA;;AAEA;AACF;AACA;AACA;AACEA,EAAAA,QAAQA,GAAG;IACT,OAAO,IAAI,CAACvpB,OAAO,GAAG,IAAI,CAACP,EAAE,GAAG8D,GAAG,CAAA;AACrC,GAAA;;AAEA;AACF;AACA;AACA;AACEs5B,EAAAA,SAASA,GAAG;IACV,OAAO,IAAI,CAAC78B,OAAO,GAAG,IAAI,CAACP,EAAE,GAAG,IAAI,GAAG8D,GAAG,CAAA;AAC5C,GAAA;;AAEA;AACF;AACA;AACA;AACEu5B,EAAAA,aAAaA,GAAG;AACd,IAAA,OAAO,IAAI,CAAC98B,OAAO,GAAG0D,IAAI,CAACuE,KAAK,CAAC,IAAI,CAACxI,EAAE,GAAG,IAAI,CAAC,GAAG8D,GAAG,CAAA;AACxD,GAAA;;AAEA;AACF;AACA;AACA;AACEsmB,EAAAA,MAAMA,GAAG;AACP,IAAA,OAAO,IAAI,CAACT,KAAK,EAAE,CAAA;AACrB,GAAA;;AAEA;AACF;AACA;AACA;AACE2T,EAAAA,MAAMA,GAAG;AACP,IAAA,OAAO,IAAI,CAAC1zB,QAAQ,EAAE,CAAA;AACxB,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACE8f,EAAAA,QAAQA,CAACzpB,IAAI,GAAG,EAAE,EAAE;AAClB,IAAA,IAAI,CAAC,IAAI,CAACM,OAAO,EAAE,OAAO,EAAE,CAAA;AAE5B,IAAA,MAAMiF,IAAI,GAAG;AAAE,MAAA,GAAG,IAAI,CAACwY,CAAAA;KAAG,CAAA;IAE1B,IAAI/d,IAAI,CAACs9B,aAAa,EAAE;AACtB/3B,MAAAA,IAAI,CAAC0B,cAAc,GAAG,IAAI,CAACA,cAAc,CAAA;AACzC1B,MAAAA,IAAI,CAACuB,eAAe,GAAG,IAAI,CAACc,GAAG,CAACd,eAAe,CAAA;AAC/CvB,MAAAA,IAAI,CAACzE,MAAM,GAAG,IAAI,CAAC8G,GAAG,CAAC9G,MAAM,CAAA;AAC/B,KAAA;AACA,IAAA,OAAOyE,IAAI,CAAA;AACb,GAAA;;AAEA;AACF;AACA;AACA;AACEoE,EAAAA,QAAQA,GAAG;AACT,IAAA,OAAO,IAAI3I,IAAI,CAAC,IAAI,CAACV,OAAO,GAAG,IAAI,CAACP,EAAE,GAAG8D,GAAG,CAAC,CAAA;AAC/C,GAAA;;AAEA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEkpB,IAAIA,CAACwQ,aAAa,EAAEngC,IAAI,GAAG,cAAc,EAAE4C,IAAI,GAAG,EAAE,EAAE;IACpD,IAAI,CAAC,IAAI,CAACM,OAAO,IAAI,CAACi9B,aAAa,CAACj9B,OAAO,EAAE;AAC3C,MAAA,OAAOknB,QAAQ,CAACgB,OAAO,CAAC,wCAAwC,CAAC,CAAA;AACnE,KAAA;AAEA,IAAA,MAAMgV,OAAO,GAAG;MAAE18B,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEgG,eAAe,EAAE,IAAI,CAACA,eAAe;MAAE,GAAG9G,IAAAA;KAAM,CAAA;AAEvF,IAAA,MAAMkb,KAAK,GAAGjF,UAAU,CAAC7Y,IAAI,CAAC,CAACqM,GAAG,CAAC+d,QAAQ,CAACmB,aAAa,CAAC;MACxD8U,YAAY,GAAGF,aAAa,CAAChT,OAAO,EAAE,GAAG,IAAI,CAACA,OAAO,EAAE;AACvD2F,MAAAA,OAAO,GAAGuN,YAAY,GAAG,IAAI,GAAGF,aAAa;AAC7CpN,MAAAA,KAAK,GAAGsN,YAAY,GAAGF,aAAa,GAAG,IAAI;MAC3CG,MAAM,GAAG3Q,IAAI,CAACmD,OAAO,EAAEC,KAAK,EAAEjV,KAAK,EAAEsiB,OAAO,CAAC,CAAA;IAE/C,OAAOC,YAAY,GAAGC,MAAM,CAAChT,MAAM,EAAE,GAAGgT,MAAM,CAAA;AAChD,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,OAAOA,CAACvgC,IAAI,GAAG,cAAc,EAAE4C,IAAI,GAAG,EAAE,EAAE;AACxC,IAAA,OAAO,IAAI,CAAC+sB,IAAI,CAACxlB,QAAQ,CAACkK,GAAG,EAAE,EAAErU,IAAI,EAAE4C,IAAI,CAAC,CAAA;AAC9C,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACE49B,KAAKA,CAACL,aAAa,EAAE;AACnB,IAAA,OAAO,IAAI,CAACj9B,OAAO,GAAGyrB,QAAQ,CAACE,aAAa,CAAC,IAAI,EAAEsR,aAAa,CAAC,GAAG,IAAI,CAAA;AAC1E,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEvQ,EAAAA,OAAOA,CAACuQ,aAAa,EAAEngC,IAAI,EAAE4C,IAAI,EAAE;AACjC,IAAA,IAAI,CAAC,IAAI,CAACM,OAAO,EAAE,OAAO,KAAK,CAAA;AAE/B,IAAA,MAAMu9B,OAAO,GAAGN,aAAa,CAAChT,OAAO,EAAE,CAAA;IACvC,MAAMuT,cAAc,GAAG,IAAI,CAACx0B,OAAO,CAACi0B,aAAa,CAACj6B,IAAI,EAAE;AAAEgtB,MAAAA,aAAa,EAAE,IAAA;AAAK,KAAC,CAAC,CAAA;IAChF,OACEwN,cAAc,CAACjR,OAAO,CAACzvB,IAAI,EAAE4C,IAAI,CAAC,IAAI69B,OAAO,IAAIA,OAAO,IAAIC,cAAc,CAACtB,KAAK,CAACp/B,IAAI,EAAE4C,IAAI,CAAC,CAAA;AAEhG,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEI,MAAMA,CAAC8N,KAAK,EAAE;AACZ,IAAA,OACE,IAAI,CAAC5N,OAAO,IACZ4N,KAAK,CAAC5N,OAAO,IACb,IAAI,CAACiqB,OAAO,EAAE,KAAKrc,KAAK,CAACqc,OAAO,EAAE,IAClC,IAAI,CAACjnB,IAAI,CAAClD,MAAM,CAAC8N,KAAK,CAAC5K,IAAI,CAAC,IAC5B,IAAI,CAACsE,GAAG,CAACxH,MAAM,CAAC8N,KAAK,CAACtG,GAAG,CAAC,CAAA;AAE9B,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEm2B,EAAAA,UAAUA,CAACp3B,OAAO,GAAG,EAAE,EAAE;AACvB,IAAA,IAAI,CAAC,IAAI,CAACrG,OAAO,EAAE,OAAO,IAAI,CAAA;AAC9B,IAAA,MAAMiF,IAAI,GAAGoB,OAAO,CAACpB,IAAI,IAAIgC,QAAQ,CAACgE,UAAU,CAAC,EAAE,EAAE;QAAEjI,IAAI,EAAE,IAAI,CAACA,IAAAA;AAAK,OAAC,CAAC;AACvE06B,MAAAA,OAAO,GAAGr3B,OAAO,CAACq3B,OAAO,GAAI,IAAI,GAAGz4B,IAAI,GAAG,CAACoB,OAAO,CAACq3B,OAAO,GAAGr3B,OAAO,CAACq3B,OAAO,GAAI,CAAC,CAAA;AACpF,IAAA,IAAI9iB,KAAK,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAA;AACtE,IAAA,IAAI9d,IAAI,GAAGuJ,OAAO,CAACvJ,IAAI,CAAA;IACvB,IAAI+Y,KAAK,CAACC,OAAO,CAACzP,OAAO,CAACvJ,IAAI,CAAC,EAAE;MAC/B8d,KAAK,GAAGvU,OAAO,CAACvJ,IAAI,CAAA;AACpBA,MAAAA,IAAI,GAAGoE,SAAS,CAAA;AAClB,KAAA;IACA,OAAO62B,YAAY,CAAC9yB,IAAI,EAAE,IAAI,CAACgE,IAAI,CAACy0B,OAAO,CAAC,EAAE;AAC5C,MAAA,GAAGr3B,OAAO;AACV0D,MAAAA,OAAO,EAAE,QAAQ;MACjB6Q,KAAK;AACL9d,MAAAA,IAAAA;AACF,KAAC,CAAC,CAAA;AACJ,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE6gC,EAAAA,kBAAkBA,CAACt3B,OAAO,GAAG,EAAE,EAAE;AAC/B,IAAA,IAAI,CAAC,IAAI,CAACrG,OAAO,EAAE,OAAO,IAAI,CAAA;AAE9B,IAAA,OAAO+3B,YAAY,CAAC1xB,OAAO,CAACpB,IAAI,IAAIgC,QAAQ,CAACgE,UAAU,CAAC,EAAE,EAAE;MAAEjI,IAAI,EAAE,IAAI,CAACA,IAAAA;KAAM,CAAC,EAAE,IAAI,EAAE;AACtF,MAAA,GAAGqD,OAAO;AACV0D,MAAAA,OAAO,EAAE,MAAM;AACf6Q,MAAAA,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC;AAClCod,MAAAA,SAAS,EAAE,IAAA;AACb,KAAC,CAAC,CAAA;AACJ,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACE,EAAA,OAAOvnB,GAAGA,CAAC,GAAGuc,SAAS,EAAE;IACvB,IAAI,CAACA,SAAS,CAAC4Q,KAAK,CAAC32B,QAAQ,CAAC+yB,UAAU,CAAC,EAAE;AACzC,MAAA,MAAM,IAAIj9B,oBAAoB,CAAC,yCAAyC,CAAC,CAAA;AAC3E,KAAA;AACA,IAAA,OAAOgZ,MAAM,CAACiX,SAAS,EAAGzqB,CAAC,IAAKA,CAAC,CAAC0nB,OAAO,EAAE,EAAEvmB,IAAI,CAAC+M,GAAG,CAAC,CAAA;AACxD,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACE,EAAA,OAAOC,GAAGA,CAAC,GAAGsc,SAAS,EAAE;IACvB,IAAI,CAACA,SAAS,CAAC4Q,KAAK,CAAC32B,QAAQ,CAAC+yB,UAAU,CAAC,EAAE;AACzC,MAAA,MAAM,IAAIj9B,oBAAoB,CAAC,yCAAyC,CAAC,CAAA;AAC3E,KAAA;AACA,IAAA,OAAOgZ,MAAM,CAACiX,SAAS,EAAGzqB,CAAC,IAAKA,CAAC,CAAC0nB,OAAO,EAAE,EAAEvmB,IAAI,CAACgN,GAAG,CAAC,CAAA;AACxD,GAAA;;AAEA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAOmtB,iBAAiBA,CAACnV,IAAI,EAAErL,GAAG,EAAEhX,OAAO,GAAG,EAAE,EAAE;IAChD,MAAM;AAAE7F,QAAAA,MAAM,GAAG,IAAI;AAAEgG,QAAAA,eAAe,GAAG,IAAA;AAAK,OAAC,GAAGH,OAAO;AACvDwzB,MAAAA,WAAW,GAAGl0B,MAAM,CAACwE,QAAQ,CAAC;QAC5B3J,MAAM;QACNgG,eAAe;AACf6D,QAAAA,WAAW,EAAE,IAAA;AACf,OAAC,CAAC,CAAA;AACJ,IAAA,OAAOmqB,iBAAiB,CAACqF,WAAW,EAAEnR,IAAI,EAAErL,GAAG,CAAC,CAAA;AAClD,GAAA;;AAEA;AACF;AACA;EACE,OAAOygB,iBAAiBA,CAACpV,IAAI,EAAErL,GAAG,EAAEhX,OAAO,GAAG,EAAE,EAAE;IAChD,OAAOY,QAAQ,CAAC42B,iBAAiB,CAACnV,IAAI,EAAErL,GAAG,EAAEhX,OAAO,CAAC,CAAA;AACvD,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO03B,iBAAiBA,CAAC1gB,GAAG,EAAEhX,OAAO,GAAG,EAAE,EAAE;IAC1C,MAAM;AAAE7F,QAAAA,MAAM,GAAG,IAAI;AAAEgG,QAAAA,eAAe,GAAG,IAAA;AAAK,OAAC,GAAGH,OAAO;AACvDwzB,MAAAA,WAAW,GAAGl0B,MAAM,CAACwE,QAAQ,CAAC;QAC5B3J,MAAM;QACNgG,eAAe;AACf6D,QAAAA,WAAW,EAAE,IAAA;AACf,OAAC,CAAC,CAAA;AACJ,IAAA,OAAO,IAAIgqB,WAAW,CAACwF,WAAW,EAAExc,GAAG,CAAC,CAAA;AAC1C,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO2gB,gBAAgBA,CAACtV,IAAI,EAAEuV,YAAY,EAAEv+B,IAAI,GAAG,EAAE,EAAE;IACrD,IAAIiD,WAAW,CAAC+lB,IAAI,CAAC,IAAI/lB,WAAW,CAACs7B,YAAY,CAAC,EAAE;AAClD,MAAA,MAAM,IAAIlhC,oBAAoB,CAC5B,+DACF,CAAC,CAAA;AACH,KAAA;IACA,MAAM;AAAEyD,QAAAA,MAAM,GAAG,IAAI;AAAEgG,QAAAA,eAAe,GAAG,IAAA;AAAK,OAAC,GAAG9G,IAAI;AACpDm6B,MAAAA,WAAW,GAAGl0B,MAAM,CAACwE,QAAQ,CAAC;QAC5B3J,MAAM;QACNgG,eAAe;AACf6D,QAAAA,WAAW,EAAE,IAAA;AACf,OAAC,CAAC,CAAA;IAEJ,IAAI,CAACwvB,WAAW,CAAC/5B,MAAM,CAACm+B,YAAY,CAACz9B,MAAM,CAAC,EAAE;AAC5C,MAAA,MAAM,IAAIzD,oBAAoB,CAC3B,CAAA,yCAAA,EAA2C88B,WAAY,CAAA,EAAA,CAAG,GACxD,CAAA,sCAAA,EAAwCoE,YAAY,CAACz9B,MAAO,CAAA,CACjE,CAAC,CAAA;AACH,KAAA;IAEA,MAAM;MAAEgkB,MAAM;MAAExhB,IAAI;MAAEywB,cAAc;AAAEzJ,MAAAA,aAAAA;AAAc,KAAC,GAAGiU,YAAY,CAACzJ,iBAAiB,CAAC9L,IAAI,CAAC,CAAA;AAE5F,IAAA,IAAIsB,aAAa,EAAE;AACjB,MAAA,OAAO/iB,QAAQ,CAACihB,OAAO,CAAC8B,aAAa,CAAC,CAAA;AACxC,KAAC,MAAM;AACL,MAAA,OAAOqM,mBAAmB,CACxB7R,MAAM,EACNxhB,IAAI,EACJtD,IAAI,EACH,CAASu+B,OAAAA,EAAAA,YAAY,CAACr+B,MAAO,CAAA,CAAC,EAC/B8oB,IAAI,EACJ+K,cACF,CAAC,CAAA;AACH,KAAA;AACF,GAAA;;AAEA;;AAEA;AACF;AACA;AACA;EACE,WAAWr2B,UAAUA,GAAG;IACtB,OAAO6e,UAAkB,CAAA;AAC3B,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWze,QAAQA,GAAG;IACpB,OAAOye,QAAgB,CAAA;AACzB,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWxe,qBAAqBA,GAAG;IACjC,OAAOwe,qBAA6B,CAAA;AACtC,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWte,SAASA,GAAG;IACrB,OAAOse,SAAiB,CAAA;AAC1B,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWre,SAASA,GAAG;IACrB,OAAOqe,SAAiB,CAAA;AAC1B,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWpe,WAAWA,GAAG;IACvB,OAAOoe,WAAmB,CAAA;AAC5B,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWje,iBAAiBA,GAAG;IAC7B,OAAOie,iBAAyB,CAAA;AAClC,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAW/d,sBAAsBA,GAAG;IAClC,OAAO+d,sBAA8B,CAAA;AACvC,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAW7d,qBAAqBA,GAAG;IACjC,OAAO6d,qBAA6B,CAAA;AACtC,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAW5d,cAAcA,GAAG;IAC1B,OAAO4d,cAAsB,CAAA;AAC/B,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAW1d,oBAAoBA,GAAG;IAChC,OAAO0d,oBAA4B,CAAA;AACrC,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWzd,yBAAyBA,GAAG;IACrC,OAAOyd,yBAAiC,CAAA;AAC1C,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWxd,wBAAwBA,GAAG;IACpC,OAAOwd,wBAAgC,CAAA;AACzC,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWvd,cAAcA,GAAG;IAC1B,OAAOud,cAAsB,CAAA;AAC/B,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWtd,2BAA2BA,GAAG;IACvC,OAAOsd,2BAAmC,CAAA;AAC5C,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWrd,YAAYA,GAAG;IACxB,OAAOqd,YAAoB,CAAA;AAC7B,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWpd,yBAAyBA,GAAG;IACrC,OAAOod,yBAAiC,CAAA;AAC1C,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWnd,yBAAyBA,GAAG;IACrC,OAAOmd,yBAAiC,CAAA;AAC1C,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWld,aAAaA,GAAG;IACzB,OAAOkd,aAAqB,CAAA;AAC9B,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWjd,0BAA0BA,GAAG;IACtC,OAAOid,0BAAkC,CAAA;AAC3C,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAWhd,aAAaA,GAAG;IACzB,OAAOgd,aAAqB,CAAA;AAC9B,GAAA;;AAEA;AACF;AACA;AACA;EACE,WAAW/c,0BAA0BA,GAAG;IACtC,OAAO+c,0BAAkC,CAAA;AAC3C,GAAA;AACF,CAAA;;AAEA;AACA;AACA;AACO,SAAS4P,gBAAgBA,CAACqS,WAAW,EAAE;AAC5C,EAAA,IAAIj3B,QAAQ,CAAC+yB,UAAU,CAACkE,WAAW,CAAC,EAAE;AACpC,IAAA,OAAOA,WAAW,CAAA;AACpB,GAAC,MAAM,IAAIA,WAAW,IAAIA,WAAW,CAACjU,OAAO,IAAIvb,QAAQ,CAACwvB,WAAW,CAACjU,OAAO,EAAE,CAAC,EAAE;AAChF,IAAA,OAAOhjB,QAAQ,CAACwxB,UAAU,CAACyF,WAAW,CAAC,CAAA;GACxC,MAAM,IAAIA,WAAW,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;AACzD,IAAA,OAAOj3B,QAAQ,CAACgE,UAAU,CAACizB,WAAW,CAAC,CAAA;AACzC,GAAC,MAAM;IACL,MAAM,IAAInhC,oBAAoB,CAC3B,CAAA,2BAAA,EAA6BmhC,WAAY,CAAY,UAAA,EAAA,OAAOA,WAAY,CAAA,CAC3E,CAAC,CAAA;AACH,GAAA;AACF;;AC/hFMC,MAAAA,OAAO,GAAG;;;;;;;;;;;;;;"}