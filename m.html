<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ollama Local Chat with Memory</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/luxon@3.4.4/build/global/luxon.min.js"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800 flex items-center justify-center min-h-screen">

    <div class="w-full max-w-2xl p-4 sm:p-6 bg-white rounded-xl shadow-lg border border-gray-200">
        
        <!-- Header with clear history button -->
        <div class="flex justify-between items-center mb-4">
            <h1 class="text-2xl font-bold text-gray-900">Ollama Chat</h1>
            <button id="clearButton" class="text-sm text-gray-500 hover:text-red-500 transition-colors duration-200">
                Clear History
            </button>
        </div>

        <!-- Connection Status Indicator -->
        <div id="connectionStatus" class="mb-4 p-3 text-sm rounded-lg" style="display:none;">
            <!-- Status message will be injected here -->
        </div>

        <!-- Chat Area -->
        <div id="chatArea" class="h-96 overflow-y-auto p-4 mb-4 border rounded-xl bg-gray-50 shadow-inner">
            <!-- Chat messages will be dynamically added here -->
        </div>

        <!-- Input and Send Button -->
        <div class="flex items-center">
            <input 
                type="text" 
                id="userInput" 
                class="flex-grow p-3 text-gray-700 border border-gray-300 rounded-l-xl focus:outline-none focus:ring-2 focus:ring-blue-500" 
                placeholder="Type your message..."
            >
            <button 
                id="sendButton" 
                class="p-3 bg-blue-600 text-white rounded-r-xl font-medium hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
                Send
            </button>
        </div>
    </div>

    <script>
        // Browser-compatible version of the DateTime Tool using Luxon
        const { DateTime } = luxon;

        /**
         * Date/Time Tool for LLM/Agent using Luxon
         */
        class DateTimeTool {
            constructor() {
                this.name = 'datetime_tool';
                this.description = 'Provides current date and time, and can perform date/time calculations and conversions.';
            }

            getCurrentDateTime({ location } = {}) {
                try {
                    let dt;
                    if (location) {
                        dt = DateTime.now().setZone(location);
                    } else {
                        dt = DateTime.now();
                    }
                    return dt.toFormat('MMMM d, yyyy h:mm:ss a ZZZZ');
                } catch (error) {
                    return `Error: Invalid location: ${location}. Please specify a valid IANA time zone (e.g., 'America/New_York') or a city.`;
                }
            }

            convertToTimeZone({ dateTimeStr, targetTimeZone }) {
                try {
                    const dt = DateTime.fromISO(dateTimeStr).setZone(targetTimeZone);
                    if (!dt.isValid) {
                        throw new Error(dt.invalidReason || "Invalid date/time or timezone");
                    }
                    return dt.toFormat('MMMM d, yyyy h:mm:ss a ZZZZ');
                } catch (error) {
                    return `Error: Could not convert ${dateTimeStr} to time zone ${targetTimeZone}. ${error.message}`;
                }
            }

            formatDateTime({ dateTimeStr, format }) {
                try {
                    const dt = DateTime.fromISO(dateTimeStr);
                    if (!dt.isValid) {
                        throw new Error(dt.invalidReason || "Invalid date/time string");
                    }
                    return dt.toFormat(format);
                } catch (error) {
                    return `Error: Invalid date/time string or format: ${dateTimeStr}, ${format}. ${error.message}`;
                }
            }

            dateTimeDifference({ startDateTimeStr, endDateTimeStr, unit = 'days' }) {
                try {
                    const startDt = DateTime.fromISO(startDateTimeStr);
                    const endDt = DateTime.fromISO(endDateTimeStr);

                    if (!startDt.isValid) {
                        throw new Error(`Invalid start date/time: ${startDateTimeStr}`);
                    }
                    if (!endDt.isValid) {
                        throw new Error(`Invalid end date/time: ${endDateTimeStr}`);
                    }

                    const diff = endDt.diff(startDt, unit);
                    const value = diff.toObject()[unit];

                    if (value === undefined) {
                        throw new Error(`Invalid unit: ${unit}`);
                    }

                    return `The difference is ${value} ${unit}.`;
                } catch (error) {
                    return `Error calculating difference: ${error.message}`;
                }
            }

            addTimeToDateTime({ dateTimeStr, amount, unit }) {
                try {
                    const dt = DateTime.fromISO(dateTimeStr).plus({ [unit]: amount });
                    if (!dt.isValid) {
                        throw new Error(dt.invalidReason || "Invalid date/time or calculation result");
                    }
                    return dt.toFormat('MMMM d, yyyy h:mm:ss a ZZZZ');
                } catch (error) {
                    return `Error: Could not add ${amount} ${unit} to ${dateTimeStr}. ${error.message}`;
                }
            }

            subtractTimeFromDateTime({ dateTimeStr, amount, unit }) {
                try {
                    const dt = DateTime.fromISO(dateTimeStr).minus({ [unit]: amount });
                    if (!dt.isValid) {
                        throw new Error(dt.invalidReason || "Invalid date/time or calculation result");
                    }
                    return dt.toFormat('MMMM d, yyyy h:mm:ss a ZZZZ');
                } catch (error) {
                    return `Error: Could not subtract ${amount} ${unit} from ${dateTimeStr}. ${error.message}`;
                }
            }
        }

        const dateTimeToolInstance = new DateTimeTool();

        // Tool object with call method
        const dateTimeTool = {
            name: 'datetime_tool',
            description: dateTimeToolInstance.description,
            call: async (params) => {
                console.log(`🕒 [DATETIME TOOL] Executing datetime_tool with params:`, params);
                const { action, ...actionParams } = params;

                try {
                    switch (action) {
                        case 'getCurrentDateTime':
                            return dateTimeToolInstance.getCurrentDateTime(actionParams);
                        case 'convertToTimeZone':
                            if (!actionParams.dateTimeStr || !actionParams.targetTimeZone) {
                                return "Error: convertToTimeZone requires 'dateTimeStr' and 'targetTimeZone' parameters.";
                            }
                            return dateTimeToolInstance.convertToTimeZone(actionParams);
                        case 'formatDateTime':
                            if (!actionParams.dateTimeStr || !actionParams.format) {
                                return "Error: formatDateTime requires 'dateTimeStr' and 'format' parameters.";
                            }
                            return dateTimeToolInstance.formatDateTime(actionParams);
                        case 'dateTimeDifference':
                            if (!actionParams.startDateTimeStr || !actionParams.endDateTimeStr) {
                                return "Error: dateTimeDifference requires 'startDateTimeStr' and 'endDateTimeStr' parameters.";
                            }
                            return dateTimeToolInstance.dateTimeDifference(actionParams);
                        case 'addTimeToDateTime':
                            if (!actionParams.dateTimeStr || actionParams.amount === undefined || !actionParams.unit) {
                                return "Error: addTimeToDateTime requires 'dateTimeStr', 'amount', and 'unit' parameters.";
                            }
                            return dateTimeToolInstance.addTimeToDateTime(actionParams);
                        case 'subtractTimeFromDateTime':
                            if (!actionParams.dateTimeStr || actionParams.amount === undefined || !actionParams.unit) {
                                return "Error: subtractTimeFromDateTime requires 'dateTimeStr', 'amount', and 'unit' parameters.";
                            }
                            return dateTimeToolInstance.subtractTimeFromDateTime(actionParams);
                        default:
                            return `Error: Unknown action '${action}'. Please use one of the defined actions.`;
                    }
                } catch (error) {
                    console.error(`🕒 [DATETIME TOOL] Unexpected error during execution:`, error);
                    return `An unexpected error occurred in the datetime tool: ${error.message}`;
                }
            }
        };

        // Constants for DOM elements
        const chatArea = document.getElementById('chatArea');
        const userInput = document.getElementById('userInput');
        const sendButton = document.getElementById('sendButton');
        const connectionStatus = document.getElementById('connectionStatus');
        const clearButton = document.getElementById('clearButton');

        // Constant for local storage key
        const STORAGE_KEY = 'ollama-chat-history';

        // Load conversation history from local storage
        let conversationHistory = loadHistory();

        // Helper function to load history from localStorage
        function loadHistory() {
            try {
                const history = localStorage.getItem(STORAGE_KEY);
                return history ? JSON.parse(history) : [];
            } catch (error) {
                console.error("Failed to load history from localStorage:", error);
                return [];
            }
        }

        // Helper function to save history to localStorage
        function saveHistory() {
            try {
                localStorage.setItem(STORAGE_KEY, JSON.stringify(conversationHistory));
            } catch (error) {
                console.error("Failed to save history to localStorage:", error);
            }
        }

        // Renders the messages from the history array to the chat area
        function renderHistory() {
            chatArea.innerHTML = '';
            conversationHistory.forEach(msg => {
                if (msg.role === 'user') {
                    appendMessage('You', msg.content, 'text-blue-600');
                } else if (msg.role === 'assistant') {
                    appendMessage('Model', msg.content, 'text-green-600');
                }
            });
            chatArea.scrollTop = chatArea.scrollHeight;
        }

        // Clears the history from both memory and local storage
        function clearHistory() {
            conversationHistory = [];
            saveHistory();
            renderHistory();
        }

        // Check the Ollama connection status
        async function checkOllamaConnection() {
            connectionStatus.style.display = 'block';
            connectionStatus.textContent = 'Checking connection to Ollama...';
            connectionStatus.className = 'mb-4 p-3 text-sm rounded-lg bg-yellow-100 text-yellow-800';

            try {
                const response = await fetch('http://localhost:11434/api/version', { method: 'GET' });
                if (response.ok) {
                    const version = await response.json();
                    connectionStatus.textContent = `Connected to Ollama (Version: ${version.version})`;
                    connectionStatus.className = 'mb-4 p-3 text-sm rounded-lg bg-green-100 text-green-800';
                    return true;
                } else {
                    throw new Error('Connection failed');
                }
            } catch (error) {
                connectionStatus.textContent = 'Unable to connect to Ollama. Please check your setup.';
                connectionStatus.className = 'mb-4 p-3 text-sm rounded-lg bg-red-100 text-red-800';
                return false;
            }
        }

        // Advanced tool integration using the full datetime tool
        async function handleToolRequests(message) {
            const lowerMessage = message.toLowerCase();

            try {
                // Current time queries
                if (lowerMessage.includes('what time is it') || lowerMessage.includes('current time')) {
                    if (lowerMessage.includes('tokyo') || lowerMessage.includes('japan')) {
                        return await dateTimeTool.call({ action: 'getCurrentDateTime', location: 'Asia/Tokyo' });
                    } else if (lowerMessage.includes('london') || lowerMessage.includes('uk')) {
                        return await dateTimeTool.call({ action: 'getCurrentDateTime', location: 'Europe/London' });
                    } else if (lowerMessage.includes('new york') || lowerMessage.includes('ny')) {
                        return await dateTimeTool.call({ action: 'getCurrentDateTime', location: 'America/New_York' });
                    } else if (lowerMessage.includes('los angeles') || lowerMessage.includes('la')) {
                        return await dateTimeTool.call({ action: 'getCurrentDateTime', location: 'America/Los_Angeles' });
                    } else {
                        return await dateTimeTool.call({ action: 'getCurrentDateTime' });
                    }
                }

                // Date arithmetic queries
                if (lowerMessage.includes('add') && (lowerMessage.includes('day') || lowerMessage.includes('hour') || lowerMessage.includes('minute'))) {
                    // Example: "add 5 days to today"
                    const now = new Date().toISOString();
                    const amount = parseInt(lowerMessage.match(/\d+/)?.[0] || '1');
                    let unit = 'days';
                    if (lowerMessage.includes('hour')) unit = 'hours';
                    if (lowerMessage.includes('minute')) unit = 'minutes';
                    if (lowerMessage.includes('week')) unit = 'weeks';
                    if (lowerMessage.includes('month')) unit = 'months';
                    if (lowerMessage.includes('year')) unit = 'years';

                    return await dateTimeTool.call({
                        action: 'addTimeToDateTime',
                        dateTimeStr: now,
                        amount: amount,
                        unit: unit
                    });
                }

                // Time zone conversion
                if (lowerMessage.includes('convert') && lowerMessage.includes('to')) {
                    const now = new Date().toISOString();
                    let targetZone = 'UTC';
                    if (lowerMessage.includes('est') || lowerMessage.includes('eastern')) targetZone = 'America/New_York';
                    if (lowerMessage.includes('pst') || lowerMessage.includes('pacific')) targetZone = 'America/Los_Angeles';
                    if (lowerMessage.includes('gmt') || lowerMessage.includes('utc')) targetZone = 'UTC';
                    if (lowerMessage.includes('tokyo')) targetZone = 'Asia/Tokyo';
                    if (lowerMessage.includes('london')) targetZone = 'Europe/London';

                    return await dateTimeTool.call({
                        action: 'convertToTimeZone',
                        dateTimeStr: now,
                        targetTimeZone: targetZone
                    });
                }

            } catch (error) {
                return `Tool error: ${error.message}`;
            }

            return null;
        }

        // Main function to handle message sending and streaming
        async function sendMessage() {
            const message = userInput.value.trim();
            if (!message) return;

            // Check for tool requests first
            const toolResponse = await handleToolRequests(message);
            if (toolResponse) {
                appendMessage('You', message, 'text-blue-600');
                appendMessage('DateTime Tool', toolResponse, 'text-purple-600');
                conversationHistory.push({ role: 'user', content: message });
                conversationHistory.push({ role: 'assistant', content: `DateTime Tool: ${toolResponse}` });
                saveHistory();
                userInput.value = '';
                return;
            }

            // Check connection before sending
            const isConnected = await checkOllamaConnection();
            if (!isConnected) return;

            // Add user message to chat and history
            appendMessage('You', message, 'text-blue-600');
            conversationHistory.push({ role: 'user', content: message });
            saveHistory();

            // Clear input
            userInput.value = '';

            try {
                const response = await fetch('http://localhost:11434/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        model: 'gemma3:1b',  
                        messages: conversationHistory,
                        stream: true
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // Stream the response
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let fullModelResponse = '';
                const modelMessageElement = appendMessage('Model', '', 'text-green-600');

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n').filter(line => line.trim() !== '');

                    lines.forEach(line => {
                        try {
                            const parsedChunk = JSON.parse(line);
                            if (parsedChunk.message && parsedChunk.message.content) {
                                fullModelResponse += parsedChunk.message.content;
                                modelMessageElement.textContent = `Model: ${fullModelResponse}`;
                                chatArea.scrollTop = chatArea.scrollHeight;
                            }
                        } catch (parseError) {
                            console.error('Parsing error:', parseError);
                        }
                    });
                }
                
                // Add final response to conversation history and save
                conversationHistory.push({ role: 'assistant', content: fullModelResponse });
                saveHistory();

            } catch (error) {
                console.error('Error:', error);
                appendMessage('System', `Error communicating with the model: ${error.message}`, 'text-red-600');
            }
        }

        // Helper function to display messages in the chat area
        function appendMessage(sender, message, colorClass) {
            const messageElement = document.createElement('div');
            messageElement.innerHTML = `
                <strong class="${colorClass}">${sender}:</strong> 
                <span>${message}</span>
            `;
            messageElement.className = 'mb-2 p-3 bg-gray-100 rounded-lg shadow-sm';
            chatArea.appendChild(messageElement);
            chatArea.scrollTop = chatArea.scrollHeight;
            return messageElement.querySelector('span'); // Return the span element to update its text content
        }

        // Initial setup on page load
        window.onload = function() {
            checkOllamaConnection();
            renderHistory();
        };

        // Event Listeners
        sendButton.addEventListener('click', sendMessage);
        userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        clearButton.addEventListener('click', clearHistory);
    </script>
</body>
</html>
