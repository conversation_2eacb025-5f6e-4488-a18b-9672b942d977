<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ollama Local Chat with Memory</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800 flex items-center justify-center min-h-screen">

    <div class="w-full max-w-2xl p-4 sm:p-6 bg-white rounded-xl shadow-lg border border-gray-200">
        
        <!-- Header with clear history button -->
        <div class="flex justify-between items-center mb-4">
            <h1 class="text-2xl font-bold text-gray-900">Ollama Chat</h1>
            <button id="clearButton" class="text-sm text-gray-500 hover:text-red-500 transition-colors duration-200">
                Clear History
            </button>
        </div>

        <!-- Connection Status Indicator -->
        <div id="connectionStatus" class="mb-4 p-3 text-sm rounded-lg" style="display:none;">
            <!-- Status message will be injected here -->
        </div>

        <!-- Chat Area -->
        <div id="chatArea" class="h-96 overflow-y-auto p-4 mb-4 border rounded-xl bg-gray-50 shadow-inner">
            <!-- Chat messages will be dynamically added here -->
        </div>

        <!-- Input and Send Button -->
        <div class="flex items-center">
            <input 
                type="text" 
                id="userInput" 
                class="flex-grow p-3 text-gray-700 border border-gray-300 rounded-l-xl focus:outline-none focus:ring-2 focus:ring-blue-500" 
                placeholder="Type your message..."
            >
            <button 
                id="sendButton" 
                class="p-3 bg-blue-600 text-white rounded-r-xl font-medium hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
                Send
            </button>
        </div>
    </div>

    <script type="module">
        import { dateTimeTool } from './date-time-tool.js';

        // Constants for DOM elements
        const chatArea = document.getElementById('chatArea');
        const userInput = document.getElementById('userInput');
        const sendButton = document.getElementById('sendButton');
        const connectionStatus = document.getElementById('connectionStatus');
        const clearButton = document.getElementById('clearButton');

        // Constant for local storage key
        const STORAGE_KEY = 'ollama-chat-history';

        // Load conversation history from local storage
        let conversationHistory = loadHistory();

        // Helper function to load history from localStorage
        function loadHistory() {
            try {
                const history = localStorage.getItem(STORAGE_KEY);
                return history ? JSON.parse(history) : [];
            } catch (error) {
                console.error("Failed to load history from localStorage:", error);
                return [];
            }
        }

        // Helper function to save history to localStorage
        function saveHistory() {
            try {
                localStorage.setItem(STORAGE_KEY, JSON.stringify(conversationHistory));
            } catch (error) {
                console.error("Failed to save history to localStorage:", error);
            }
        }

        // Renders the messages from the history array to the chat area
        function renderHistory() {
            chatArea.innerHTML = '';
            conversationHistory.forEach(msg => {
                if (msg.role === 'user') {
                    appendMessage('You', msg.content, 'text-blue-600');
                } else if (msg.role === 'assistant') {
                    appendMessage('Model', msg.content, 'text-green-600');
                }
            });
            chatArea.scrollTop = chatArea.scrollHeight;
        }

        // Clears the history from both memory and local storage
        function clearHistory() {
            conversationHistory = [];
            saveHistory();
            renderHistory();
        }

        // Check the Ollama connection status
        async function checkOllamaConnection() {
            connectionStatus.style.display = 'block';
            connectionStatus.textContent = 'Checking connection to Ollama...';
            connectionStatus.className = 'mb-4 p-3 text-sm rounded-lg bg-yellow-100 text-yellow-800';

            try {
                const response = await fetch('http://localhost:11434/api/version', { method: 'GET' });
                if (response.ok) {
                    const version = await response.json();
                    connectionStatus.textContent = `Connected to Ollama (Version: ${version.version})`;
                    connectionStatus.className = 'mb-4 p-3 text-sm rounded-lg bg-green-100 text-green-800';
                    return true;
                } else {
                    throw new Error('Connection failed');
                }
            } catch (error) {
                connectionStatus.textContent = 'Unable to connect to Ollama. Please check your setup.';
                connectionStatus.className = 'mb-4 p-3 text-sm rounded-lg bg-red-100 text-red-800';
                return false;
            }
        }

        // Tool usage instructions for the agent
        const toolInstructions = `
        Available Tools:

        1. **datetime_tool** - For all date/time operations:
           - Use "getCurrentDateTime" to get current time (optionally for specific timezone)
           - Use "convertToTimeZone" to convert dates between timezones
           - Use "formatDateTime" to format dates in specific formats
           - Use "dateTimeDifference" to calculate time differences
           - Use "addTimeToDateTime" / "subtractTimeFromDateTime" for date arithmetic

        Examples:
        - "What time is it?" → getCurrentDateTime
        - "What time is it in Tokyo?" → getCurrentDateTime with location: "Asia/Tokyo"
        - "Convert 2024-01-15T10:00:00Z to EST" → convertToTimeZone
        - "How many days until Christmas?" → dateTimeDifference
        `;

        // Include in your agent's system prompt
        const systemPrompt = `You are a helpful assistant with access to tools. ${toolInstructions}`;

        // Add to your existing tools array
        const availableTools = [
            dateTimeTool,
            // ... other tools
        ];

        // Main function to handle message sending and streaming
        async function sendMessage() {
            const message = userInput.value.trim();
            if (!message) return;

            // Check connection before sending
            const isConnected = await checkOllamaConnection();
            if (!isConnected) return;

            // Add user message to chat and history
            appendMessage('You', message, 'text-blue-600');
            conversationHistory.push({ role: 'user', content: message });
            saveHistory();

            // Clear input
            userInput.value = '';

            try {
                const response = await fetch('http://localhost:11434/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        model: 'gemma3:1b',  
                        messages: conversationHistory,
                        stream: true
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // Stream the response
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let fullModelResponse = '';
                const modelMessageElement = appendMessage('Model', '', 'text-green-600');

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n').filter(line => line.trim() !== '');

                    lines.forEach(line => {
                        try {
                            const parsedChunk = JSON.parse(line);
                            if (parsedChunk.message && parsedChunk.message.content) {
                                fullModelResponse += parsedChunk.message.content;
                                modelMessageElement.textContent = `Model: ${fullModelResponse}`;
                                chatArea.scrollTop = chatArea.scrollHeight;
                            }
                        } catch (parseError) {
                            console.error('Parsing error:', parseError);
                        }
                    });
                }
                
                // Add final response to conversation history and save
                conversationHistory.push({ role: 'assistant', content: fullModelResponse });
                saveHistory();

            } catch (error) {
                console.error('Error:', error);
                appendMessage('System', `Error communicating with the model: ${error.message}`, 'text-red-600');
            }
        }

        // Helper function to display messages in the chat area
        function appendMessage(sender, message, colorClass) {
            const messageElement = document.createElement('div');
            messageElement.innerHTML = `
                <strong class="${colorClass}">${sender}:</strong> 
                <span>${message}</span>
            `;
            messageElement.className = 'mb-2 p-3 bg-gray-100 rounded-lg shadow-sm';
            chatArea.appendChild(messageElement);
            chatArea.scrollTop = chatArea.scrollHeight;
            return messageElement.querySelector('span'); // Return the span element to update its text content
        }

        // Initial setup on page load
        window.onload = function() {
            checkOllamaConnection();
            renderHistory();
        };

        // Event Listeners
        sendButton.addEventListener('click', sendMessage);
        userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        clearButton.addEventListener('click', clearHistory);
    </script>
</body>
</html>
